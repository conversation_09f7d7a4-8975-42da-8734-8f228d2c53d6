package com.sgs.customerbiz.validation.service.formatter;

import com.google.common.collect.ImmutableList;
import com.sgs.customerbiz.validation.service.ValidationResult;
import org.apache.commons.lang3.text.StrSubstitutor;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component(MessageFormatter.MULTI_TYPE_PARAM_TABLE_FORMATTER)
public class MultiTypeParamTableMessageFormatter implements MessageFormatter{
    @Override
    public List<String> format(ValidationResult result) {
        Object messageTemplateObj = result.getParams().getOrDefault(MessageFormatter.OVERRIDE_TEMPLATE,result.getFieldRuleConfig().getRuleConfig().getMessageTemplate());
        if(messageTemplateObj == null) {
            throw new IllegalArgumentException("Message template can not be null" + result);
        }
        String messageTemplate = messageTemplateObj.toString();
        Map<String, Object> params = result.getParams();
        Object paramsTableColl = params.getOrDefault(MessageFormatter.MULTI_VALUE_PARAM_VALUE, Collections.emptyList());
        if(paramsTableColl instanceof Collection) {
            return ((Collection<Map<String, Object>>) paramsTableColl).stream()
                    .map(item -> {
                        StrSubstitutor formatter = new StrSubstitutor(item);
                        String template = item.getOrDefault(MessageFormatter.OVERRIDE_TEMPLATE, messageTemplate).toString();
                        return formatter.replace(template);
                    })
                    .collect(Collectors.toList());
        }
        StrSubstitutor formatter = new StrSubstitutor(result.getParams());
        return ImmutableList.of(formatter.replace(messageTemplateObj));
    }
}
