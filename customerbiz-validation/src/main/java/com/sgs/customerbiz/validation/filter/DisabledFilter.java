package com.sgs.customerbiz.validation.filter;

import com.googlecode.aviator.script.AviatorBindings;
import com.sgs.customerbiz.validation.props.ValidationProps;
import com.sgs.customerbiz.validation.service.FieldValidationConfig;
import com.sgs.customerbiz.validation.validator.ScriptEvaluator;
import org.springframework.stereotype.Component;

import javax.script.Bindings;
import java.util.Optional;

@Component
public class DisabledFilter implements ValidationFilter{

    private final ValidationProps props;

    public DisabledFilter(ValidationProps props) {
        this.props = props;
    }

    @Override
    public Optional<FieldValidationConfig> filter(FieldValidationConfig fieldValidationConfig, FilterContext ctx) {

        if(ScriptEvaluator.conditionEval(props.getGlobalsDisabled().getDisabledScript(), aviatorBindings(ctx))) {
            return Optional.empty();
        }
        return Optional.of(fieldValidationConfig);
    }

    public static void main(String[] args) {
        Bindings bindings = new AviatorBindings();
        bindings.put(MODE_PARAM_NAME, "mode");
        bindings.put(REF_SYSTEM_ID, 10020);
        bindings.put(SYSTEM_ID, 1);
        bindings.put(PRODUCT_LINE_CODE, null);
        System.out.println(ScriptEvaluator.conditionEval("productLineCode != 'HL'", bindings));
    }
}

