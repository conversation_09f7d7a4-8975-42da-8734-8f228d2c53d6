package com.sgs.customerbiz.validation.validator;

import com.google.common.collect.ImmutableSet;
import com.sgs.customerbiz.validation.enums.FieldType;
import com.sgs.customerbiz.validation.service.FieldRuleConfig;
import com.sgs.customerbiz.validation.service.FieldValidationConfig;
import com.sgs.customerbiz.validation.service.ValidationResult;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class Exists extends AbstractOkIfNullValueRule{

    public static final String VALUE_SET = "valueSet";
    public static final String DELIMITER = ",";

    @Override
    protected ValidationResult doValidateIfValueNotNull(Object value,
                                                        Map<String, Object> params,
                                                        FieldValidationConfig fieldValidationConfig,
                                                        FieldRuleConfig ruleConfig) {
        FieldType fieldType = fieldValidationConfig.getFieldType();
        if(fieldType != FieldType.Number && fieldType != FieldType.String) {
            throw new IllegalStateException("field type is not number or string");
        }
        Object strValues = params.get(VALUE_SET);
        if(strValues==null || value == null){
            return ValidationResult.ok(fieldValidationConfig, ruleConfig, value, params);
        }
        if(strValues instanceof String) {
            Set<String> dict = Stream.of(((String) strValues).split(DELIMITER)).collect(Collectors.toSet());
            if(value instanceof Collection) {
                Collection<Object> collection = (Collection<Object>) value;
                for(Object item : collection) {
                    if(item == null) {
                        continue;
                    }
                    ValidationResult itemResult = existsValue(item, params, fieldValidationConfig, ruleConfig, fieldType, dict);
                    if(itemResult.isFail()) {
                        return ValidationResult.fail(fieldValidationConfig, ruleConfig, value, params);
                    }
                }
                return ValidationResult.ok(fieldValidationConfig, ruleConfig, value, params);
            }
            return existsValue(value, params, fieldValidationConfig, ruleConfig, fieldType, dict);
        }
        throw new IllegalStateException("value is not String");
    }

    private static ValidationResult existsValue(Object value, Map<String, Object> params, FieldValidationConfig fieldValidationConfig, FieldRuleConfig ruleConfig, FieldType fieldType, Set<String> dict) {
        if(fieldType == FieldType.String) {
            return existsString((String)value, params, fieldValidationConfig, ruleConfig, fieldType, dict);
        }
        String valueToString = value.toString();
        return existsString(valueToString, params, fieldValidationConfig, ruleConfig, fieldType, dict);
    }

    private static ValidationResult existsString(String value, Map<String, Object> params, FieldValidationConfig fieldValidationConfig, FieldRuleConfig ruleConfig, FieldType fieldType, Set<String> dict) {
        return ValidationResult.of(dict.contains(value), fieldValidationConfig, ruleConfig, value, params);
    }

    @Override
    public Set<String> parameterNames() {
        return ImmutableSet.of(VALUE_SET);
    }
}
