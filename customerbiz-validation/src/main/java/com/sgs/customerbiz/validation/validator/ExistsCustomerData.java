package com.sgs.customerbiz.validation.validator;

import com.google.common.collect.ImmutableSet;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.validation.enums.FieldType;
import com.sgs.customerbiz.validation.service.FieldRuleConfig;
import com.sgs.customerbiz.validation.service.FieldValidationConfig;
import com.sgs.customerbiz.validation.service.ValidationResult;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
public class ExistsCustomerData extends AbstractOkIfNullValueRule{

    public static final String REF_SYSTEM_ID = "refSystemId";
    public static final String CUSTOMER_DATA_PATH = "customerDataPath";
    public static final String SLICE_PATTERN = "slicePattern";
    private final ConfigClient configClient;

    public ExistsCustomerData(ConfigClient configClient) {
        this.configClient = configClient;
    }

    /**
     * 从字符串开头移除所有匹配的模式（大小写不敏感）
     * @param sourceStr 源字符串
     * @param pattern 要移除的模式
     * @return 处理后的字符串
     */
    private static Object sliceByStarts(Object sourceStr, Object pattern) {
        if (sourceStr == null || pattern == null) {
            return null;
        }

        String source = String.valueOf(sourceStr);
        String patternStr = String.valueOf(pattern);

        if (StringUtils.isEmpty(source) || StringUtils.isEmpty(patternStr)) {
            return source;
        }

        String lowerSource = source.toLowerCase();
        String lowerPattern = patternStr.toLowerCase();
        int startIndex = 0;

        while (lowerSource.startsWith(lowerPattern, startIndex)) {
            startIndex += patternStr.length();
        }

        return startIndex > 0 ? source.substring(startIndex) : source;
    }

    @Override
    protected ValidationResult doValidateIfValueNotNull(Object value, Map<String, Object> params, FieldValidationConfig fieldValidationConfig, FieldRuleConfig ruleConfig) {
        FieldType fieldType = fieldValidationConfig.getFieldType();
        if(fieldType != FieldType.Number && fieldType != FieldType.String) {
            throw new IllegalStateException("field type is not number or string");
        }
        Integer refSystemId = (Integer)params.get(REF_SYSTEM_ID);
        String customerDataPath = (String) params.get(CUSTOMER_DATA_PATH);
        Object slicePattern = params.get(SLICE_PATTERN);
        Object data = configClient.getCustomerDataConfig(refSystemId, "$."+customerDataPath);
        if(data==null){
            return ValidationResult.ok(fieldValidationConfig, ruleConfig, value, params);
        }
        Set<String> dict = fromData(data);
        if(value instanceof Collection) {
            Collection<Object> collection = (Collection<Object>) value;
            for(Object item : collection) {
                if(item == null) {
                    continue;
                }
                // 使用sliceByStarts处理item，从params获取pattern
                Object processedItem = slicePattern != null ? sliceByStarts(item, slicePattern) : item;
                ValidationResult itemResult = existsValue(processedItem, params, fieldValidationConfig, ruleConfig, fieldType, dict);
                if(itemResult.isFail()) {
                    return ValidationResult.fail(fieldValidationConfig, ruleConfig, value, params);
                }
            }
            return ValidationResult.ok(fieldValidationConfig, ruleConfig, value, params);
        }
        // 使用sliceByStarts处理value，从params获取pattern
        Object processedValue = slicePattern != null ? sliceByStarts(value, slicePattern) : value;
        return existsValue(processedValue, params, fieldValidationConfig, ruleConfig, fieldType, dict);
    }

    private static @NotNull Set<String> fromData(Object data) {
        Set<String> dict = new HashSet<>();
        if(data instanceof Set) {
            dict = (Set<String>) data;
        }
        if(data instanceof Collection) {
            dict = ((Collection<String>) data).stream().collect(Collectors.toSet());
        }
        return dict;
    }

    private static ValidationResult existsValue(Object value, Map<String, Object> params, FieldValidationConfig fieldValidationConfig, FieldRuleConfig ruleConfig, FieldType fieldType, Set<String> dict) {
        if(fieldType == FieldType.String) {
            return existsString((String)value, params, fieldValidationConfig, ruleConfig, fieldType, dict);
        }
        String valueToString = value.toString();
        return existsString(valueToString, params, fieldValidationConfig, ruleConfig, fieldType, dict);
    }

    private static ValidationResult existsString(String value, Map<String, Object> params, FieldValidationConfig fieldValidationConfig, FieldRuleConfig ruleConfig, FieldType fieldType, Set<String> dict) {
        return ValidationResult.of(dict.contains(value), fieldValidationConfig, ruleConfig, value, params);
    }

    @Override
    public Set<String> parameterNames() {
        return ImmutableSet.of(
                REF_SYSTEM_ID,
                CUSTOMER_DATA_PATH
        );
    }
}
