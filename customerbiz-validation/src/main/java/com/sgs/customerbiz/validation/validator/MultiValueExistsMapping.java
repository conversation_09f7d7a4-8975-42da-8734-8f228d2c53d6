package com.sgs.customerbiz.validation.validator;

import com.alibaba.fastjson.*;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.model.trf.dto.TrfProductAttributeDTO;
import com.sgs.customerbiz.model.trf.dto.TrfTestSampleLimitGroupDTO;
import com.sgs.customerbiz.validation.enums.FieldType;
import com.sgs.customerbiz.validation.service.FieldRuleConfig;
import com.sgs.customerbiz.validation.service.FieldValidationConfig;
import com.sgs.customerbiz.validation.service.ValidationMessageService;
import com.sgs.customerbiz.validation.service.ValidationResult;
import com.sgs.customerbiz.validation.service.extractor.ObjectListExtractor;
import com.sgs.customerbiz.validation.service.formatter.MessageFormatter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MultiValueExistsMapping extends AbstractOkIfNullValueRule{

    public static final String REF_SYSTEM_ID = "refSystemId";
    public static final String CUSTOMER_DATA_PATH = "customerDataPath";
    public static final String CONFIGS = "configs";
    public static final String EXCLUDE = "EXCLUDE";
    public static final String INCLUDE = "INCLUDE";
    public static final String PASS_IF = "passIf";
    private final ConfigClient configClient;

    public MultiValueExistsMapping(ConfigClient configClient) {
        this.configClient = configClient;
    }

    public enum MatchCase {
        Eq,Like,NotLike
    }

    @Data
    public static class ItemParamSchema {
        private Object value;
        private FieldType type = FieldType.String;
        private boolean match = false;
        private MatchCase matchCase = MatchCase.Eq;
        private boolean keyAttr = false;
        private Map<String, ItemParamSchema> childSchemas = new HashMap<>();
        private boolean skipIfNull = false;

        public ItemParamSchema immutableSetValue(Object value) {
            ItemParamSchema newResult = new ItemParamSchema();
            newResult.setValue(value);
            newResult.setMatch(match);
            newResult.setMatchCase(matchCase);
            newResult.setType(type);
            newResult.setChildSchemas(childSchemas);
            newResult.setKeyAttr(keyAttr);
            newResult.setSkipIfNull(skipIfNull);
            return newResult;
        }
    }

    @Override
    protected ValidationResult doValidateIfValueNotNull(Object value, Map<String, Object> params, FieldValidationConfig fieldValidationConfig, FieldRuleConfig ruleConfig) {
        ValidationResult.ValidationResultContext ctx = ValidationResult.ctx(fieldValidationConfig, ruleConfig, value, params);
        Integer refSystemId = (Integer)params.get(REF_SYSTEM_ID);
        String customerDataPath = (String) params.get(CUSTOMER_DATA_PATH);
        Object data = configClient.getCustomerDataConfig(refSystemId, "$."+customerDataPath);
        if(data==null){
            return ctx.ok();
        }
        if(!(data instanceof JSONArray)) {
            throw new IllegalArgumentException("refSystemId:"+refSystemId+" customerDataPath:"+customerDataPath+" is not a JSONArray");
        }
        JSONArray dataSource = (JSONArray) data;
        Object configs = params.getOrDefault(CONFIGS, Collections.emptyList());
        if(!(configs instanceof Collection)) {
            throw new IllegalStateException("configs must be a collection");
        }
        Collection<Map<String, ItemParamSchema>> configList = JSON.parseObject(JSON.toJSONString(configs), new TypeReference<Collection<Map<String, ItemParamSchema>>>() {});


        List<Map<String, Object>> allIllegalData = configList.stream()
                .flatMap(config -> getIllegalData(value, config, dataSource).stream())
                .collect(Collectors.toList());

        params.put(ValidationMessageService.MESSAGE_FORMATTER, MessageFormatter.MULTI_VALUE_PARAM_TABLE_FORMATTER); // change formatter
        params.put(ValidationMessageService.OBJECT_LIST_EXTRACTOR, ObjectListExtractor.MULTI_VALUE_EXTRACTOR);

        if(CollectionUtils.isEmpty(allIllegalData)) {
            return ctx.ok();
        } else {
            params.put(MessageFormatter.MULTI_VALUE_PARAM_VALUE, allIllegalData);
            return ctx.fail();
        }
    }

    private @NotNull List<Map<String, Object>> getIllegalData(Object value,
                                                              Map<String, ItemParamSchema> paramSchemaMap,
                                                              JSONArray dataSource) {

        List<Map<String, Object>> illegalData = flatConvert(paramSchemaMap, listValue(value)).stream()
                .flatMap(configInstance -> generateCombinations(configInstance).stream())
                .filter(flatValues -> {
                    // 检查skipIfNull为true的属性值是否为空
                    boolean shouldSkip = flatValues.values().stream()
                            .anyMatch(schema -> schema.isSkipIfNull() && Objects.isNull(schema.getValue()));
                    return !shouldSkip;
                })
                .filter(flatValues -> {
                    if(passIfExcluded(flatValues)) {
                        return isIllegalByExcluded(dataSource, flatValues);
                    } else {
                        return isIllegalByInclude(dataSource, flatValues);
                    }
                })
                .map(flatValues -> {
                    Map<String, Object> aMap = new HashMap<>();
                    flatValues.forEach((k,v) ->{
                        aMap.put(k, v.getValue());
                    });
                    return aMap;
                })
                .collect(Collectors.toList());
        return illegalData;
    }

    private boolean isIllegalByInclude(JSONArray dataSource, Map<String, ItemParamSchema> flatValues) {
        // 获取所有标记为keyAttr的属性
        List<String> keyAttrs = flatValues.entrySet()
                .stream()
                .filter(entry -> entry.getValue().isKeyAttr())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
                
        if (keyAttrs.isEmpty()) {
            throw new IllegalArgumentException("config not contains any keyAttr=true");
        }

        // 按多个key进行分组，使用KeyCombination确保顺序无关性
        Map<KeyCombination, List<JSONObject>> groupByKeys = dataSource.stream()
                .map(obj -> (JSONObject) obj)
                .collect(Collectors.groupingBy(obj -> 
                    new KeyCombination(obj, keyAttrs)
                ));

        // 先检查key是否存在匹配
        boolean hasMatchingKey = false;
        for (Map.Entry<KeyCombination, List<JSONObject>> entry : groupByKeys.entrySet()) {
            KeyCombination key = entry.getKey();
            if (keyAttrs.stream().allMatch(attr ->
                    match(key.getKeyValues().get(attr), flatValues.get(attr)))) {
                hasMatchingKey = true;
                // 检查匹配记录的其他属性
                for (JSONObject record : entry.getValue()) {
                    if (checkRemainingAttributes(record, flatValues)) {
                        return false;
                    }
                }
            }
        }

        return hasMatchingKey;
    }

    private boolean checkRemainingAttributes(JSONObject record, Map<String, ItemParamSchema> flatValues) {
        return flatValues.entrySet().stream()
                .filter(entry -> entry.getValue().isMatch() && !entry.getValue().isKeyAttr())
                .allMatch(entry -> {
                    ItemParamSchema value = entry.getValue();

                    return (value.isSkipIfNull() && Objects.isNull(value.getValue()))  || (match(record, entry.getKey(), entry.getValue()));
                });
    }

    private boolean isIllegalByExcluded(JSONArray dataSource, Map<String, ItemParamSchema> flatValues) {
        for (int i = 0; i < dataSource.size(); i++) {
            JSONObject data = dataSource.getJSONObject(i);
            boolean exists = flatValues.entrySet().stream()
                    .filter(entry -> entry.getValue().isMatch())
                    .allMatch(entry -> {
                        String attrName = entry.getKey();
                        ItemParamSchema schema = entry.getValue();
                        return match(data, attrName, schema);
                    });
            if(exists) {
                return true;
            }
        }
        return false;
    }

    private static boolean passIfExcluded(Map<String, ItemParamSchema> flatValues) {
        Object passIfObj = Optional.ofNullable(flatValues.get(PASS_IF)).map(ItemParamSchema::getValue).orElse(EXCLUDE);
        return passIfObj.toString().equalsIgnoreCase(EXCLUDE);
    }

    private @NotNull Boolean match(JSONObject data, String attrName, ItemParamSchema schema) {
        Object valueOfSource = data.get(attrName);
        return match(valueOfSource, schema);
    }

    private @NotNull Boolean match(Object valueOfSource, ItemParamSchema schema) {
        Object valueOfSchema = schema.getValue();
        switch (schema.matchCase) {
            case Eq:
                return eq(schema, valueOfSource, valueOfSchema);
            case Like:
                return like(valueOfSchema, valueOfSource);
            case NotLike:
                return notLike(valueOfSchema, valueOfSource);
            default:
                return false;
        }
    }

    private @NotNull Boolean notLike(Object valueOfSchema, Object valueOfSource) {
        return !like(valueOfSchema, valueOfSource);
    }

    private @NotNull Boolean like(Object valueOfSchema, Object valueOfSource) {
        Optional<String> valueOfSchemaOpt = Optional.ofNullable(valueOfSchema).map(Object::toString);
        Optional<String> valueOfSourceOpt = Optional.ofNullable(valueOfSource).map(Object::toString);
        if(!valueOfSchemaOpt.isPresent() && !valueOfSourceOpt.isPresent()) {
            return true;
        }
        if(!valueOfSourceOpt.isPresent() || !valueOfSchemaOpt.isPresent()) {
            return false;
        }
        String sourceStrValue = valueOfSourceOpt.orElse("");
        String schemaStrValue = valueOfSchemaOpt.orElse("");
        return schemaStrValue.contains(sourceStrValue);
    }

    private static @NotNull Boolean eq(ItemParamSchema schema, Object valueOfSource, Object valueOfSchema) {
        if(Objects.isNull(valueOfSource) && Objects.isNull(valueOfSchema)) {
            return true;
        }
        if(Objects.isNull(valueOfSource) || Objects.isNull(valueOfSchema)) {
            return false;
        }
        return valueOfSource.equals(schema.getValue());
    }

    private static @NotNull Map<String, ItemParamSchema> parseJsonExpr(Map<String, ItemParamSchema> paramSchemaMap, Object item) {
        Map<String, ItemParamSchema> paramValueMap = new HashMap<>();
        for (Map.Entry<String, ItemParamSchema> param : paramSchemaMap.entrySet()) {
            ItemParamSchema schema = param.getValue();
            if((schema.getValue() instanceof String)) {
                String strValue = (String) schema.getValue();
                if(strValue.startsWith("$")) {
                    paramValueMap.put(param.getKey(), schema.immutableSetValue(JSONPath.eval(item, strValue)));
                } else {
                    paramValueMap.put(param.getKey(), param.getValue());
                }
            } else {
                paramValueMap.put(param.getKey(), param.getValue());
            }
        }
        return paramValueMap;
    }

    private static Collection<Object> listValue(Object value) {
        if(value instanceof Collection) {
            return (Collection<Object>) value;
        } else {
            return ImmutableList.of(value);
        }
    }

    @Override
    public Set<String> parameterNames() {
        return ImmutableSet.of(
                REF_SYSTEM_ID,
                CUSTOMER_DATA_PATH,
                CONFIGS
        );
    }

    public static List<Map<String, ItemParamSchema>> generateCombinations(Map<String, ItemParamSchema> itemParamTable) {
        // 提取出所有 key 和 value 的列表
        List<String> keys = new ArrayList<>(itemParamTable.keySet());
        List<ItemParamSchema> values = new ArrayList<>(itemParamTable.values());

        // 初始化结果列表
        List<Map<String, ItemParamSchema>> result = new ArrayList<>();

        // 初始化组合列表，从第一个参数开始
        backtrack(result, new HashMap<>(), keys, values, 0);

        return result;
    }

    private static void backtrack(List<Map<String, ItemParamSchema>> result, Map<String, ItemParamSchema> currentMap,
                                  List<String> keys, List<ItemParamSchema> values, int index) {
        // 如果已经处理完所有 keys，则将当前的组合加入到结果列表中
        if (index == keys.size()) {
            result.add(new HashMap<>(currentMap));
            return;
        }

        String key = keys.get(index);
        ItemParamSchema schemaValue = values.get(index);

        if(schemaValue.getType() == FieldType.Collection) {

        } else if(schemaValue.getType() == FieldType.Object){

        }
        // 如果 schemaValue 是 Collection，则循环其每个元素
        if (schemaValue.getValue() instanceof Collection) {
            Collection<?> collection = (Collection<?>) schemaValue.getValue();
            for (Object item : collection) {
                currentMap.put(key, schemaValue.immutableSetValue(item));
                backtrack(result, currentMap, keys, values, index + 1);
            }
        } else {
            // 如果 schemaValue 不是 Collection，直接放入当前组合中
            currentMap.put(key, schemaValue);
            backtrack(result, currentMap, keys, values, index + 1);
        }
    }

    public static List<Map<String, ItemParamSchema>> flatConvert(Map<String, ItemParamSchema> schemaMap, Collection<Object> root) {
        // Initialize a list to hold the final result
        List<Map<String, ItemParamSchema>> resultList = new ArrayList<>();

        // Iterate over each item in the root list
        for (Object rootItem : root) {
            // For each root item, process based on the schemaMap
            List<Map<String, ItemParamSchema>> flattenedMaps = processSchema(schemaMap, rootItem);
            resultList.addAll(flattenedMaps);
        }
        return resultList;
    }

    // Process each schema recursively and flatten the result
    private static List<Map<String, ItemParamSchema>> processSchema(Map<String, ItemParamSchema> schemaMap, Object root) {
        List<Map<String, ItemParamSchema>> result = new ArrayList<>();
        result.add(new HashMap<>());  // Start with an empty map for combining results

        Map<String, ItemParamSchema> valueMap = parseJsonExpr(schemaMap, root);

        for (Map.Entry<String, ItemParamSchema> entry : valueMap.entrySet()) {
            String key = entry.getKey();
            ItemParamSchema schema = entry.getValue();

            if (schema.getType() == FieldType.Collection) {
                // Recursively handle collections
                List<Object> collection = (List<Object>) schema.getValue();
                if(CollectionUtils.isEmpty(collection)) {
                    collection = new ArrayList<>();
                    collection.add(null);
                }
                List<Map<String, ItemParamSchema>> childMaps = new ArrayList<>();
                for (Object childItem : collection) {
                    childMaps.addAll(processSchema(schema.getChildSchemas(), childItem));
                }
                // Merge childMaps into current result
                result = cartesianProduct(result, childMaps);

            } else {
                // Simple field, append to each map in the result
                for (Map<String, ItemParamSchema> map : result) {
                    map.put(key, schema);
                }
            }
        }
        return result;
    }

    // Helper method to perform a cartesian product between two list of maps
    private static List<Map<String, ItemParamSchema>> cartesianProduct(List<Map<String, ItemParamSchema>> current, List<Map<String, ItemParamSchema>> newMaps) {
        if (current.isEmpty()) {
            return newMaps;  // If the current list is empty, simply return the newMaps
        }
        List<Map<String, ItemParamSchema>> result = new ArrayList<>();
        for (Map<String, ItemParamSchema> map1 : current) {
            for (Map<String, ItemParamSchema> map2 : newMaps) {
                Map<String, ItemParamSchema> combined = new HashMap<>(map1);
                combined.putAll(map2);  // Merge the two maps
                result.add(combined);
            }
        }
        return result;
    }

    public static void main(String[] args) {
        String schemaStr = "{\n" +
                "        \"testSampleNo\": {\n" +
                "          \"value\": \"$.testSample.testSampleNo\",\n" +
                "          \"match\": false\n" +
                "        },\n" +
                "        \"ppNo\": {\n" +
                "          \"value\": \"$.testLine.ppTestLineRelList[0].ppNo\",\n" +
                "          \"match\": true,\n" +
                "          \"matchCase\": \"Eq\"\n" +
                "        },\n" +
                "        \"testLineId\": {\n" +
                "          \"value\": \"$.testLine.testLineId\",\n" +
                "          \"match\": true,\n" +
                "          \"matchCase\": \"Eq\"\n" +
                "        },\n" +
                "        \"limitGroup\": {\n" +
                "          \"value\": \"$.testSample.limitGroupList\",\n" +
                "          \"match\": false,\n" +
                "          \"type\": \"Collection\",\n" +
                "          \"childSchemas\":{\n" +
                "            \"limitGroupId\":{\n" +
                "                \"value\": \"$.limitGroupId\",\n" +
                "                \"match\": true,\n" +
                "                \"matchCase\": \"Eq\"\n" +
                "            },\n" +
                "            \"productAttributeId\": {\n" +
                "                \"value\": \"$.productAttributeList.productAttributeId\",\n" +
                "                \"match\": true,\n" +
                "                \"matchCase\": \"Eq\"\n" +
                "            }\n" +
                "          }\n" +
                "        },\n" +
                "        \"analyte\": {\n" +
                "          \"value\": \"$.testLine.analyteList\",\n" +
                "          \"match\": false,\n" +
                "          \"type\": \"Collection\",\n" +
                "          \"childSchemas\":{\n" +
                "            \"analyteId\":{\n" +
                "                \"value\": \"$.analyteId\",\n" +
                "                \"match\": true,\n" +
                "                \"matchCase\": \"Eq\"\n" +
                "            },\n" +
                "            \"analyteInstanceId\": {\n" +
                "                \"value\": \"$.analyteInstanceId\",\n" +
                "                \"match\": false\n" +
                "            }\n" +
                "          }\n" +
                "        },\n" +
                "        \"passIf\": {\n" +
                "          \"value\": \"Exclude\",\n" +
                "          \"match\": false\n" +
                "        },\n" +
                "        \"objectType\": {\n" +
                "          \"value\": \"LimitGroup\",\n" +
                "          \"match\": false\n" +
                "        },\n" +
                "        \"checkCategory\": {\n" +
                "          \"value\": \"$.product.productAttrList[labelCode='SpecialCustomerAttribute1'][0].languageList[languageId=2][0].value\",\n" +
                "          \"match\": true,\n" +
                "          \"matchCase\": \"Eq\"\n" +
                "        }\n" +
                "      }";
        String valueStr = "{\"testSample\":{\"conclusion\":{},\"parentTestSampleId\":\"5f30d0bc-af2e-4a52-99fc-de24f72c1fbf\",\"materialAttrList\":[{\"extFields\":{\"materialCategory\":\"[\\\"针织\\\",\\\"针织净色\\\",\\\"太空棉/空气层/健康布\\\"]\"},\"materialOtherSampleInfo\":\"面料66\",\"materialRemark\":\"\",\"materialTexture\":\"\",\"materialEndUse\":\"袖子\",\"materialSampleRemark\":\"\",\"materialColor\":\"黑色\",\"materialDescription\":\"太空棉/空气层/健康布\"}],\"systemId\":2,\"testSampleSeq\":101101000,\"externalSampleNo\":\"1\",\"orderNo\":\"SL924593301493TX\",\"materialAttr\":{\"extFields\":{\"materialCategory\":\"[\\\"针织\\\",\\\"针织净色\\\",\\\"太空棉/空气层/健康布\\\"]\"},\"materialOtherSampleInfo\":\"面料66\",\"materialRemark\":\"\",\"materialTexture\":\"\",\"materialEndUse\":\"袖子\",\"materialSampleRemark\":\"\",\"materialColor\":\"黑色\",\"materialDescription\":\"太空棉/空气层/健康布\"},\"testSampleNo\":\"1\",\"externalTestSampleNo\":\"1\",\"testSampleInstanceId\":\"757de11f-0216-45ee-b623-16bac5534f56\",\"testSampleType\":102},\"product\":{\"productInstanceId\":\"66ddd0ba-b8ce-44a3-ad6a-dcf0b163a29a\",\"templateId\":\"57d5b745-d259-479d-9d5f-cdb684a6e022\",\"productAttrList\":[{\"displayInReport\":\"1\",\"dataType\":\"TextArea\",\"attrSeq\":19,\"labelValue\":\"\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"\",\"labelName\":\"SKU No.\",\"value\":\"\"}],\"labelName\":\"SKU No.\",\"value\":\"\",\"labelCode\":\"RefCode9\",\"seq\":19},{\"displayInReport\":\"1\",\"dataType\":\"Select2\",\"attrSeq\":13,\"labelValue\":\"Via Market\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"Via Market\",\"labelName\":\"Production Channel Source\",\"value\":\"Via Market\"}],\"labelName\":\"Production Channel Source\",\"value\":\"Via Market\",\"labelCode\":\"SpecialCustomerAttribute5\",\"seq\":13},{\"displayInReport\":\"1\",\"dataType\":\"Select2\",\"attrSeq\":14,\"labelValue\":\"\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"\",\"labelName\":\"Product Category\",\"value\":\"\"}],\"labelName\":\"Product Category\",\"value\":\"\",\"labelCode\":\"ProductCategory1\",\"seq\":14},{\"displayInReport\":\"1\",\"dataType\":\"Input\",\"attrSeq\":16,\"labelValue\":\"\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"\",\"labelName\":\"Previous Report No.\",\"value\":\"\"}],\"labelName\":\"Previous Report No.\",\"value\":\"\",\"labelCode\":\"PreviousReportNo\",\"seq\":16},{\"displayInReport\":\"1\",\"dataType\":\"Select\",\"attrSeq\":15,\"labelValue\":\"SEE TEST PROPERTY\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"SEE TEST PROPERTY\",\"labelName\":\"Test Requested\",\"value\":\"SEE TEST PROPERTY\"}],\"labelName\":\"Test Requested\",\"value\":\"SEE TEST PROPERTY\",\"labelCode\":\"SpecialCustomerAttribute7\",\"seq\":15},{\"displayInReport\":\"0\",\"dataType\":\"Input\",\"attrSeq\":20,\"labelValue\":\"泳衣品类(沙滩类)\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"泳衣品类(沙滩类)\",\"labelName\":\"Check Category\",\"value\":\"泳衣品类(沙滩类)\"}],\"labelName\":\"Check Category\",\"value\":\"泳衣品类(沙滩类)\",\"labelCode\":\"SpecialCustomerAttribute1\",\"seq\":20},{\"displayInReport\":\"1\",\"dataType\":\"Input\",\"attrSeq\":17,\"languageList\":[{\"languageId\":1,\"labelName\":\"Sample Receiving Date\"}],\"labelName\":\"Sample Receiving Date\",\"labelCode\":\"SampleReceivedDate\",\"seq\":17},{\"displayInReport\":\"1\",\"dataType\":\"Input\",\"attrSeq\":3,\"labelValue\":\"\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"\",\"labelName\":\"Fabric Supplier/Mill\",\"value\":\"\"}],\"labelName\":\"Fabric Supplier/Mill\",\"value\":\"\",\"labelCode\":\"Supplier\",\"seq\":3},{\"displayInReport\":\"1\",\"dataType\":\"TextArea\",\"attrSeq\":7,\"labelValue\":\"\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"\",\"labelName\":\"Sample Description\",\"value\":\"\"}],\"labelName\":\"Sample Description\",\"value\":\"\",\"labelCode\":\"ProductDescription\",\"seq\":7},{\"displayInReport\":\"0\",\"dataType\":\"Input\",\"attrSeq\":21,\"labelValue\":\"SHEIN Brand\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"SHEIN Brand\",\"labelName\":\"Quality Rank Name\",\"value\":\"SHEIN Brand\"}],\"labelName\":\"Quality Rank Name\",\"value\":\"SHEIN Brand\",\"labelCode\":\"SpecialCustomerAttribute3\",\"seq\":21},{\"displayInReport\":\"0\",\"dataType\":\"Input\",\"attrSeq\":18,\"labelValue\":\"\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"\",\"labelName\":\"GoodsTexture\",\"value\":\"\"}],\"labelName\":\"GoodsTexture\",\"value\":\"\",\"labelCode\":\"RefCode4\",\"seq\":18}]},\"testMatrixId\":\"3bd7e9cd-aae3-422c-8dc7-33e9d35541f2\",\"testLine\":{\"systemId\":2,\"orderNo\":\"SL924593301493TX\",\"citationFullName\":\"ISO 105-C06:2010\",\"productLineAbbr\":\"SL\",\"testLineVersionId\":83306,\"citation\":{\"citationSectionId\":0,\"citationFullName\":\"ISO 105-C06:2010\",\"citationId\":1008,\"citationName\":\"ISO 105-C06:2010\",\"languageList\":[{\"citationFullName\":\"ISO 105-C06:2010\",\"languageId\":1,\"citationName\":\"ISO 105-C06:2010\"},{\"citationFullName\":\"ISO 105-C06:2010\",\"languageId\":2}],\"citationType\":3,\"citationVersionId\":1008},\"evaluationName\":\"Colour Fastness To Washing\",\"ppName\":\"SLTX_Supplier RSL& Garment test 2023\",\"citationId\":1008,\"analyteList\":[{\"unitBaseId\":123,\"analyteName\":\"Cross Staining\",\"reportUnit\":\"Grade\",\"analyteSeq\":8,\"analyteInstanceId\":\"00e28ad1-a633-423e-b63c-08349c98352c\",\"unitId\":154,\"analyteId\":16457,\"languageList\":[{\"analyteName\":\"Cross Staining\",\"reportUnit\":\"Grade\",\"languageId\":1},{\"analyteName\":\"自沾\",\"reportUnit\":\"级\",\"languageId\":2}],\"analyteBaseId\":2837},{\"unitBaseId\":123,\"analyteName\":\"Change in Shade\",\"reportUnit\":\"Grade\",\"analyteSeq\":1,\"analyteInstanceId\":\"50653b7e-0669-4aa9-ab47-202ac88e23f5\",\"unitId\":154,\"analyteId\":16423,\"languageList\":[{\"analyteName\":\"Change in Shade\",\"reportUnit\":\"Grade\",\"languageId\":1},{\"analyteName\":\"原样变色\",\"reportUnit\":\"级\",\"languageId\":2}],\"analyteBaseId\":2791},{\"unitBaseId\":123,\"analyteName\":\"Staining On Solvent\",\"reportUnit\":\"Grade\",\"analyteSeq\":7,\"analyteInstanceId\":\"62a8b225-e4db-41b1-b04f-67260656684f\",\"unitId\":154,\"analyteId\":20481,\"languageList\":[{\"analyteName\":\"Staining On Solvent\",\"reportUnit\":\"Grade\",\"languageId\":1},{\"analyteName\":\"浮色\",\"reportUnit\":\"级\",\"languageId\":2}],\"analyteBaseId\":6419},{\"unitBaseId\":123,\"analyteName\":\"Cotton\",\"reportUnit\":\"Grade\",\"analyteSeq\":3,\"analyteInstanceId\":\"6820a37a-33cc-4bde-9390-db717037c126\",\"unitId\":154,\"analyteId\":16450,\"languageList\":[{\"analyteName\":\"Cotton\",\"reportUnit\":\"Grade\",\"languageId\":1},{\"analyteName\":\"贴衬沾色（棉）\",\"reportUnit\":\"级\",\"languageId\":2}],\"analyteBaseId\":2825},{\"unitBaseId\":123,\"analyteName\":\"Polyester\",\"reportUnit\":\"Grade\",\"analyteSeq\":5,\"analyteInstanceId\":\"737fd1a5-5b11-4dd0-96b0-c1cfb395f801\",\"unitId\":154,\"analyteId\":16705,\"languageList\":[{\"analyteName\":\"Polyester\",\"reportUnit\":\"Grade\",\"languageId\":1},{\"analyteName\":\"贴衬沾色（涤纶）\",\"reportUnit\":\"级\",\"languageId\":2}],\"analyteBaseId\":3070},{\"unitBaseId\":123,\"analyteName\":\"Acrylic\",\"reportUnit\":\"Grade\",\"analyteSeq\":6,\"analyteInstanceId\":\"b9b4f551-52f1-4d49-bc15-8ac3e01ca3fe\",\"unitId\":154,\"analyteId\":16344,\"languageList\":[{\"analyteName\":\"Acrylic\",\"reportUnit\":\"Grade\",\"languageId\":1},{\"analyteName\":\"贴衬沾色（腈纶）\",\"reportUnit\":\"级\",\"languageId\":2}],\"analyteBaseId\":2746},{\"unitBaseId\":123,\"analyteName\":\"Polyamide\",\"reportUnit\":\"Grade\",\"analyteSeq\":4,\"analyteInstanceId\":\"bfd1f909-c1cd-451c-a397-73f909a5dd1c\",\"unitId\":154,\"analyteId\":21606,\"languageList\":[{\"analyteName\":\"Polyamide\",\"reportUnit\":\"Grade\",\"languageId\":1},{\"analyteName\":\"贴衬沾色（锦纶）\",\"reportUnit\":\"级\",\"languageId\":2}],\"analyteBaseId\":7452}],\"citationType\":3,\"testLineId\":76923,\"testLineType\":0,\"labSectionBaseId\":13753,\"conclusion\":{},\"wi\":{},\"testLineBaseId\":2293708,\"ppNo\":22059008,\"testLineInstanceId\":\"5dad179b-7baf-4887-b205-02c8dea6379d\",\"languageList\":[{\"evaluationName\":\"Colour Fastness To Washing\",\"languageId\":1,\"evaluationAlias\":\"Colour Fastness To Washing\"},{\"evaluationName\":\"耐洗色牢度\",\"languageId\":2,\"evaluationAlias\":\"耐水洗色牢度\"}],\"ppTestLineRelList\":[{\"ppVersionId\":324294,\"ppTlRelId\":\"4bbb7db5-4fb6-4835-8f5d-5e2166ef66e2\",\"ppNo\":22059008,\"ppBaseId\":1886066,\"ppName\":\"SLTX_Supplier RSL& Garment test 2023\",\"ppArtifactRelId\":48764018,\"languageList\":[{\"ppName\":\"SLTX_Supplier RSL& Garment test 2023\",\"languageId\":1},{\"ppName\":\"SLTX_New测试 PP 2022\",\"languageId\":2}],\"sectionLevel\":0,\"aid\":8423449}],\"evaluationAlias\":\"Colour Fastness To Washing\",\"testLineSeq\":64,\"testLineStatus\":701},\"sample\":{\"sampleInstanceId\":\"5f30d0bc-af2e-4a52-99fc-de24f72c1fbf\",\"productItemNo\":\"SupplierCode_1\",\"sampleAttrList\":[{\"displayInReport\":\"0\",\"dataType\":\"Input\",\"attrSeq\":9,\"labelValue\":\"Elastane 3;Polyamide 27;Polyester 70;\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"Elastane 3;Polyamide 27;Polyester 70;\",\"labelName\":\"Fiber Content\",\"value\":\"Elastane 3;Polyamide 27;Polyester 70;\"}],\"labelName\":\"Fiber Content\",\"value\":\"Elastane 3;Polyamide 27;Polyester 70;\",\"labelCode\":\"FiberComposition\",\"seq\":9},{\"displayInReport\":\"1\",\"dataType\":\"Input\",\"attrSeq\":8,\"labelValue\":\"sw2112155151458345\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"sw2112155151458345\",\"labelName\":\"Style No./SKC NO.\",\"value\":\"sw2112155151458345\"}],\"labelName\":\"Style No./SKC NO.\",\"value\":\"sw2112155151458345\",\"labelCode\":\"StyleNo\",\"seq\":8},{\"displayInReport\":\"1\",\"dataType\":\"Input\",\"attrSeq\":13,\"labelValue\":\"\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"\",\"labelName\":\"Client Expected Care Instrustion\",\"value\":\"\"}],\"labelName\":\"Client Expected Care Instrustion\",\"value\":\"\",\"labelCode\":\"SpecialCustomerAttribute9\",\"seq\":13},{\"displayInReport\":\"1\",\"dataType\":\"Input\",\"attrSeq\":5,\"labelValue\":\"White\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"White\",\"labelName\":\"Color\",\"value\":\"White\"}],\"labelName\":\"Color\",\"value\":\"White\",\"labelCode\":\"ProductColor\",\"seq\":5},{\"displayInReport\":\"1\",\"dataType\":\"Input\",\"attrSeq\":7,\"labelValue\":\"8696463-1\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"8696463-1\",\"labelName\":\"P.O. No.\",\"value\":\"8696463-1\"}],\"labelName\":\"P.O. No.\",\"value\":\"8696463-1\",\"labelCode\":\"PONo\",\"seq\":7},{\"displayInReport\":\"1\",\"dataType\":\"Input\",\"attrSeq\":6,\"labelValue\":\"儿童\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"儿童\",\"labelName\":\"Age Group\",\"value\":\"儿童\"}],\"labelName\":\"Age Group\",\"value\":\"儿童\",\"labelCode\":\"AgeGroup\",\"seq\":6},{\"displayInReport\":\"1\",\"dataType\":\"Input\",\"attrSeq\":10,\"labelValue\":\"\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"\",\"labelName\":\"End Use\",\"value\":\"\"}],\"labelName\":\"End Use\",\"value\":\"\",\"labelCode\":\"EndUse1\",\"seq\":10},{\"displayInReport\":\"1\",\"dataType\":\"Input\",\"attrSeq\":11,\"labelValue\":\"\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"\",\"labelName\":\"Claimed Fabric Weight\",\"value\":\"\"}],\"labelName\":\"Claimed Fabric Weight\",\"value\":\"\",\"labelCode\":\"FiberWeight\",\"seq\":11},{\"displayInReport\":\"0\",\"dataType\":\"Input\",\"attrSeq\":1,\"labelValue\":\"1\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"1\",\"labelName\":\"No. of Sample\",\"value\":\"1\"}],\"labelName\":\"No. of Sample\",\"value\":\"1\",\"labelCode\":\"NoOfSample\",\"seq\":1},{\"displayInReport\":\"0\",\"dataType\":\"TextArea\",\"attrSeq\":4,\"labelValue\":\"泳衣品类(沙滩类)\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"泳衣品类(沙滩类)\",\"labelName\":\"Category\",\"value\":\"泳衣品类(沙滩类)\"}],\"labelName\":\"Category\",\"value\":\"泳衣品类(沙滩类)\",\"labelCode\":\"SpecialCustomerAttribute2\",\"seq\":4},{\"displayInReport\":\"0\",\"dataType\":\"Input\",\"attrSeq\":14,\"labelValue\":\"\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"\",\"labelName\":\"QualityRank\",\"value\":\"\"}],\"labelName\":\"QualityRank\",\"value\":\"\",\"labelCode\":\"RefCode1\",\"seq\":14},{\"displayInReport\":\"1\",\"dataType\":\"Input\",\"attrSeq\":3,\"labelValue\":\"针织长裤\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"针织长裤\",\"labelName\":\"Sample Descriptions\",\"value\":\"针织长裤\"}],\"labelName\":\"Sample Descriptions\",\"value\":\"针织长裤\",\"labelCode\":\"OtherSampleInformation\",\"seq\":3},{\"displayInReport\":\"1\",\"dataType\":\"Input\",\"attrSeq\":12,\"labelValue\":\"SGSMOCK_1_FJC202408200006-1\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"SGSMOCK_1_FJC202408200006-1\",\"labelName\":\"Shein No.\",\"value\":\"SGSMOCK_1_FJC202408200006-1\"}],\"labelName\":\"Shein No.\",\"value\":\"SGSMOCK_1_FJC202408200006-1\",\"labelCode\":\"SpecialCustomerAttribute4\",\"seq\":12},{\"displayInReport\":\"0\",\"dataType\":\"Input\",\"attrSeq\":2,\"labelValue\":\"A\",\"languageList\":[{\"languageId\":1,\"labelValue\":\"A\",\"labelName\":\"Sample ID\",\"value\":\"A\"}],\"labelName\":\"Sample ID\",\"value\":\"A\",\"labelCode\":\"SampleID\",\"seq\":2}],\"sampleNo\":\"A\",\"templateId\":\"2b1a0d7b-1a08-487d-b21a-00fbfbd0e71f\",\"testSampleInstanceId\":\"5f30d0bc-af2e-4a52-99fc-de24f72c1fbf\"}}";
        Map<String, ItemParamSchema> schemaMap = JSON.parseObject(schemaStr, new TypeReference<Map<String, ItemParamSchema>>() {
        });
        JSONObject value = JSON.parseObject(valueStr);
        TrfTestSampleLimitGroupDTO limitGroupDTO = new TrfTestSampleLimitGroupDTO();
        limitGroupDTO.setLimitGroupId(1);
        TrfProductAttributeDTO productAttributeDTO = new TrfProductAttributeDTO();
        productAttributeDTO.setProductAttributeId(2);
        TrfProductAttributeDTO productAttributeDTO1 = new TrfProductAttributeDTO();
        productAttributeDTO1.setProductAttributeId(5);

        TrfTestSampleLimitGroupDTO limitGroupDTO1 = new TrfTestSampleLimitGroupDTO();
        limitGroupDTO1.setLimitGroupId(3);
        TrfProductAttributeDTO productAttributeDTO2 = new TrfProductAttributeDTO();
        productAttributeDTO2.setProductAttributeId(4);

        limitGroupDTO.setProductAttributeList(ImmutableList.of(productAttributeDTO,productAttributeDTO1));
        limitGroupDTO1.setProductAttributeList(ImmutableList.of(productAttributeDTO2));
//        value.getJSONObject("testSample").put("limitGroupList", ImmutableList.of(limitGroupDTO, limitGroupDTO1));

        List<Map<String, ItemParamSchema>> maps = flatConvert(schemaMap, ImmutableList.of(value));
        System.out.println(maps);

    }
}
