package com.sgs.customerbiz.validation.filter;


import com.googlecode.aviator.script.AviatorBindings;
import com.sgs.customerbiz.validation.service.FieldValidationConfig;

import javax.script.Bindings;
import java.util.Optional;

public interface ValidationFilter {

   String PRODUCT_LINE_CODE = "productLineCode";
   String MODE_PARAM_NAME = "mode";
   String REF_SYSTEM_ID = "refSystemId";
   String SYSTEM_ID = "systemId";
   String DATA = "data";

   Optional<FieldValidationConfig> filter(FieldValidationConfig fieldValidationConfig, FilterContext ctx);


   default Bindings aviatorBindings(FilterContext ctx) {
      String mode = ctx.getMode();
      Integer refSystemId = ctx.getRefSystemId();
      Integer systemId = ctx.getSystemId();
      Bindings bindings = new AviatorBindings();
      bindings.put(MODE_PARAM_NAME, mode);
      bindings.put(REF_SYSTEM_ID, refSystemId);
      bindings.put(SYSTEM_ID, systemId);
      bindings.put(PRODUCT_LINE_CODE, ctx.getProductLineCode());
      bindings.put(DATA, ctx.getStandardModel());
      return bindings;
   }


}
