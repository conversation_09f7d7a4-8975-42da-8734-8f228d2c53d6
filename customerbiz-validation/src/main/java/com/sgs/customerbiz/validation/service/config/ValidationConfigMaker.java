package com.sgs.customerbiz.validation.service.config;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.sgs.customerbiz.validation.enums.FieldType;
import com.sgs.customerbiz.validation.service.FieldRuleConfig;
import com.sgs.customerbiz.validation.service.FieldValidationConfig;
import com.sgs.customerbiz.validation.service.RuleConfig;
import com.sgs.customerbiz.validation.validator.*;
import com.sgs.customerbiz.validation.validator.veyer.VeyerSupplier;
import lombok.Data;
import org.apache.commons.lang3.text.StrSubstitutor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sgs.customerbiz.validation.validator.AbstractLogicalLengthRule.FIELD_LENGTH2;
import static com.sgs.customerbiz.validation.validator.AbstractLogicalRule.RULE_DESC;
import static com.sgs.customerbiz.validation.validator.Between.*;
import static com.sgs.customerbiz.validation.validator.ConditionalRequired.CONDITIONAL_SCRIPT;
import static com.sgs.customerbiz.validation.validator.Exists.VALUE_SET;
import static com.sgs.customerbiz.validation.validator.Length.FIELD_LENGTH;
import static com.sgs.customerbiz.validation.validator.Logical.LOGICAL_VALUE;
import static com.sgs.customerbiz.validation.validator.Logical.OPERATION;

public class ValidationConfigMaker {

    public static FieldValidationConfig mkReportConfig(String fieldName,  FieldType fieldType, String expr, List<FieldRuleConfig> fieldRuleConfigs) {
        return mkConfig("Report", fieldName, fieldType, expr, fieldRuleConfigs);
    }

    public static FieldValidationConfig mkOrderConfig(String fieldName,  FieldType fieldType, String expr, List<FieldRuleConfig> fieldRuleConfigs) {
        return mkConfig("Order", fieldName, fieldType, expr, fieldRuleConfigs);
    }

    public static FieldValidationConfig mkTrfConfig(String fieldName,  FieldType fieldType, String expr, List<FieldRuleConfig> fieldRuleConfigs) {
        return mkConfig("Trf", fieldName, fieldType, expr, fieldRuleConfigs);
    }

    public static FieldValidationConfig mkConfig(String modelName,
                                                  String fieldName,
                                                  FieldType fieldType,
                                                  String expr,
                                                  List<FieldRuleConfig> fieldRuleConfigs) {
        FieldValidationConfig config = new FieldValidationConfig();
        config.setCustomized(true);
        config.setModelName(modelName);
        config.setFieldName(fieldName);
        config.setFieldType(fieldType);
        config.setExpr(expr);
        config.setFieldRuleConfigList(
                fieldRuleConfigs.stream()
                        .map(frc -> frc.ruleConfig(RuleConfig.findRuleConfig(frc.getRuleCode())))
                        .collect(Collectors.toList())
        );
        return config;
    }




    public static FieldRuleConfig mkConditionalRequired(String k1, String v1, String condition) {
        return mkFieldRuleConfig(ConditionalRequired.class.getSimpleName(), ImmutableMap.of(
                k1, v1, CONDITIONAL_SCRIPT, condition
        ));
    }

    public static FieldRuleConfig mkRequired() {
        return mkFieldRuleConfig(Required.class.getSimpleName(), ImmutableMap.of());
    }

    public static FieldRuleConfig mkLength(int length) {
        return mkFieldRuleConfig(Length.class.getSimpleName(), ImmutableMap.of(
                FIELD_LENGTH, length
        ));
    }

    public static FieldRuleConfig mkMaxLength(int length) {
        return mkFieldRuleConfig(MaxLength.class.getSimpleName(), ImmutableMap.of(
                FIELD_LENGTH, length
        ));
    }

    public static FieldRuleConfig mkRangeLength(int min, int max) {
        return mkFieldRuleConfig(RangeLength.class.getSimpleName(), ImmutableMap.of(
                FIELD_LENGTH, min,
                FIELD_LENGTH2, max
        ));
    }

    public static FieldRuleConfig mkRangeClosedLength(int min, int max) {
        return mkFieldRuleConfig(RangeClosedLength.class.getSimpleName(), ImmutableMap.of(
                FIELD_LENGTH, min,
                FIELD_LENGTH2, max
        ));
    }

    public static FieldRuleConfig mkBetween(String leftValue, String leftOperation, String rightValue, String rightOperation, String ruleDesc) {
        return mkFieldRuleConfig(Between.class.getSimpleName(), ImmutableMap.of(
                LEFT_VALUE, leftValue,
                LEFT_OPERATION, leftOperation,
                RIGHT_VALUE, rightValue,
                RIGHT_OPERATION, rightOperation,
                RULE_DESC, ruleDesc
        ));
    }

    public static FieldRuleConfig mkLogical(String logicalValue, String operation, String ruleDesc) {
        return mkFieldRuleConfig(Logical.class.getSimpleName(),
                ImmutableMap.of(LOGICAL_VALUE, logicalValue, OPERATION, operation, RULE_DESC, ruleDesc));
    }

    public static FieldRuleConfig mkExists(String valueSet) {
        return mkFieldRuleConfig(Exists.class.getSimpleName(), ImmutableMap.of(
                VALUE_SET, valueSet
        ));
    }

    public static FieldRuleConfig mkNonContains(String validationChars, String ruleDesc) {
        return mkFieldRuleConfig(NonContains.class.getSimpleName(), ImmutableMap.of(
                NonContains.VALIDATION_CHARS, validationChars,
                NonContains.RULE_DESC, ruleDesc
        ));
    }

    public static FieldRuleConfig mkSplitLength(String delimiter, Integer fieldLength) {
        return mkFieldRuleConfig(SplitLength.class.getSimpleName(), ImmutableMap.of(
                SplitLength.DELIMITER, delimiter,
                FIELD_LENGTH, fieldLength
        ));
    }

    public static FieldRuleConfig mkSplitRangeLength(String delimiter, Integer min, Integer max) {
        return mkFieldRuleConfig(SplitRangeLength.class.getSimpleName(), ImmutableMap.of(
                SplitLength.DELIMITER, delimiter,
                FIELD_LENGTH, min,
                FIELD_LENGTH2, max
        ));
    }

    public static FieldRuleConfig mkSplitRangeClosedLength(String delimiter, Integer min, Integer max) {
        return mkFieldRuleConfig(SplitRangeClosedLength.class.getSimpleName(), ImmutableMap.of(
                SplitLength.DELIMITER, delimiter,
                FIELD_LENGTH, min,
                FIELD_LENGTH2, max
        ));
    }

    public static FieldRuleConfig mkExistsCustomerData(Integer refSystemId, String customerDataPath) {
        return mkFieldRuleConfig(ExistsCustomerData.class.getSimpleName(), ImmutableMap.of(
                ExistsCustomerData.REF_SYSTEM_ID, refSystemId,
                ExistsCustomerData.CUSTOMER_DATA_PATH, customerDataPath
        ));
    }

    public static FieldRuleConfig mkVeyerSupplier(String factoryName, String factoryNo, String vendorName, String vendorNo) {
        return mkFieldRuleConfig(VeyerSupplier.class.getSimpleName(), ImmutableMap.of(
                VeyerSupplier.VENDOR_NAME, vendorName,
                VeyerSupplier.VENDOR_NO, vendorNo,
                VeyerSupplier.FACTORY_NAME, factoryName,
                VeyerSupplier.FACTORY_NO, factoryNo
        ));
    }

    public static FieldRuleConfig mkOnlyOneHaveValue(String otherValue, String otherName) {
        return mkFieldRuleConfig(OnlyOneHaveValue.class.getSimpleName(), ImmutableMap.of(
                OnlyOneHaveValue.OTHER_VALUE, otherValue,
                OnlyOneHaveValue.OTHER_NAME, otherName
        ));
    }

    public static FieldRuleConfig mkFieldRuleConfig(String ruleCode, Map<String, Object> param) {
        Map<String, Object> paramTable = new HashMap<>(param);
        paramTable.put("reportNo", "$.Report[0].reportNo");
        return FieldRuleConfig.of(ruleCode, JSON.toJSONString(paramTable));
    }



    public static void main(String[] args) {

        int index = 211;
        for (String mailConfig : WalmartMaker.getEmailConfig()) {
            index = index+1;
            String[] split = mailConfig.split(",");
            String[] mails = split[1].split(";");
//            String[] mails = new String[]{"<EMAIL>","<EMAIL>"};
            String lab = split[0];
            String[] labSplit = lab.split(" ");
            String bu = labSplit[1];
            if(bu.equals("EEC")) {
                bu = "EE";
            }
            RecipientConfigInfo configInfo = new RecipientConfigInfo();
            configInfo.setCustomerName("WALMART");
            configInfo.setMailTo(Stream.of(mails).collect(Collectors.toList()));
            configInfo.setType(RecipientConfigInfo.RecipientType.Bus);
            configInfo.setLabCode(lab);
            StrSubstitutor formatter = new StrSubstitutor(ImmutableMap.of(
                    "id", index,
                    "refSystemId", 10022,
                    "recipient", JSON.toJSONString(configInfo),
                    "lab", lab,
                    "bu", bu
            ));
            System.out.println(
                    formatter.replace("INSERT INTO sgs_todolistdb.tb_cfg_info (id, csutomer_group, customer_no, identity_id, product_line, config_type, config_key, config_value, created_by, created_date, modified_by, modified_date, last_modified_timestamp)\n" +
                            "VALUES (${id}, null, null, '${refSystemId}', '${bu}', 2, 'Recipient', '${recipient}', 'admin', '2024-03-25 17:49:38', 'admin', '2024-03-25 17:49:38', '2024-04-17 21:35:00');\n")
            );
        }

    }

    @Data
    public static class RecipientConfigInfo {

        public enum RecipientType {
            Bus, CustomerInterface, System
        }

        private Integer refSystemId;
        private String labCode;
        private RecipientType type;
        private String customerName;
        private List<String> mailTo;

    }

}
