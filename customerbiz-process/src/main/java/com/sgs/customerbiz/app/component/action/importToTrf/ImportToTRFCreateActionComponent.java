package com.sgs.customerbiz.app.component.action.importToTrf;

import com.sgs.customerbiz.app.component.action.BaseActionComponent;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.service.importtrf.cmd.DefaultImportTrfActionExtPt;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.util.UserHelper;
import com.sgs.customerbiz.domain.domainevent.TrfNewEvent;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.TrfLabDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.StringUtil;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Component
@LiteflowComponent(id = "importToTRFCreateActionComponent", name = "importToTRF默认CreateAction处理器")
public class ImportToTRFCreateActionComponent extends BaseActionComponent {

    @Resource
    private DefaultImportTrfActionExtPt defaultImportTrfActionExtPt;
    @Override
    public void process() throws Exception {

        ImportToTRFContext importToTRFContext = this.getContextBean(ImportToTRFContext.class);
        createTrf(importToTRFContext);
    }

    protected void createTrf(ImportToTRFContext importToTRFContext) {
        TrfImportReq importReq = importToTRFContext.getTrfImportReq();
        TrfDTO sgsTRF = importToTRFContext.getTrfDTO();
        TrfDOV2 trfParam = createSgsTrf(sgsTRF, importToTRFContext.getCustomerTrf(), importReq);
        importToTRFContext.setTrfDOV2(trfParam);

        TrfNewEvent createEvent = defaultImportTrfActionExtPt.getTrfNewEvent(sgsTRF, importReq);
        importToTRFContext.getTrfEventList().add(createEvent);
    }

    protected TrfDOV2 createSgsTrf(TrfDTO sgsTRF, String customerTRFJson, TrfImportReq importReq) {
        // 1.validate trf
        validateImportTrf(sgsTRF, importReq);
        preHandler(sgsTRF, importReq);
        TrfDOV2 trfParam = TrfConvertor.toTrfDOV2(sgsTRF);
        beforeCreateTrf(trfParam, sgsTRF, importReq);
        return trfParam;
    }
    protected void beforeCreateTrf(TrfDOV2 trfParam, TrfDTO sgsTRF, TrfImportReq importReq) {
        defaultImportTrfActionExtPt.beforeCreateTrf(trfParam, sgsTRF, importReq);
    }

    protected void validateImportTrf(TrfDTO sgsTRF, TrfImportReq importReq) {
        defaultImportTrfActionExtPt.validateImportTrf(sgsTRF, importReq);
    }
    protected void preHandler(TrfDTO sgsTRF, TrfImportReq importReq) {
        defaultImportTrfActionExtPt.preHandler(sgsTRF, importReq);
    }

    protected TrfNewEvent publishTrfCreateEvent(TrfDTO trfDTO, TrfImportReq importReq) {
        TrfNewEvent createEvent = new TrfNewEvent(TrfEventTriggerBy.CUSTOMER);

        createEvent.setPayload(trfDTO);
        createEvent.setRefSystemId(importReq.getRefSystemId());
        createEvent.setProductLineCode(Func.isEmpty(ProductLineContextHolder.getProductLineCode()) ? importReq.getProductLineCode() : ProductLineContextHolder.getProductLineCode());
        createEvent.setSystemId(importReq.getSystemId());
        createEvent.setSourceId(importReq.getRequestId());
        createEvent.setTrfNo(importReq.getTrfNo());
        createEvent.setStatusFrom(TrfStatusEnum.New.getStatus());
        createEvent.setStatusTo(TrfStatusEnum.New.getStatus());
        createEvent.setOperator(importReq.getOperator());
        createEvent.setOperationTime(importReq.getOperationTime());
        return createEvent;
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        ImportToTRFContext importToTRFContext = (ImportToTRFContext)requestContext;
        createTrf(importToTRFContext);
        return importToTRFContext;
    }
}
