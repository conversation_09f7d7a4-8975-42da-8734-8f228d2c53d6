package com.sgs.customerbiz.app.process;

import com.alibaba.fastjson.JSON;
import com.sgs.customerbiz.biz.convertor.TrfSyncConvertor;
import com.sgs.customerbiz.context.ImportToTRFContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.dto.resp.TrfSyncResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.core.FlowExecutorHolder;
import com.yomahub.liteflow.enums.ParseModeEnum;
import com.yomahub.liteflow.flow.FlowBus;
import com.yomahub.liteflow.flow.LiteflowResponse;
import com.yomahub.liteflow.flow.element.Node;
import com.yomahub.liteflow.flow.id.DefaultRequestIdGenerator;
import com.yomahub.liteflow.flow.id.RequestIdGenerator;
import com.yomahub.liteflow.property.LiteflowConfig;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
public class SyncTRFProcess  implements BaseProcess<TrfSyncResult, TrfSyncReq>{


    @Resource
    private ProcessFlowManager processFlowManager;
    @Override
    public TrfSyncResult doProcess(TrfSyncReq object) {
        SyncTRFContext syncTRFContext = new SyncTRFContext();
        syncTRFContext.setSyncReq(object);
        syncTRFContext.setAction(object.getAction());
        //System.out.println("JsonData:" + JSON.toJSON(syncTRFContext));
        //Chain chain = FlowBus.getChain("SmartImport2TRF");
        String chainName = "Sync2TRF";
        syncTRFContext.setChainName(chainName);
        // 执行流程
        LiteflowResponse response = processFlowManager.executeFlow(ProcessTypeEnum.SYNC_TRF,chainName, syncTRFContext);
        //System.out.println("LiteflowResponse="+response.getExecuteStepStr());
        if (response.isSuccess()) {
            SyncTRFContext responseContext = response.getContextBean(SyncTRFContext.class);
            return  TrfSyncConvertor.toSyncResult(responseContext.getSyncReq());
        } else {
            Exception ex = response.getCause();
            if (ex instanceof BizException) {
                BizException bizException = (BizException) ex;
                throw bizException;
            } else {
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.IMPORTPROCESS, ErrorFunctionTypeEnum.PROCESS, ErrorTypeEnum.SYSTEMERROR);
                throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), ex.getMessage());
            }
        }
    }
}
