package com.sgs.customerbiz.app.component.action.order2Trf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sgs.core.domain.UserInfo;
import com.sgs.customerbiz.app.component.action.BaseActionComponent;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.convertor.TrfNoConvertor;
import com.sgs.customerbiz.biz.service.order2trf.cmd.DefaultOrderToTrfActionExtPt;
import com.sgs.customerbiz.context.Order2TRFContext;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.core.util.DateUtils;
import com.sgs.customerbiz.core.util.UserHelper;
import com.sgs.customerbiz.dbstorages.mybatis.model.CustomerTrfInfoPO;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.customerbiz.domain.domainevent.TrfNewEvent;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfHeaderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.enums.TrfActionEnum;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.TrfNoDTO;
import com.sgs.customerbiz.model.trf.dto.req.OrderToTrfReq;
import com.sgs.customerbiz.model.trf.enums.TrfSourceType;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;

import java.util.List;
import java.util.stream.Collectors;

import static com.sgs.customerbiz.core.constants.Constants.*;

@Slf4j
@Component
@LiteflowComponent(id = "defaultOrderToTrfCreateActionComponent", name = "order2TRF默认CreateAction处理器")
public class DefaultOrderToTrfCreateActionComponent extends BaseActionComponent {

    @Resource
    private DefaultOrderToTrfActionExtPt defaultOrderToTrfActionExtPt;
    @Resource
    private IdService idService;

    @Resource
    private TransactionTemplate transactionTemplate;
    @Override
    public void process() throws Exception {

        Order2TRFContext order2TRFContext = this.getContextBean(Order2TRFContext.class);
        OrderToTrfReq orderToTrfReq = order2TRFContext.getOrderToTrfReq();
        validate(orderToTrfReq);
        createTrf(order2TRFContext);
    }

    public void validate(OrderToTrfReq orderToTrfReq) {

    }

    private void createTrf(Order2TRFContext order2TRFContext) {
        OrderToTrfReq orderToTrfReq = order2TRFContext.getOrderToTrfReq();
//        TrfDTO trfDTO = order2TRFContext.getTrfDTO();
//        //buildcreateTrfByOrder
//        TrfDOV2 trfParam = createTrfByOrder(order2TRFContext,orderToTrfReq,trfDTO);
//        //createTrfOrderRel
//        createTrfOrderRel(order2TRFContext,trfParam);

        TrfDOV2 trfParam = transactionTemplate.execute(transactionStatus -> {
            TrfDOV2 trfDOV2 = defaultOrderToTrfActionExtPt.createTrfAndTrfOrderRel(orderToTrfReq);

            return trfDOV2;
        });
        TrfNoDTO trfNoDTO = TrfNoConvertor.toTrfNo(trfParam);
        order2TRFContext.setTrfNoDTO(trfNoDTO);
        //publishTrfConfirmEvent
        TrfEvent event =buildTrfConfirmEvent(trfParam, orderToTrfReq);
        order2TRFContext.getTrfEventList().add(event);
    }

    protected TrfEvent buildTrfConfirmEvent(TrfDOV2 trfParam, OrderToTrfReq orderToTrfReq) {
        TrfEvent event = new TrfNewEvent(TrfEventTriggerBy.PREORDER);
        event.setPayload(trfParam);
        event.setTriggerBy(TrfEventTriggerBy.PREORDER);
        event.setRefSystemId(trfParam.getHeader().getRefSystemId());
        event.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        event.setSystemId(trfParam.getHeader().getSystemId());
        event.setSourceId(orderToTrfReq.getRequestId());
        event.setTrfNo(trfParam.getHeader().getTrfNo());
        event.setEventId(System.currentTimeMillis());
        return event;
    }
    protected void createTrfOrderRel(Order2TRFContext order2TRFContext,TrfDOV2 trfParam) {

        // 先调用bind建立trf和order关系
        //trfDomainService.bind(trfParam.getHeader(), trfParam.getOrder());

        // TODO 依赖confirm cfg配置中orderOpType需要配置成1 2023-12-26
        TrfStatusControlDO trfStatusControlDO = new TrfStatusControlDO();
        trfStatusControlDO.setAction(TrfActionEnum.SYNC_CONFIRMED.getCode());
        trfStatusControlDO.setRefSystemId(trfParam.getHeader().getRefSystemId());
        trfStatusControlDO.setSystemId(trfParam.getOrder().getSystemId());
        trfStatusControlDO.setTrfNo(trfParam.getHeader().getTrfNo());
        trfStatusControlDO.setOrder(trfParam.getOrder());
        order2TRFContext.setTrfStatusControlDO(trfStatusControlDO);
        //TrfStatusResult trfStatusResult = trfDomainService.confirm(trfStatusControlDO);

    }

    private TrfDOV2 createTrfByOrder(Order2TRFContext order2TRFContext,OrderToTrfReq orderToTrfReq, TrfDTO trfDTO) {
        //保存原CustomerTrf信息
        List<CustomerTrfInfoPO> trfs = saveCustomerTRF(orderToTrfReq.getHeader().getTrfNo(), trfDTO.getHeader().getRefSystemId(), JSON.toJSONString(orderToTrfReq.getOrder()), trfDTO.getHeader().getLab().getLabCode());
        order2TRFContext.setTrfs(trfs);
        //保存Trf 信息
        TrfDOV2 trfParam = createSgsTrf(orderToTrfReq, trfDTO, orderToTrfReq.getHeader().getTrfNo());
        order2TRFContext.setTrfDOV2(trfParam);
        return trfParam;
    }
    private TrfDOV2 createSgsTrf(OrderToTrfReq orderToTrfReq, TrfDTO trfDTO, String trfNo) {
        TrfDOV2 trfParam = TrfConvertor.toTrfDOV2(trfDTO);
        TrfHeaderDOV2 headerDO = trfParam.getHeader();
        headerDO.setTrfNo(trfNo);
        headerDO.setSource(TrfSourceType.Order2TRF.getSourceType());
        headerDO.setSystemId(orderToTrfReq.getSystemId());
        headerDO.getLab().setLabId(orderToTrfReq.getLabId());
        //trfDomainService.createTrf(trfParam);
        return trfParam;
    }
    private List <CustomerTrfInfoPO> saveCustomerTRF(String trfNo, Integer refSystemId, String customerTRFJson, String labCode) {
        UserInfo user = UserHelper.getLocalUser();
        if (user == null) {
            user = new UserInfo();
            user.setRegionAccount(USER_DEFAULT);
            user.setName(USER_DEFAULT);
            user.setCurrentLabCode(labCode);
            UserHelper.setLocalUser(user);
        }

        // 保存todolist trf
        List <CustomerTrfInfoPO> trfs = importTrfInfoData(trfNo, refSystemId, customerTRFJson, true);

        return trfs;
    }

    private List<CustomerTrfInfoPO> importTrfInfoData(String trfNo, Integer refSystemId, String customerTrfJson, boolean needLabCode) {
       // CustomResult result = new CustomResult();

        CustomResult<List<CustomerTrfInfoPO>> listCustomResult = this.updateOrInsertTrfInfo(refSystemId, trfNo, customerTrfJson, true);
        if (!listCustomResult.isSuccess()) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.SYSTEM_ERROR, ErrorBizModelEnum.SGSMAARTORDER, ErrorFunctionTypeEnum.CREATESAVE, ErrorTypeEnum.DATANOTMATCH);
            throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(),listCustomResult.getMsg());
        }

        List<CustomerTrfInfoPO> trfs = listCustomResult.getData();
        if (trfs == null || trfs.isEmpty()) {
            throw new BizException("未获取到符合条件的trf信息，请检查数据！");
        }
        List<String> trfNos = trfs.stream().map(CustomerTrfInfoPO::getTrfNo).distinct().collect(Collectors.toList());
        log.info("trfNo:{}需要在tb_trf_todo_info中删除", trfNos);
        return trfs;
    }
    private CustomResult<List<CustomerTrfInfoPO>> updateOrInsertTrfInfo(Integer refSystemId, String trfNo, String customerTrfJson, boolean needLabCode) {
        CustomResult<List<CustomerTrfInfoPO>> rspResult = new CustomResult<>(true);

        UserInfo user = UserHelper.getLocalUser();
        if (user == null) {
            return rspResult.fail("get user fail!");
        }
        String regionAccount = user.getRegionAccount();
        String currentLabCode = user.getCurrentLabCode();
        if (needLabCode && Func.isEmpty(currentLabCode)) {
            return rspResult.fail("labCode not found");
        }

        List<CustomerTrfInfoPO> insertOrUpdate = Lists.newArrayList();

        List<String> errorMsg = Lists.newArrayList();

        JSONObject jsonObject = JSONObject.parseObject(customerTrfJson);

        // 获取模板
        String dffFormId = (String) jsonObject.get(DFF_FORM_ID);
        String gridFormId = (String) jsonObject.get(GRID_FORM_ID);

        CustomerTrfInfoPO customerTrf = new CustomerTrfInfoPO();
        customerTrf.setId(idService.nextId());
        customerTrf.setProductLineCode(ProductLineContextHolder.getProductLineCode());
        customerTrf.setRefSystemId(refSystemId);//客户系统Id,
        customerTrf.setTrfNo(trfNo);
        customerTrf.setObjectNo(trfNo);
        customerTrf.setLabCode(currentLabCode);
        customerTrf.setDffFormId(dffFormId);
        customerTrf.setGridFormId(gridFormId);
        customerTrf.setContent(customerTrfJson != null ? customerTrfJson : null);
        customerTrf.setTrfStatus(com.sgs.framework.model.enums.TrfStatusEnum.ToBeBound.getStatus());

        // 0: inactive, 1: active
        customerTrf.setActiveIndicator(1);
        customerTrf.setCreatedBy(regionAccount);
        customerTrf.setCreatedDate(DateUtils.getNow());
        customerTrf.setModifiedBy(regionAccount);
        customerTrf.setModifiedDate(DateUtils.getNow());

        // 已存在，更新，如果已开单，则不更新;
        insertOrUpdate.add(customerTrf);

        rspResult.setData(insertOrUpdate);

        if (!org.springframework.util.CollectionUtils.isEmpty(errorMsg)) {
            rspResult.setMsg(StringUtils.join(errorMsg, "\r\n"));
            rspResult.setSuccess(false);
        }
        return rspResult;
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) {
        Order2TRFContext order2TRFContext = (Order2TRFContext) requestContext;
        createTrf(order2TRFContext);
        return order2TRFContext;
    }
}
