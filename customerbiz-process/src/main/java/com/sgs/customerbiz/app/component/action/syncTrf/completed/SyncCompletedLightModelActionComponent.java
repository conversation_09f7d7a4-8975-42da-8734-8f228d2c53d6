package com.sgs.customerbiz.app.component.action.syncTrf.completed;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.app.component.action.syncTrf.BaseSyncTRFActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.SyncTrfContextHolder;
import com.sgs.customerbiz.biz.service.synctrf.cmd.SyncCompletedActionExtPt;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.domain.domainevent.TrfActionEvent;
import com.sgs.customerbiz.domain.domainevent.actionevent.TrfSupplementEvent;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfReportDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfOrderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfSyncHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;

@Slf4j
@Component
@LiteflowComponent(id = "syncCompletedLightModelActionComponent", name = "SyncCompleted的Light模式")
public class SyncCompletedLightModelActionComponent extends BaseSyncTRFActionComponent {

    @Autowired
    private SyncCompletedActionExtPt syncCompletedActionExtPt;
    @Resource
    protected TrfDomainService trfDomainService;
    @Autowired
    private ConfigClient configClient;

//    @Override
//    public void process() throws Exception {
//        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
//        doHandle(syncTRFContext);
//    }
//
//    private void doHandle(SyncTRFContext syncTRFContext) {
//        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
//        TrfFullDTO trfDOParam = syncTRFContext.getTrfDOParam();
////        String action = syncReq.getAction();
////
////        TrfInfoPO trfInfoByTrfNo = trfDomainService.getTrfInfoByTrfNo(trfDOParam.getHeader().getTrfNo(), trfDOParam.getHeader().getRefSystemId());
////        Optional.ofNullable(trfInfoByTrfNo).map(TrfInfoPO::getSystemId).ifPresent(systemId -> {
////            trfDOParam.getReportList().forEach(r -> r.setSystemId(systemId.longValue()));
////        });
////        trfDomainService.createTrfReport(trfInfoByTrfNo, JSONObject.parseArray(JSONObject.toJSONString(trfDOParam.getReportList()), TrfReportDOV2.class));
////        String productLineCode = Func.isEmpty(ProductLineContextHolder.getProductLineCode()) ? trfDOParam.getProductLineCode() : ProductLineContextHolder.getProductLineCode();
////        TrfActionEvent event = buildEvent(trfDOParam, productLineCode);
////        syncTRFContext.getTrfActionEventList().add(event);
//        syncCompletedActionExtPt.processByAction(trfDOParam);
//    }

    @Override
    public boolean processByAction(TrfFullDTO trfDOParam, TrfStatusControlDO trfStatusControlDO, TrfSyncHeaderDTO syncHeader) {
        return  syncCompletedActionExtPt.processByAction(trfDOParam, trfStatusControlDO, syncHeader);
    }

    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
        return null;
    }

    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {

    }

}
