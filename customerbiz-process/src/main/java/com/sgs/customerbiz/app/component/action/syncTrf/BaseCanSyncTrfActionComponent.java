package com.sgs.customerbiz.app.component.action.syncTrf;

import com.sgs.customerbiz.app.component.action.BaseActionComponent;
import com.sgs.customerbiz.biz.service.synctrf.cmd.BaseSyncTrfAction;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.customerbiz.domain.domainevent.TrfPendingEvent;
import com.sgs.customerbiz.domain.domainevent.TrfUnPendingEvent;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfSyncHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.enums.PendingFlagEnum;
import lombok.extern.slf4j.Slf4j;

@Slf4j
abstract public class BaseCanSyncTrfActionComponent extends BaseActionComponent {


    private final BaseSyncTrfAction baseSyncTrfAction;

    protected BaseCanSyncTrfActionComponent(BaseSyncTrfAction baseSyncTrfAction) {
        this.baseSyncTrfAction = baseSyncTrfAction;
    }


    public boolean processByAction(TrfFullDTO trfDOParam, TrfStatusControlDO trfStatusControlDO, TrfSyncHeaderDTO syncHeader) {
        return true;
    }

    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = (SyncTRFContext) requestContext;
        doBaseProcess(syncTRFContext);
        return syncTRFContext;
    }

    @Override
    public void process() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        doBaseProcess(syncTRFContext);
    }

    private void doBaseProcess(SyncTRFContext syncTRFContext) {
        TrfFullDTO trfDOParam = syncTRFContext.getTrfDOParam();
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        TrfStatusControlDO trfStatusControlDO = syncTRFContext.getTrfStatusControlDO();
        boolean processByAction = processByAction(trfDOParam, trfStatusControlDO, syncReq.getHeader());
        if (!processByAction) {
            syncTRFContext.setReturn(true);
            return;
        }
        baseSyncTrfAction.checkHostModelAndValidate(syncReq);
        baseSyncTrfAction.checkBizRule(syncReq, trfDOParam);
        doCheck(syncTRFContext);
    }

    protected TrfEvent newTrfPendingEvent(Integer pendingFlag) {
        if (PendingFlagEnum.check(pendingFlag, PendingFlagEnum.Pending)) {
            return new TrfPendingEvent(TrfEventTriggerBy.PREORDER);
        } else {
            return new TrfUnPendingEvent(TrfEventTriggerBy.PREORDER);
        }
    }

    public abstract void doCheck(SyncTRFContext syncTRFContext);

}
