package com.sgs.customerbiz.app.component.action.event;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.app.component.action.BaseActionComponent;
import com.sgs.customerbiz.biz.event.EventUtils;
import com.sgs.customerbiz.biz.service.synctrf.SyncTrfContextHolder;
import com.sgs.customerbiz.biz.service.synctrf.cmd.BaseSyncTrfAction;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.domain.domainevent.TrfActionEvent;
import com.sgs.customerbiz.domain.domainevent.TrfConfirmedEvent;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.customerbiz.domain.domainevent.actionevent.TrfSupplementEvent;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfOrderDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.Func;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;

@Slf4j
@Component
@LiteflowComponent(id = "syncTRFEventNotifyActionComponent", name = "SyncTRF消息事件通知组件")
public class SyncTRFEventNotifyActionComponent extends BaseActionComponent {

    @Autowired
    protected BaseSyncTrfAction baseSyncTrfAction;
    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        publishEvent(syncTRFContext);
        return syncTRFContext;
    }

    @Override
    public void process() throws Exception {
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        publishEvent(syncTRFContext);
    }

    private void publishEvent(SyncTRFContext syncTRFContext) {
        TrfSyncReq syncReq = syncTRFContext.getSyncReq();
        TrfStatusResult trfStatusResult = syncTRFContext.getTrfStatusResult();
        TrfInfoPO trfInfoPO = syncTRFContext.getTrfInfoPO();
        TrfFullDTO trfDOParam = syncTRFContext.getTrfDOParam();
        TrfStatusControlDO trfStatusControlDO = syncTRFContext.getTrfStatusControlDO();
        String action = syncReq.getAction();
        // 有部分syncTrfAction会直接自己做消息通知，所以这里要判断一下
        if(Func.isNotEmpty(trfStatusResult)&&!syncTRFContext.isReturn()) {
            baseSyncTrfAction.publishEvent(syncReq, trfStatusResult, action, trfInfoPO, trfDOParam, trfStatusControlDO);
        }
    }


}
