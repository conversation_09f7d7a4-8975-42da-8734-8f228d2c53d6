package com.sgs.customerbiz.app.record;

import lombok.Data;

import java.util.Date;

@Data
public class ProcessFlowStep {

    private Long flowId;
    private Integer sort;
    private String nodeId;
    private String nodeName;
    private String chainId;
    private String className;
    private String status;
    private String tag;
    private String stepType;
    private Date startTime;
    private Date endTime;
    private Long timeCost;
}
