package com.sgs.customerbiz.app.component.convert;

import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.convertor.TrfSyncConvertor;
import com.sgs.customerbiz.context.RequestContext;
import com.sgs.customerbiz.context.SyncTRFContext;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@LiteflowComponent(id = "syncTRFConvertComponent", name = "SyncTRFConvert数据转换组件")
public class SyncTRFConvertComponent extends BaseConvertComponent{
    @Override
    public RequestContext testComponent(RequestContext requestContext) throws Exception {
        SyncTRFContext syncTRFContext = (SyncTRFContext)requestContext;
        TrfHeaderDTO trfHeaderDTO = this.getCurrLoopObj();
        syncTRFContext.setTrfHeaderDTO(trfHeaderDTO);
        convert(syncTRFContext);
        return syncTRFContext;
    }

    @Override
    public void process() throws Exception {
        // TODO Auto-generated method stub
        SyncTRFContext syncTRFContext = this.getContextBean(SyncTRFContext.class);
        TrfHeaderDTO trfHeaderDTO = this.getCurrLoopObj();
        syncTRFContext.setTrfHeaderDTO(trfHeaderDTO);
        convert(syncTRFContext);
    }

    private void convert(SyncTRFContext requestContext) {
        TrfHeaderDTO trfHeaderDTO = requestContext.getTrfHeaderDTO();
        TrfSyncReq syncReq = requestContext.getSyncReq();

        TrfFullDTO trfDOParam = TrfConvertor.toTrfFullDTO(trfHeaderDTO, syncReq);
        TrfStatusControlDO trfStatusControlDO = TrfSyncConvertor.toTrfStatusControlDO(trfHeaderDTO, syncReq);
        requestContext.setTrfDOParam(trfDOParam);
        requestContext.setTrfStatusControlDO(trfStatusControlDO);

    }
}
