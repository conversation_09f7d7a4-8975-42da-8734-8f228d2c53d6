package com.sgs.customerbiz.context;

import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.model.trf.dto.TrfDTO;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.TrfImportReq;
import com.sgs.framework.core.base.BaseResponse;
import lombok.Data;

@Data
public class ImportToTRFContext extends RequestContext{

    private TrfImportReq trfImportReq;

    private String customerTrf;
    private TrfDOV2 trfDOV2;
    private TrfDTO sgsTRF;
    private BaseResponse<JSONObject> rspResult;
    private TrfImportResult result;
}
