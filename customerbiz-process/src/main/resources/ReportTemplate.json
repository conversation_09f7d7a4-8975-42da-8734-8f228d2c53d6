[{"operation": "shift", "spec": {"reportId": "id.reportId", "rootReportNo": "id.rootReportNo", "originalReportNo": "id.parentReportNo", "reportNo": "id.reportNo", "reportInstanceId": "id.external.externalReportId", "oldReportNo": "id.external.externalReportNo", "reportDueDate": "header.reportDueDate", "reportStatus": "header.reportStatus", "certificateName": "header.certificateName", "conclusion": {"failCode": "conclusion.conclusionCode", "customerConclusion": "conclusion.customerConclusion", "reviewConclusion": "conclusion.reviewConclusion", "conclusionRemark": "conclusion.conclusionRemark"}, "reportMatrixList": {"*": {"testLineInstanceId": "testMatrixList[&1].testLine.testLineInstanceId", "testLineId": "testMatrixList[&1].testLine.testLineId", "testLineVersionId": "testMatrixList[&1].testLine.testLineVersionId", "evaluationAlias": "testMatrixList[&1].testLine.evaluationAlias", "evaluationName": "testMatrixList[&1].testLine.evaluationName", "citation": {"citationType": "testMatrixList[&1].testLine.citation.citationType", "citationName": "testMatrixList[&1].testLine.citation.citationName"}, "testSample": {"testSampleInstanceId": "testMatrixList[&1].testSample.testSampleInstanceId", "parentTestSampleId": "testMatrixList[&1].testSample.parentTestSampleId", "testSampleType": "testMatrixList[&1].testSample.testSampleType", "category": "testMatrixList[&1].testSample.category", "testSampleSeq": "testMatrixList[&1].testSample.testSampleSeq", "testSampleGroupList": {"*": {"testSampleInstanceId": "testMatrixList[&1].testSample.testSampleGroupList[&1].testSampleInstanceId", "mainSampleFlag": "testMatrixList[&1].testSample.testSampleGroupList[&1].mainSampleFlag"}}}}}}}]