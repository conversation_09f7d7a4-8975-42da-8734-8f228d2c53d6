package com.sgs.customerbiz.facade.impl;

import com.sgs.customerbiz.app.process.CanSyncTrfProcess;
import com.sgs.customerbiz.app.router.ProcessRouterService;
import com.sgs.customerbiz.biz.service.InspectorioBizService;
import com.sgs.customerbiz.biz.service.preview.PreviewSyncReportService;
import com.sgs.customerbiz.biz.service.SciTrfBizService;
import com.sgs.customerbiz.biz.service.SyncReviewConclusionService;
import com.sgs.customerbiz.model.trf.dto.resp.TestLinePreviewDTO;
import com.sgs.customerbiz.biz.service.todolist.TrfService;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.facade.ITrfFacade;
import com.sgs.customerbiz.facade.model.dto.BoundTrfRelDTO;
import com.sgs.customerbiz.facade.model.todolist.req.*;
import com.sgs.customerbiz.facade.model.trf.req.BoundTrfInfoSearchReq;
import com.sgs.customerbiz.facade.model.trf.req.CustomerTrfInfoReq;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.*;
import com.sgs.customerbiz.model.trf.dto.resp.CustomerConfirmReportFlag;
import com.sgs.customerbiz.model.trf.enums.IdentityEnum;
import com.sgs.framework.core.base.BaseResponse;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 */
@Component("trfFacade")
public class TrfFacadeImpl implements ITrfFacade {
    private final TrfService trfService;

    private final SciTrfBizService sciTrfBizService;

    private final PreviewSyncReportService previewSyncReportService;

    private final TrfDomainService trfDomainService;

    private final ProcessRouterService processRouterService;

    private final InspectorioBizService inspectorioBizService;

    private final SyncReviewConclusionService syncReviewConclusionService;

    private final CanSyncTrfProcess canSyncTrfProcess;

    public TrfFacadeImpl(TrfService trfService,
                         SciTrfBizService sciTrfBizService, PreviewSyncReportService previewSyncReportService,
                         TrfDomainService trfDomainService,
                         ProcessRouterService processRouterService,
                         InspectorioBizService inspectorioBizService,
                         SyncReviewConclusionService syncReviewConclusionService, CanSyncTrfProcess canSyncTrfProcess) {
        this.trfService = trfService;
        this.sciTrfBizService = sciTrfBizService;
        this.previewSyncReportService = previewSyncReportService;
        this.trfDomainService = trfDomainService;
        this.processRouterService = processRouterService;
        this.inspectorioBizService = inspectorioBizService;
        this.syncReviewConclusionService = syncReviewConclusionService;
        this.canSyncTrfProcess = canSyncTrfProcess;
    }


    @Override
    public BaseResponse rejectInspectorio(RejectInspectorio reject) {
        return BaseResponse.newInstance(trfService.rejectInspectorio(reject));
    }

    /**
     * Remove Trf
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse remove(RemoveTrfReq reqObject) {
        return BaseResponse.newInstance(trfService.returnTrf(reqObject));
    }

    /**
     * Cancel Trf
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse cancel(CancelTrfReq reqObject) {
        return BaseResponse.newInstance(trfService.cancelTrf(reqObject));
    }

    /**
     * UnDisplay Trf
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse unDisplay(TrfDisplayReq reqObject) {
        return BaseResponse.newInstance(trfService.unDisplayTrf(reqObject));
    }

    /**
     * display Trf
     *
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse display(TrfDisplayReq reqObject) {
        return BaseResponse.newInstance(trfService.displayTrf(reqObject));
    }

    /**
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse<List<CustomerTrfInfoRsp>> getTrfInfoList(CustomerTrfInfoReq reqObject) {
        return BaseResponse.newInstance(trfService.getTrfInfoList(reqObject));
    }

    /**
     * @param reqObject
     * @return
     */
    @Override
    public BaseResponse<List<BoundTrfRelDTO>> getBoundTrfInfoList(BoundTrfInfoSearchReq reqObject) {
        return BaseResponse.newInstance(trfDomainService.getBoundTrfInfoList(reqObject));
    }

    @Override
    public BaseResponse<List<CustomerTrfInfoRsp>> getCustomerTrfInfoList(List<String> trfNos, List<String> packageBarcodes) {
        return BaseResponse.newInstance(trfService.getCustomerTrfInfoList(trfNos, packageBarcodes));
    }

    @Override
    public BaseResponse getRemoveCancelData(RemoveCancelReq reqObject) {
        return BaseResponse.newInstance(trfService.getRemoveCancelData(reqObject));
    }

    @Override
    public BaseResponse<TrfImportResult> importTrf(TrfImportReq importReq) {
        return BaseResponse.newInstance(processRouterService.importToTrfRouter(importReq));
    }

    @Override
    public BaseResponse<TrfHeaderDTO> orderToTrf(OrderToTrfReq orderToTrfReq) {
        return BaseResponse.newInstance(processRouterService.orderToTrfRouter(orderToTrfReq));
    }

    @Override
    public BaseResponse<TrfHeaderDTO> syncTrf(TrfSyncReq syncReq) {
        return BaseResponse.newInstance(processRouterService.syncToTrfRouter(syncReq));
    }

    @Override
    public BaseResponse<TrfHeaderDTO> syncReportToTrf(TrfSyncReq syncReq) {
        return BaseResponse.newInstance(sciTrfBizService.syncReportToTrf(syncReq));
    }

    @Override
    public BaseResponse syncReviewConclusion(SyncReportToTrfReq req) {
        return BaseResponse.newInstance(syncReviewConclusionService.syncReviewConclusion(req));
    }

    @Override
    public BaseResponse getCustomerInfo(CustomerTrfReq req) {
        return BaseResponse.newInstance(processRouterService.getCustomerInfo(req));
    }

    @Override
    public BaseResponse<CustomerConfirmReportFlag> confirmReport(CustomerConfirmedReport confirmedReport) {
        return BaseResponse.of(sciTrfBizService.confirmReport(confirmedReport));
    }

    @Override
    public BaseResponse<CustomerConfirmReportTrf<CustomerConfirmReportFlag>> queryReportConfirmFlag(CustomerConfirmReportTrf<ReportNo> queryCustomerConfirmReport) {
        return BaseResponse.of(sciTrfBizService.queryReportConfirmFlag(queryCustomerConfirmReport));
    }

    @Override
    public BaseResponse<Boolean> canReviseReport(TrfSyncReq syncReq) {
        return BaseResponse.of(canSyncTrfProcess.doProcess(syncReq));
    }

    @Override
    public BaseResponse<List<TestLinePreviewDTO>> previewSyncReport(TrfSyncReq syncReq) {
        return BaseResponse.of(previewSyncReportService.preview(syncReq));
    }

    @Override
    public BaseResponse unbindTrf(TrfUnbindReq unbindReq) {
        return BaseResponse.newInstance(sciTrfBizService.unbindTrf(unbindReq));
    }

    @Override
    public BaseResponse cancelByCustomer(TrfCancelReq reqObject) {
        return BaseResponse.newInstance(sciTrfBizService.cancelTrf(reqObject, IdentityEnum.CUSTOMER));
    }

    @Override
    public BaseResponse cancelByPreOrder(TrfCancelReq reqObject) {
        return BaseResponse.newInstance(sciTrfBizService.cancelTrf(reqObject, IdentityEnum.PREORDER));
    }

    @Override
    public BaseResponse returnByCustomer(TrfRemoveReq reqObject) {
        return BaseResponse.newInstance(sciTrfBizService.returnTrf(reqObject, IdentityEnum.CUSTOMER));
    }

    @Override
    public BaseResponse returnByPreOrder(TrfRemoveReq reqObject) {
        return BaseResponse.newInstance(sciTrfBizService.returnTrf(reqObject, IdentityEnum.PREORDER));
    }

    @Override
    public BaseResponse bindTrf(TrfBindReq bindReq) {
        return BaseResponse.newInstance(processRouterService.bindTrf(bindReq));
    }

    @Override
    public BaseResponse<TrfImportResult> exportSgsTrfByTrfNo(TrfImportReq trfImportReq) {
        return BaseResponse.newInstance(sciTrfBizService.exportSgsTrfByTrfNo(trfImportReq));
    }

    @Override
    public BaseResponse<TrfImportResult> getSgsTrfByTrfNo(String trfNo) {
        return BaseResponse.newInstance(sciTrfBizService.getSgsTrfByTrfNo(trfNo));
    }

    @Override
    public BaseResponse getConfig(GetConfigReq getConfigReq) {
        return BaseResponse.newInstance(sciTrfBizService.getConfig(getConfigReq));
    }

    @Override
    public BaseResponse syncQuotationToTrf(TrfSyncQuotationReq trfSyncQuotationReq) {
        return BaseResponse.newInstance(sciTrfBizService.syncQuotationToTrf(trfSyncQuotationReq));
    }

    @Override
    public BaseResponse sendQuotationStatus(TrfSyncQuotationStatusReq reqObject) {
        return BaseResponse.newInstance(sciTrfBizService.sendQuotationStatus(reqObject));
    }

    @Override
    public BaseResponse getCustomerConfig(GetCustomerConfigReq getConfigReq) {
        return BaseResponse.newInstance(sciTrfBizService.getCustomerConfig(getConfigReq));
    }




}
