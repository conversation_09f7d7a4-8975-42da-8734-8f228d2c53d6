package com.sgs.customerbiz.validation.service;

import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.validation.service.config.LowesMaker;
import org.junit.jupiter.api.Test;

import java.io.IOException;

class LowesValidationTestWithSpring extends ValidationTestWithSpring {


    @Test void testInterfaceValidate() throws IOException {
        Object data = getInterfaceDataByClasspath();
        validationService.validate(LowesMaker.getLowesValidationConfigs(), ValidationTestUtils.getValidationData(data)).forEach(System.out::println);
    }

    @Test void testSyncTrfValidate() throws IOException {
        TrfSyncReq request = getSyncTrfDataByClasspath();
        validationService.validate(LowesMaker.getLowesValidationConfigs(), ValidationTestUtils.getSyncTrfValidationData(request)).forEach(System.out::println);
    }

    @Test void testCollectDataValidate() throws IOException {
        JSONObject jsonObject = getCollectedDataByClasspath();
        validationService.validate(LowesMaker.getLowesValidationConfigs(), ValidationTestUtils.getValidationData(jsonObject)).forEach(System.out::println);
    }

    public static void main(String[] args) throws IOException {
        // interface
        JSONObject data = ValidationTestUtils.getInterfaceDataFromPath();
        validateLowes(ValidationTestUtils.getValidationData(data));

        // sync trf
        TrfSyncReq syncTrfRequest = ValidationTestUtils.getSyncTrfDataFromPath();
        validateLowes(ValidationTestUtils.getSyncTrfValidationData(syncTrfRequest));

        // collect data
        JSONObject collectData = ValidationTestUtils.getCollectedDataFromPath();
        validateLowes(ValidationTestUtils.getValidationData(collectData));
    }

    private static void validateLowes(ValidationData validationData) {
        ValidationTestUtils.validate(validationData, LowesMaker.getLowesValidationConfigs()).forEach(System.out::println);
    }

}
