<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.customerbiz.dfv.dbstorages.mybatis.mapper.DfvFunctionFieldMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.customerbiz.dfv.dbstorages.mybatis.model.DfvFunctionFieldPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="system_id" property="systemId" jdbcType="INTEGER" />
    <result column="function_id" property="functionId" jdbcType="BIGINT" />
    <result column="full_field_path" property="fullFieldPath" jdbcType="VARCHAR" />
    <result column="field_path" property="fieldPath" jdbcType="VARCHAR" />
    <result column="field_code" property="fieldCode" jdbcType="VARCHAR" />
    <result column="field_type" property="fieldType" jdbcType="VARCHAR" />
    <result column="label_name" property="labelName" jdbcType="VARCHAR" />
    <result column="sort_num" property="sortNum" jdbcType="INTEGER" />
    <result column="parent_id" property="parentId" jdbcType="BIGINT" />
    <result column="active_indicator" property="activeIndicator" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, system_id, function_id, full_field_path, field_path, field_code, field_type, 
    label_name, sort_num, parent_id, active_indicator, created_by, created_date, modified_by, 
    modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.customerbiz.dfv.dbstorages.mybatis.model.DfvFunctionFieldExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_dfv_func_field
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_dfv_func_field
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_dfv_func_field
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.customerbiz.dfv.dbstorages.mybatis.model.DfvFunctionFieldExample" >
    delete from tb_dfv_func_field
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.customerbiz.dfv.dbstorages.mybatis.model.DfvFunctionFieldPO" >
    insert into tb_dfv_func_field (id, system_id, function_id, 
      full_field_path, field_path, field_code, 
      field_type, label_name, sort_num, 
      parent_id, active_indicator, created_by, 
      created_date, modified_by, modified_date
      )
    values (#{id,jdbcType=BIGINT}, #{systemId,jdbcType=INTEGER}, #{functionId,jdbcType=BIGINT}, 
      #{fullFieldPath,jdbcType=VARCHAR}, #{fieldPath,jdbcType=VARCHAR}, #{fieldCode,jdbcType=VARCHAR}, 
      #{fieldType,jdbcType=VARCHAR}, #{labelName,jdbcType=VARCHAR}, #{sortNum,jdbcType=INTEGER}, 
      #{parentId,jdbcType=BIGINT}, #{activeIndicator,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, #{modifiedDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.customerbiz.dfv.dbstorages.mybatis.model.DfvFunctionFieldPO" >
    insert into tb_dfv_func_field
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="systemId != null" >
        system_id,
      </if>
      <if test="functionId != null" >
        function_id,
      </if>
      <if test="fullFieldPath != null" >
        full_field_path,
      </if>
      <if test="fieldPath != null" >
        field_path,
      </if>
      <if test="fieldCode != null" >
        field_code,
      </if>
      <if test="fieldType != null" >
        field_type,
      </if>
      <if test="labelName != null" >
        label_name,
      </if>
      <if test="sortNum != null" >
        sort_num,
      </if>
      <if test="parentId != null" >
        parent_id,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="systemId != null" >
        #{systemId,jdbcType=INTEGER},
      </if>
      <if test="functionId != null" >
        #{functionId,jdbcType=BIGINT},
      </if>
      <if test="fullFieldPath != null" >
        #{fullFieldPath,jdbcType=VARCHAR},
      </if>
      <if test="fieldPath != null" >
        #{fieldPath,jdbcType=VARCHAR},
      </if>
      <if test="fieldCode != null" >
        #{fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="fieldType != null" >
        #{fieldType,jdbcType=VARCHAR},
      </if>
      <if test="labelName != null" >
        #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="sortNum != null" >
        #{sortNum,jdbcType=INTEGER},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.customerbiz.dfv.dbstorages.mybatis.model.DfvFunctionFieldExample" resultType="java.lang.Integer" >
    select count(*) from tb_dfv_func_field
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_dfv_func_field
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.systemId != null" >
        system_id = #{record.systemId,jdbcType=INTEGER},
      </if>
      <if test="record.functionId != null" >
        function_id = #{record.functionId,jdbcType=BIGINT},
      </if>
      <if test="record.fullFieldPath != null" >
        full_field_path = #{record.fullFieldPath,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldPath != null" >
        field_path = #{record.fieldPath,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldCode != null" >
        field_code = #{record.fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldType != null" >
        field_type = #{record.fieldType,jdbcType=VARCHAR},
      </if>
      <if test="record.labelName != null" >
        label_name = #{record.labelName,jdbcType=VARCHAR},
      </if>
      <if test="record.sortNum != null" >
        sort_num = #{record.sortNum,jdbcType=INTEGER},
      </if>
      <if test="record.parentId != null" >
        parent_id = #{record.parentId,jdbcType=BIGINT},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_dfv_func_field
    set id = #{record.id,jdbcType=BIGINT},
      system_id = #{record.systemId,jdbcType=INTEGER},
      function_id = #{record.functionId,jdbcType=BIGINT},
      full_field_path = #{record.fullFieldPath,jdbcType=VARCHAR},
      field_path = #{record.fieldPath,jdbcType=VARCHAR},
      field_code = #{record.fieldCode,jdbcType=VARCHAR},
      field_type = #{record.fieldType,jdbcType=VARCHAR},
      label_name = #{record.labelName,jdbcType=VARCHAR},
      sort_num = #{record.sortNum,jdbcType=INTEGER},
      parent_id = #{record.parentId,jdbcType=BIGINT},
      active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.customerbiz.dfv.dbstorages.mybatis.model.DfvFunctionFieldPO" >
    update tb_dfv_func_field
    <set >
      <if test="systemId != null" >
        system_id = #{systemId,jdbcType=INTEGER},
      </if>
      <if test="functionId != null" >
        function_id = #{functionId,jdbcType=BIGINT},
      </if>
      <if test="fullFieldPath != null" >
        full_field_path = #{fullFieldPath,jdbcType=VARCHAR},
      </if>
      <if test="fieldPath != null" >
        field_path = #{fieldPath,jdbcType=VARCHAR},
      </if>
      <if test="fieldCode != null" >
        field_code = #{fieldCode,jdbcType=VARCHAR},
      </if>
      <if test="fieldType != null" >
        field_type = #{fieldType,jdbcType=VARCHAR},
      </if>
      <if test="labelName != null" >
        label_name = #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="sortNum != null" >
        sort_num = #{sortNum,jdbcType=INTEGER},
      </if>
      <if test="parentId != null" >
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.customerbiz.dfv.dbstorages.mybatis.model.DfvFunctionFieldPO" >
    update tb_dfv_func_field
    set system_id = #{systemId,jdbcType=INTEGER},
      function_id = #{functionId,jdbcType=BIGINT},
      full_field_path = #{fullFieldPath,jdbcType=VARCHAR},
      field_path = #{fieldPath,jdbcType=VARCHAR},
      field_code = #{fieldCode,jdbcType=VARCHAR},
      field_type = #{fieldType,jdbcType=VARCHAR},
      label_name = #{labelName,jdbcType=VARCHAR},
      sort_num = #{sortNum,jdbcType=INTEGER},
      parent_id = #{parentId,jdbcType=BIGINT},
      active_indicator = #{activeIndicator,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_dfv_func_field
      (`id`,`system_id`,`function_id`,
      `full_field_path`,`field_path`,`field_code`,
      `field_type`,`label_name`,`sort_num`,
      `parent_id`,`active_indicator`,`created_by`,
      `created_date`,`modified_by`,`modified_date`
      )
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.systemId, jdbcType=INTEGER},#{ item.functionId, jdbcType=BIGINT},
      #{ item.fullFieldPath, jdbcType=VARCHAR},#{ item.fieldPath, jdbcType=VARCHAR},#{ item.fieldCode, jdbcType=VARCHAR},
      #{ item.fieldType, jdbcType=VARCHAR},#{ item.labelName, jdbcType=VARCHAR},#{ item.sortNum, jdbcType=INTEGER},
      #{ item.parentId, jdbcType=BIGINT},#{ item.activeIndicator, jdbcType=INTEGER},#{ item.createdBy, jdbcType=VARCHAR},
      #{ item.createdDate, jdbcType=TIMESTAMP},#{ item.modifiedBy, jdbcType=VARCHAR},#{ item.modifiedDate, jdbcType=TIMESTAMP}
      ) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_dfv_func_field 
      <set>
        <if test="item.systemId != null"> 
          `system_id` = #{item.systemId, jdbcType = INTEGER},
        </if> 
        <if test="item.functionId != null"> 
          `function_id` = #{item.functionId, jdbcType = BIGINT},
        </if> 
        <if test="item.fullFieldPath != null"> 
          `full_field_path` = #{item.fullFieldPath, jdbcType = VARCHAR},
        </if> 
        <if test="item.fieldPath != null"> 
          `field_path` = #{item.fieldPath, jdbcType = VARCHAR},
        </if> 
        <if test="item.fieldCode != null"> 
          `field_code` = #{item.fieldCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.fieldType != null"> 
          `field_type` = #{item.fieldType, jdbcType = VARCHAR},
        </if> 
        <if test="item.labelName != null"> 
          `label_name` = #{item.labelName, jdbcType = VARCHAR},
        </if> 
        <if test="item.sortNum != null"> 
          `sort_num` = #{item.sortNum, jdbcType = INTEGER},
        </if> 
        <if test="item.parentId != null"> 
          `parent_id` = #{item.parentId, jdbcType = BIGINT},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = INTEGER},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>