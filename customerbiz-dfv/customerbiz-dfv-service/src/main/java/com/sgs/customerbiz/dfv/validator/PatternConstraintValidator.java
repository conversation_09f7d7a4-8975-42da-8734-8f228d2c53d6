package com.sgs.customerbiz.dfv.validator;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.sgs.customerbiz.dfv.api.dto.ConstraintViolationDTO;
import com.sgs.customerbiz.dfv.enums.ConstraintType;
import com.sgs.framework.core.util.SpringUtil;
import com.sgs.framework.i18n.util.MessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class PatternConstraintValidator extends AbstractParamConstraintValidator {
    private String pattern;

    public static final String THE_DATA_MUST_BE_VALID = "the.data.must.be.valid";

    @Override
    public void initialize(String constraintValue1, String constraintValue2, ConstraintContext context) {
        this.pattern = constraintValue1;
        validateParameters(context);
    }

    @Override
    public ConstraintViolationDTO execute(Object value, ConstraintContext context) {
        if (Objects.isNull(value)) {
            return null;
        }
        if (value instanceof CharSequence) {
            if (Objects.equals(value, "")) {
                return null;
            }
        }
        String str = String.valueOf(value);
        if (!ReUtil.isMatch(this.pattern, (CharSequence) str)) {
            Object[] args = {context.getFullFieldPath(), value};
            return buildViolation(THE_DATA_MUST_BE_VALID, value,args, context);
        }

        return null;
//            throw new IllegalArgumentException(context.getFullFieldPath() + "必须是String类型");

    }


    @Override
    public String getType() {
        return ConstraintType.Pattern.name();
    }


    private void validateParameters(ConstraintContext context) {
        if (StrUtil.isEmpty(pattern)) {
            throw new IllegalArgumentException(StrUtil.format("{} {}参数不能为空", context.getFullFieldPath(), getType()));
        }
    }

    public String getMessage(String fullFieldPath) {
        return StrUtil.format("The {} format incorrect", fullFieldPath);
    }

    @Override
    public String getMessage(String i18nCode, Object[] args) {
        MessageUtil bean = SpringUtil.getBean(MessageUtil.class);
        return bean.get(i18nCode, args);
    }


}
