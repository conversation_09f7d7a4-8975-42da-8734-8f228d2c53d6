package com.sgs.customerbiz.dfv.validator;

import cn.hutool.core.util.StrUtil;
import com.sgs.customerbiz.dfv.api.dto.ConstraintViolationDTO;
import com.sgs.customerbiz.dfv.enums.ConstraintType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * 验证属性必须日期类型，属性值必须是未来的一个时间点
 * <p>
 * 当前支持的日期类型
 * <ul>
 *     <li>java.util.Date</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 */
@Component
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class FutureConstraintValidator extends AbstractConstraintValidator {

    @Override
    public ConstraintViolationDTO execute(Object value, ConstraintContext context) {
        if (Objects.isNull(value)) {
            return null;
        }
        if (value instanceof Date) {
            Date referenceDate = referenceDate();
            Date requestDate = ((Date) value);
            if (!requestDate.after(referenceDate)) {
                return buildViolation(value, context);
            }
        } else {
            throw new IllegalArgumentException(context.getFullFieldPath() + "必须是日期类型");
        }
        return null;
    }

    private Date referenceDate() {
        return new Date();
    }

    @Override
    public String getType() {
        return ConstraintType.Future.name();
    }

    @Override
    public String getMessage(String fullFieldPath) {
        return StrUtil.format("The {} must be a future date", fullFieldPath);
    }

    @Override
    public String getMessage(String i18nCode, Object[] args) {
        return null;
    }
}
