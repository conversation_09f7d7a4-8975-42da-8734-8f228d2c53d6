package com.sgs.customerbiz.dfv.api.request;

import com.sgs.framework.core.base.BaseRequest;

/**
 * <AUTHOR>
 */
public class DfvValidateRequest extends BaseRequest {
    private Integer systemId;


    private String templateCode;

    private Object context;

    public Integer getSystemId() {
        return systemId;
    }

    public void setSystemId(Integer systemId) {
        this.systemId = systemId;
    }


    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public Object getContext() {
        return context;
    }

    public void setContext(Object context) {
        this.context = context;
    }


}
