package com.sgs.config.impl.service.impl;

import com.sgs.config.api.service.SystemAPIConfigService;
import com.sgs.config.api.dto.ConfigInfo;
import com.sgs.config.api.dto.SystemApiDTO;
import com.sgs.config.api.dto.req.ConfigGetReq;
import com.sgs.config.api.service.ConfigService;
import com.sgs.config.impl.manager.SystemApiManager;
import com.sgs.customerbiz.dbstorages.mybatis.model.SystemApiPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Desc
 * <AUTHOR>
 * @date 2023/12/7 11:42
 */
@Slf4j
@Service
public class SystemAPIConfigServiceImpl implements SystemAPIConfigService {

    @Resource
    private SystemApiManager systemApiManager;

    @Resource
    private ConfigService configService;

    @Override
    public SystemApiDTO getRequestApiInfo(Integer systemId, Long apiId) {
        SystemApiPO systemApiPO = systemApiManager.findBySystemIdAndApiId(systemId, apiId);
        if (systemApiPO == null) {
            log.warn("systemId:{} ,api:{} not existed.", systemId, apiId);
            return null;
        }

        SystemApiDTO systemApi = new SystemApiDTO();
        BeanUtils.copyProperties(systemApiPO, systemApi);
        return systemApi;
    }

    public SystemApiDTO getRequestApiInfo(Long apiId) {
        SystemApiPO systemApiPO = systemApiManager.findByApiId(apiId);
        if (systemApiPO == null) {
            log.warn("api:{} not existed.", apiId);
            return null;
        }

        SystemApiDTO systemApi = new SystemApiDTO();
        BeanUtils.copyProperties(systemApiPO, systemApi);
        return systemApi;
    }

    @Override
    public SystemApiDTO getImportApiInfo(Integer refSystemId) {
        ConfigGetReq getReq = new ConfigGetReq();
        getReq.setIdentityId(String.valueOf(refSystemId));
        getReq.setConfigKey("REFSYSTEM.API.IMPORT");
        ConfigInfo configInfo = configService.getConfig(getReq);

        if (configInfo == null) {
            return null;
        }

        return getRequestApiInfo(refSystemId, Long.valueOf(configInfo.getConfigValue()));
    }

    @Override
    public SystemApiDTO getOrderToTrfApiInfo(Integer refSystemId) {
        ConfigGetReq getReq = new ConfigGetReq();
        getReq.setIdentityId(String.valueOf(refSystemId));
        getReq.setConfigKey("REFSYSTEM.API.ORDERTOTRF");
        ConfigInfo configInfo = configService.getConfig(getReq);

        if (configInfo == null) {
            return null;
        }

        return getRequestApiInfo(refSystemId, Long.valueOf(configInfo.getConfigValue()));
    }
}
