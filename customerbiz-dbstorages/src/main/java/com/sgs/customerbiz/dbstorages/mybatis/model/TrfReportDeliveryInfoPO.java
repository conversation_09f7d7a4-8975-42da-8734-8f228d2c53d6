package com.sgs.customerbiz.dbstorages.mybatis.model;

import java.util.Date;

public class TrfReportDeliveryInfoPO {
    /**
     * report_no VARCHAR(50) 必填<br>
     * 报告号
     */
    private String reportNo;

    /**
     * api_id BIGINT(19) 必填<br>
     * system_api id
     */
    private Long apiId;

    /**
     * subscriber_id BIGINT(19) 必填<br>
     * system_api ID
     */
    private Long subscriberId;

    /**
     * subscriber INTEGER(10) 必填<br>
     * 订阅号
     */
    private Integer subscriber;

    /**
     * delivery_flag INTEGER(10) 必填<br>
     * 发送标记 1: ING, 2:SUCCESS
     */
    private Integer deliveryFlag;

    /**
     * create_date TIMESTAMP(19) 必填<br>
     * 创建时间
     */
    private Date createDate;

    /**
     * resend_flag INTEGER(10) 必填<br>
     * 重新投递标志
     */
    private Integer resendFlag;

    /**
     * update_date TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP] 必填<br>
     * 更新时间
     */
    private Date updateDate;

    /**
     * report_no VARCHAR(50) 必填<br>
     * 获得 报告号
     */
    public String getReportNo() {
        return reportNo;
    }

    /**
     * report_no VARCHAR(50) 必填<br>
     * 设置 报告号
     */
    public void setReportNo(String reportNo) {
        this.reportNo = reportNo == null ? null : reportNo.trim();
    }

    /**
     * api_id BIGINT(19) 必填<br>
     * 获得 system_api id
     */
    public Long getApiId() {
        return apiId;
    }

    /**
     * api_id BIGINT(19) 必填<br>
     * 设置 system_api id
     */
    public void setApiId(Long apiId) {
        this.apiId = apiId;
    }

    /**
     * subscriber_id BIGINT(19) 必填<br>
     * 获得 system_api ID
     */
    public Long getSubscriberId() {
        return subscriberId;
    }

    /**
     * subscriber_id BIGINT(19) 必填<br>
     * 设置 system_api ID
     */
    public void setSubscriberId(Long subscriberId) {
        this.subscriberId = subscriberId;
    }

    /**
     * subscriber INTEGER(10) 必填<br>
     * 获得 订阅号
     */
    public Integer getSubscriber() {
        return subscriber;
    }

    /**
     * subscriber INTEGER(10) 必填<br>
     * 设置 订阅号
     */
    public void setSubscriber(Integer subscriber) {
        this.subscriber = subscriber;
    }

    /**
     * delivery_flag INTEGER(10) 必填<br>
     * 获得 发送标记 1: ING, 2:SUCCESS
     */
    public Integer getDeliveryFlag() {
        return deliveryFlag;
    }

    /**
     * delivery_flag INTEGER(10) 必填<br>
     * 设置 发送标记 1: ING, 2:SUCCESS
     */
    public void setDeliveryFlag(Integer deliveryFlag) {
        this.deliveryFlag = deliveryFlag;
    }

    /**
     * create_date TIMESTAMP(19) 必填<br>
     * 获得 创建时间
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * create_date TIMESTAMP(19) 必填<br>
     * 设置 创建时间
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * resend_flag INTEGER(10) 必填<br>
     * 获得 重新投递标志
     */
    public Integer getResendFlag() {
        return resendFlag;
    }

    /**
     * resend_flag INTEGER(10) 必填<br>
     * 设置 重新投递标志
     */
    public void setResendFlag(Integer resendFlag) {
        this.resendFlag = resendFlag;
    }

    /**
     * update_date TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP] 必填<br>
     * 获得 更新时间
     */
    public Date getUpdateDate() {
        return updateDate;
    }

    /**
     * update_date TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP] 必填<br>
     * 设置 更新时间
     */
    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", reportNo=").append(reportNo);
        sb.append(", apiId=").append(apiId);
        sb.append(", subscriberId=").append(subscriberId);
        sb.append(", subscriber=").append(subscriber);
        sb.append(", deliveryFlag=").append(deliveryFlag);
        sb.append(", createDate=").append(createDate);
        sb.append(", resendFlag=").append(resendFlag);
        sb.append(", updateDate=").append(updateDate);
        sb.append("]");
        return sb.toString();
    }
}