package com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist;

import com.sgs.customerbiz.dbstorages.mybatis.model.CfgTodoListRefSystemIdExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.CfgTodoListRefSystemIdPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CfgTodoListRefSystemIdMapper {
    int countByExample(CfgTodoListRefSystemIdExample example);

    int deleteByExample(CfgTodoListRefSystemIdExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CfgTodoListRefSystemIdPO record);

    int insertSelective(CfgTodoListRefSystemIdPO record);

    List<CfgTodoListRefSystemIdPO> selectByExample(CfgTodoListRefSystemIdExample example);

    CfgTodoListRefSystemIdPO selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CfgTodoListRefSystemIdPO record, @Param("example") CfgTodoListRefSystemIdExample example);

    int updateByExample(@Param("record") CfgTodoListRefSystemIdPO record, @Param("example") CfgTodoListRefSystemIdExample example);

    int updateByPrimaryKeySelective(CfgTodoListRefSystemIdPO record);

    int updateByPrimaryKey(CfgTodoListRefSystemIdPO record);

    int batchInsert(List<CfgTodoListRefSystemIdPO> list);

    int batchUpdate(List<CfgTodoListRefSystemIdPO> list);
}