package com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist;

import com.sgs.customerbiz.dbstorages.mybatis.model.TaskExecuteLogExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.TaskExecuteLogPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TaskExecuteLogMapper {
    int countByExample(TaskExecuteLogExample example);

    // int deleteByExample(TaskExecuteLogExample example);

    // int deleteByPrimaryKey(Long id);

    int insert(TaskExecuteLogPO record);

    int insertSelective(TaskExecuteLogPO record);

    List<TaskExecuteLogPO> selectByExampleWithBLOBs(TaskExecuteLogExample example);

    List<TaskExecuteLogPO> selectByExample(TaskExecuteLogExample example);

    TaskExecuteLogPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TaskExecuteLogPO record, @Param("example") TaskExecuteLogExample example);

    int updateByExampleWithBLOBs(@Param("record") TaskExecuteLogPO record, @Param("example") TaskExecuteLogExample example);

    int updateByExample(@Param("record") TaskExecuteLogPO record, @Param("example") TaskExecuteLogExample example);

    int updateByPrimaryKeySelective(TaskExecuteLogPO record);

    int updateByPrimaryKeyWithBLOBs(TaskExecuteLogPO record);

    int updateByPrimaryKey(TaskExecuteLogPO record);

    int batchInsert(List<TaskExecuteLogPO> list);

    int batchUpdate(List<TaskExecuteLogPO> list);

    List<TaskExecuteLogPO> getTaskLatestExecuteLogByTaskIdList(@Param("taskIdList") List<Long> taskIdList);
}