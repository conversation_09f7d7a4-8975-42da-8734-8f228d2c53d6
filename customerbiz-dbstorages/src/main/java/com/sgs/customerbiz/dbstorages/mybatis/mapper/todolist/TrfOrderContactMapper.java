package com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist;

import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderContactExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderContactPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TrfOrderContactMapper {
    int countByExample(TrfOrderContactExample example);

    // int deleteByExample(TrfOrderContactExample example);

    // int deleteByPrimaryKey(Long id);

    int insert(TrfOrderContactPO record);

    int insertSelective(TrfOrderContactPO record);

    List<TrfOrderContactPO> selectByExample(TrfOrderContactExample example);

    TrfOrderContactPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TrfOrderContactPO record, @Param("example") TrfOrderContactExample example);

    int updateByExample(@Param("record") TrfOrderContactPO record, @Param("example") TrfOrderContactExample example);

    int updateByPrimaryKeySelective(TrfOrderContactPO record);

    int updateByPrimaryKey(TrfOrderContactPO record);

    int batchInsert(List<TrfOrderContactPO> list);

    int batchUpdate(List<TrfOrderContactPO> list);
}