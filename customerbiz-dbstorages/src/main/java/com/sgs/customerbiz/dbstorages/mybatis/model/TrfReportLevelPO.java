package com.sgs.customerbiz.dbstorages.mybatis.model;

import java.util.Date;

public class TrfReportLevelPO {
    /**
     * id BIGINT(19) 必填<br>
     * 
     */
    private Long id;

    /**
     * trf_no VARCHAR(100)<br>
     * 
     */
    private String trfNo;

    /**
     * ref_system_id INTEGER(10)<br>
     * 
     */
    private Integer refSystemId;

    /**
     * order_no VARCHAR(100)<br>
     * 
     */
    private String orderNo;

    /**
     * biz_id VARCHAR(100)<br>
     * 
     */
    private String bizId;

    /**
     * biz_no VARCHAR(100)<br>
     * 
     */
    private String bizNo;

    /**
     * biz_type VARCHAR(50)<br>
     * 
     */
    private String bizType;

    /**
     * created_date TIMESTAMP(19)<br>
     * 
     */
    private Date createdDate;

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 
     */
    private Date lastModifiedTimestamp;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * trf_no VARCHAR(100)<br>
     * 获得 
     */
    public String getTrfNo() {
        return trfNo;
    }

    /**
     * trf_no VARCHAR(100)<br>
     * 设置 
     */
    public void setTrfNo(String trfNo) {
        this.trfNo = trfNo == null ? null : trfNo.trim();
    }

    /**
     * ref_system_id INTEGER(10)<br>
     * 获得 
     */
    public Integer getRefSystemId() {
        return refSystemId;
    }

    /**
     * ref_system_id INTEGER(10)<br>
     * 设置 
     */
    public void setRefSystemId(Integer refSystemId) {
        this.refSystemId = refSystemId;
    }

    /**
     * order_no VARCHAR(100)<br>
     * 获得 
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * order_no VARCHAR(100)<br>
     * 设置 
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * biz_id VARCHAR(100)<br>
     * 获得 
     */
    public String getBizId() {
        return bizId;
    }

    /**
     * biz_id VARCHAR(100)<br>
     * 设置 
     */
    public void setBizId(String bizId) {
        this.bizId = bizId == null ? null : bizId.trim();
    }

    /**
     * biz_no VARCHAR(100)<br>
     * 获得 
     */
    public String getBizNo() {
        return bizNo;
    }

    /**
     * biz_no VARCHAR(100)<br>
     * 设置 
     */
    public void setBizNo(String bizNo) {
        this.bizNo = bizNo == null ? null : bizNo.trim();
    }

    /**
     * biz_type VARCHAR(50)<br>
     * 获得 
     */
    public String getBizType() {
        return bizType;
    }

    /**
     * biz_type VARCHAR(50)<br>
     * 设置 
     */
    public void setBizType(String bizType) {
        this.bizType = bizType == null ? null : bizType.trim();
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 设置 
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 获得 
     */
    public Date getLastModifiedTimestamp() {
        return lastModifiedTimestamp;
    }

    /**
     * last_modified_timestamp TIMESTAMP(19)<br>
     * 设置 
     */
    public void setLastModifiedTimestamp(Date lastModifiedTimestamp) {
        this.lastModifiedTimestamp = lastModifiedTimestamp;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", trfNo=").append(trfNo);
        sb.append(", refSystemId=").append(refSystemId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", bizId=").append(bizId);
        sb.append(", bizNo=").append(bizNo);
        sb.append(", bizType=").append(bizType);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", lastModifiedTimestamp=").append(lastModifiedTimestamp);
        sb.append("]");
        return sb.toString();
    }
}