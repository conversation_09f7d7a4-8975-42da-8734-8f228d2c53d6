package com.sgs.customerbiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TrfTestItemLangExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TrfTestItemLangExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTrfIdIsNull() {
            addCriterion("trf_id is null");
            return (Criteria) this;
        }

        public Criteria andTrfIdIsNotNull() {
            addCriterion("trf_id is not null");
            return (Criteria) this;
        }

        public Criteria andTrfIdEqualTo(Long value) {
            addCriterion("trf_id =", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdNotEqualTo(Long value) {
            addCriterion("trf_id <>", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdGreaterThan(Long value) {
            addCriterion("trf_id >", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdGreaterThanOrEqualTo(Long value) {
            addCriterion("trf_id >=", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdLessThan(Long value) {
            addCriterion("trf_id <", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdLessThanOrEqualTo(Long value) {
            addCriterion("trf_id <=", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdIn(List<Long> values) {
            addCriterion("trf_id in", values, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdNotIn(List<Long> values) {
            addCriterion("trf_id not in", values, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdBetween(Long value1, Long value2) {
            addCriterion("trf_id between", value1, value2, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdNotBetween(Long value1, Long value2) {
            addCriterion("trf_id not between", value1, value2, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfTestItemIdIsNull() {
            addCriterion("trf_test_item_id is null");
            return (Criteria) this;
        }

        public Criteria andTrfTestItemIdIsNotNull() {
            addCriterion("trf_test_item_id is not null");
            return (Criteria) this;
        }

        public Criteria andTrfTestItemIdEqualTo(Long value) {
            addCriterion("trf_test_item_id =", value, "trfTestItemId");
            return (Criteria) this;
        }

        public Criteria andTrfTestItemIdNotEqualTo(Long value) {
            addCriterion("trf_test_item_id <>", value, "trfTestItemId");
            return (Criteria) this;
        }

        public Criteria andTrfTestItemIdGreaterThan(Long value) {
            addCriterion("trf_test_item_id >", value, "trfTestItemId");
            return (Criteria) this;
        }

        public Criteria andTrfTestItemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("trf_test_item_id >=", value, "trfTestItemId");
            return (Criteria) this;
        }

        public Criteria andTrfTestItemIdLessThan(Long value) {
            addCriterion("trf_test_item_id <", value, "trfTestItemId");
            return (Criteria) this;
        }

        public Criteria andTrfTestItemIdLessThanOrEqualTo(Long value) {
            addCriterion("trf_test_item_id <=", value, "trfTestItemId");
            return (Criteria) this;
        }

        public Criteria andTrfTestItemIdIn(List<Long> values) {
            addCriterion("trf_test_item_id in", values, "trfTestItemId");
            return (Criteria) this;
        }

        public Criteria andTrfTestItemIdNotIn(List<Long> values) {
            addCriterion("trf_test_item_id not in", values, "trfTestItemId");
            return (Criteria) this;
        }

        public Criteria andTrfTestItemIdBetween(Long value1, Long value2) {
            addCriterion("trf_test_item_id between", value1, value2, "trfTestItemId");
            return (Criteria) this;
        }

        public Criteria andTrfTestItemIdNotBetween(Long value1, Long value2) {
            addCriterion("trf_test_item_id not between", value1, value2, "trfTestItemId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIsNull() {
            addCriterion("language_id is null");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIsNotNull() {
            addCriterion("language_id is not null");
            return (Criteria) this;
        }

        public Criteria andLanguageIdEqualTo(Integer value) {
            addCriterion("language_id =", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotEqualTo(Integer value) {
            addCriterion("language_id <>", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdGreaterThan(Integer value) {
            addCriterion("language_id >", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("language_id >=", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdLessThan(Integer value) {
            addCriterion("language_id <", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdLessThanOrEqualTo(Integer value) {
            addCriterion("language_id <=", value, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdIn(List<Integer> values) {
            addCriterion("language_id in", values, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotIn(List<Integer> values) {
            addCriterion("language_id not in", values, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdBetween(Integer value1, Integer value2) {
            addCriterion("language_id between", value1, value2, "languageId");
            return (Criteria) this;
        }

        public Criteria andLanguageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("language_id not between", value1, value2, "languageId");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasIsNull() {
            addCriterion("evaluation_alias is null");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasIsNotNull() {
            addCriterion("evaluation_alias is not null");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasEqualTo(String value) {
            addCriterion("evaluation_alias =", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotEqualTo(String value) {
            addCriterion("evaluation_alias <>", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasGreaterThan(String value) {
            addCriterion("evaluation_alias >", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasGreaterThanOrEqualTo(String value) {
            addCriterion("evaluation_alias >=", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasLessThan(String value) {
            addCriterion("evaluation_alias <", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasLessThanOrEqualTo(String value) {
            addCriterion("evaluation_alias <=", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasLike(String value) {
            addCriterion("evaluation_alias like", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotLike(String value) {
            addCriterion("evaluation_alias not like", value, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasIn(List<String> values) {
            addCriterion("evaluation_alias in", values, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotIn(List<String> values) {
            addCriterion("evaluation_alias not in", values, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasBetween(String value1, String value2) {
            addCriterion("evaluation_alias between", value1, value2, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationAliasNotBetween(String value1, String value2) {
            addCriterion("evaluation_alias not between", value1, value2, "evaluationAlias");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameIsNull() {
            addCriterion("evaluation_name is null");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameIsNotNull() {
            addCriterion("evaluation_name is not null");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameEqualTo(String value) {
            addCriterion("evaluation_name =", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameNotEqualTo(String value) {
            addCriterion("evaluation_name <>", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameGreaterThan(String value) {
            addCriterion("evaluation_name >", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameGreaterThanOrEqualTo(String value) {
            addCriterion("evaluation_name >=", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameLessThan(String value) {
            addCriterion("evaluation_name <", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameLessThanOrEqualTo(String value) {
            addCriterion("evaluation_name <=", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameLike(String value) {
            addCriterion("evaluation_name like", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameNotLike(String value) {
            addCriterion("evaluation_name not like", value, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameIn(List<String> values) {
            addCriterion("evaluation_name in", values, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameNotIn(List<String> values) {
            addCriterion("evaluation_name not in", values, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameBetween(String value1, String value2) {
            addCriterion("evaluation_name between", value1, value2, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andEvaluationNameNotBetween(String value1, String value2) {
            addCriterion("evaluation_name not between", value1, value2, "evaluationName");
            return (Criteria) this;
        }

        public Criteria andPpNameIsNull() {
            addCriterion("pp_name is null");
            return (Criteria) this;
        }

        public Criteria andPpNameIsNotNull() {
            addCriterion("pp_name is not null");
            return (Criteria) this;
        }

        public Criteria andPpNameEqualTo(String value) {
            addCriterion("pp_name =", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameNotEqualTo(String value) {
            addCriterion("pp_name <>", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameGreaterThan(String value) {
            addCriterion("pp_name >", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameGreaterThanOrEqualTo(String value) {
            addCriterion("pp_name >=", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameLessThan(String value) {
            addCriterion("pp_name <", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameLessThanOrEqualTo(String value) {
            addCriterion("pp_name <=", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameLike(String value) {
            addCriterion("pp_name like", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameNotLike(String value) {
            addCriterion("pp_name not like", value, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameIn(List<String> values) {
            addCriterion("pp_name in", values, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameNotIn(List<String> values) {
            addCriterion("pp_name not in", values, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameBetween(String value1, String value2) {
            addCriterion("pp_name between", value1, value2, "ppName");
            return (Criteria) this;
        }

        public Criteria andPpNameNotBetween(String value1, String value2) {
            addCriterion("pp_name not between", value1, value2, "ppName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameIsNull() {
            addCriterion("citation_full_name is null");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameIsNotNull() {
            addCriterion("citation_full_name is not null");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameEqualTo(String value) {
            addCriterion("citation_full_name =", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotEqualTo(String value) {
            addCriterion("citation_full_name <>", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameGreaterThan(String value) {
            addCriterion("citation_full_name >", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameGreaterThanOrEqualTo(String value) {
            addCriterion("citation_full_name >=", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameLessThan(String value) {
            addCriterion("citation_full_name <", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameLessThanOrEqualTo(String value) {
            addCriterion("citation_full_name <=", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameLike(String value) {
            addCriterion("citation_full_name like", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotLike(String value) {
            addCriterion("citation_full_name not like", value, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameIn(List<String> values) {
            addCriterion("citation_full_name in", values, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotIn(List<String> values) {
            addCriterion("citation_full_name not in", values, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameBetween(String value1, String value2) {
            addCriterion("citation_full_name between", value1, value2, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andCitationFullNameNotBetween(String value1, String value2) {
            addCriterion("citation_full_name not between", value1, value2, "citationFullName");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameIsNull() {
            addCriterion("external_test_item_name is null");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameIsNotNull() {
            addCriterion("external_test_item_name is not null");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameEqualTo(String value) {
            addCriterion("external_test_item_name =", value, "externalTestItemName");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameNotEqualTo(String value) {
            addCriterion("external_test_item_name <>", value, "externalTestItemName");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameGreaterThan(String value) {
            addCriterion("external_test_item_name >", value, "externalTestItemName");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameGreaterThanOrEqualTo(String value) {
            addCriterion("external_test_item_name >=", value, "externalTestItemName");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameLessThan(String value) {
            addCriterion("external_test_item_name <", value, "externalTestItemName");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameLessThanOrEqualTo(String value) {
            addCriterion("external_test_item_name <=", value, "externalTestItemName");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameLike(String value) {
            addCriterion("external_test_item_name like", value, "externalTestItemName");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameNotLike(String value) {
            addCriterion("external_test_item_name not like", value, "externalTestItemName");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameIn(List<String> values) {
            addCriterion("external_test_item_name in", values, "externalTestItemName");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameNotIn(List<String> values) {
            addCriterion("external_test_item_name not in", values, "externalTestItemName");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameBetween(String value1, String value2) {
            addCriterion("external_test_item_name between", value1, value2, "externalTestItemName");
            return (Criteria) this;
        }

        public Criteria andExternalTestItemNameNotBetween(String value1, String value2) {
            addCriterion("external_test_item_name not between", value1, value2, "externalTestItemName");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameIsNull() {
            addCriterion("external_test_citation_name is null");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameIsNotNull() {
            addCriterion("external_test_citation_name is not null");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameEqualTo(String value) {
            addCriterion("external_test_citation_name =", value, "externalTestCitationName");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameNotEqualTo(String value) {
            addCriterion("external_test_citation_name <>", value, "externalTestCitationName");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameGreaterThan(String value) {
            addCriterion("external_test_citation_name >", value, "externalTestCitationName");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameGreaterThanOrEqualTo(String value) {
            addCriterion("external_test_citation_name >=", value, "externalTestCitationName");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameLessThan(String value) {
            addCriterion("external_test_citation_name <", value, "externalTestCitationName");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameLessThanOrEqualTo(String value) {
            addCriterion("external_test_citation_name <=", value, "externalTestCitationName");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameLike(String value) {
            addCriterion("external_test_citation_name like", value, "externalTestCitationName");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameNotLike(String value) {
            addCriterion("external_test_citation_name not like", value, "externalTestCitationName");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameIn(List<String> values) {
            addCriterion("external_test_citation_name in", values, "externalTestCitationName");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameNotIn(List<String> values) {
            addCriterion("external_test_citation_name not in", values, "externalTestCitationName");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameBetween(String value1, String value2) {
            addCriterion("external_test_citation_name between", value1, value2, "externalTestCitationName");
            return (Criteria) this;
        }

        public Criteria andExternalTestCitationNameNotBetween(String value1, String value2) {
            addCriterion("external_test_citation_name not between", value1, value2, "externalTestCitationName");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("active_indicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("active_indicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Integer value) {
            addCriterion("active_indicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Integer value) {
            addCriterion("active_indicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Integer value) {
            addCriterion("active_indicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("active_indicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Integer value) {
            addCriterion("active_indicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Integer value) {
            addCriterion("active_indicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Integer> values) {
            addCriterion("active_indicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Integer> values) {
            addCriterion("active_indicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("created_date is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("created_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("created_date =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("created_date <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("created_date >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("created_date >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("created_date <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("created_date <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("created_date in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("created_date not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("created_date between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("created_date not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("modified_by is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("modified_by is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("modified_by =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("modified_by <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("modified_by >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("modified_by >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("modified_by <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("modified_by <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("modified_by like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("modified_by not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("modified_by in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("modified_by not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("modified_by between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("modified_by not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("modified_date is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("modified_date is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("modified_date =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("modified_date <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("modified_date >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("modified_date >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("modified_date <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("modified_date <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("modified_date in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("modified_date not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("modified_date between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("modified_date not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}