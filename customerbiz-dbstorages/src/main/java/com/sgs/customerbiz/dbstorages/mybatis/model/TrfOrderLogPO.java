package com.sgs.customerbiz.dbstorages.mybatis.model;

import java.util.Date;

public class TrfOrderLogPO {
    /**
     * id BIGINT(19) 必填<br>
     * 
     */
    private Long id;

    /**
     * trf_order_id BIGINT(19) 必填<br>
     * 
     */
    private Long trfOrderId;

    /**
     * trf_id BIGINT(19)<br>
     * 
     */
    private Long trfId;

    /**
     * system_id INTEGER(10)<br>
     * 归属系统id
     */
    private Integer systemId;

    /**
     * order_id VARCHAR(80)<br>
     * 订单id
     */
    private String orderId;

    /**
     * order_no VARCHAR(80)<br>
     * 订单号
     */
    private String orderNo;

    /**
     * order_status INTEGER(10)<br>
     * 
     */
    private Integer orderStatus;

    /**
     * bound_status INTEGER(10) 默认值[0] 必填<br>
     * Trf绑定状态（1：已绑定、2：已解绑）
     */
    private Integer boundStatus;

    /**
     * active_indicator INTEGER(10) 默认值[1] 必填<br>
     * 有效无效标记：0: inactive, 1: active
     */
    private Integer activeIndicator;

    /**
     * pending_flag TINYINT(3) 默认值[0]<br>
     * 0: activate, 1: pending
     */
    private Integer pendingFlag;

    /**
     * pending_type INTEGER(10)<br>
     * 
     */
    private String pendingType;

    /**
     * pending_remark VARCHAR(1000)<br>
     * Pending备注
     */
    private String pendingRemark;

    /**
     * cancel_type INTEGER(10)<br>
     * 取消类型
     */
    private Integer cancelType;

    /**
     * cancel_remark VARCHAR(1000)<br>
     * 取消原因
     */
    private String cancelRemark;

    /**
     * created_by VARCHAR(50) 默认值[system]<br>
     * 创建人
     */
    private String createdBy;

    /**
     * created_date TIMESTAMP(19)<br>
     * 创建时间
     */
    private Date createdDate;

    /**
     * modified_by VARCHAR(50) 默认值[system]<br>
     * 修改人
     */
    private String modifiedBy;

    /**
     * modified_date TIMESTAMP(19)<br>
     * 修改时间
     */
    private Date modifiedDate;

    /**
     * request_id VARCHAR(100)<br>
     * 请求id
     */
    private String requestId;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * trf_order_id BIGINT(19) 必填<br>
     * 获得 
     */
    public Long getTrfOrderId() {
        return trfOrderId;
    }

    /**
     * trf_order_id BIGINT(19) 必填<br>
     * 设置 
     */
    public void setTrfOrderId(Long trfOrderId) {
        this.trfOrderId = trfOrderId;
    }

    /**
     * trf_id BIGINT(19)<br>
     * 获得 
     */
    public Long getTrfId() {
        return trfId;
    }

    /**
     * trf_id BIGINT(19)<br>
     * 设置 
     */
    public void setTrfId(Long trfId) {
        this.trfId = trfId;
    }

    /**
     * system_id INTEGER(10)<br>
     * 获得 归属系统id
     */
    public Integer getSystemId() {
        return systemId;
    }

    /**
     * system_id INTEGER(10)<br>
     * 设置 归属系统id
     */
    public void setSystemId(Integer systemId) {
        this.systemId = systemId;
    }

    /**
     * order_id VARCHAR(80)<br>
     * 获得 订单id
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * order_id VARCHAR(80)<br>
     * 设置 订单id
     */
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    /**
     * order_no VARCHAR(80)<br>
     * 获得 订单号
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * order_no VARCHAR(80)<br>
     * 设置 订单号
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * order_status INTEGER(10)<br>
     * 获得 
     */
    public Integer getOrderStatus() {
        return orderStatus;
    }

    /**
     * order_status INTEGER(10)<br>
     * 设置 
     */
    public void setOrderStatus(Integer orderStatus) {
        this.orderStatus = orderStatus;
    }

    /**
     * bound_status INTEGER(10) 默认值[0] 必填<br>
     * 获得 Trf绑定状态（1：已绑定、2：已解绑）
     */
    public Integer getBoundStatus() {
        return boundStatus;
    }

    /**
     * bound_status INTEGER(10) 默认值[0] 必填<br>
     * 设置 Trf绑定状态（1：已绑定、2：已解绑）
     */
    public void setBoundStatus(Integer boundStatus) {
        this.boundStatus = boundStatus;
    }

    /**
     * active_indicator INTEGER(10) 默认值[1] 必填<br>
     * 获得 有效无效标记：0: inactive, 1: active
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator INTEGER(10) 默认值[1] 必填<br>
     * 设置 有效无效标记：0: inactive, 1: active
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * pending_flag TINYINT(3) 默认值[0]<br>
     * 获得 0: activate, 1: pending
     */
    public Integer getPendingFlag() {
        return pendingFlag;
    }

    /**
     * pending_flag TINYINT(3) 默认值[0]<br>
     * 设置 0: activate, 1: pending
     */
    public void setPendingFlag(Integer pendingFlag) {
        this.pendingFlag = pendingFlag;
    }

    /**
     * pending_type VARCHAR(1000)<br>
     * 获得 Pending类型
     */
    public String getPendingType() {
        return pendingType;
    }

    /**
     * pending_type VARCHAR(1000)<br>
     * 设置 Pending类型
     */
    public void setPendingType(String pendingType) {
        this.pendingType = pendingType == null ? null : pendingType.trim();
    }

    /**
     * pending_remark VARCHAR(1000)<br>
     * 获得 Pending备注
     */
    public String getPendingRemark() {
        return pendingRemark;
    }

    /**
     * pending_remark VARCHAR(1000)<br>
     * 设置 Pending备注
     */
    public void setPendingRemark(String pendingRemark) {
        this.pendingRemark = pendingRemark == null ? null : pendingRemark.trim();
    }

    /**
     * cancel_type INTEGER(10)<br>
     * 获得 取消类型
     */
    public Integer getCancelType() {
        return cancelType;
    }

    /**
     * cancel_type INTEGER(10)<br>
     * 设置 取消类型
     */
    public void setCancelType(Integer cancelType) {
        this.cancelType = cancelType;
    }

    /**
     * cancel_remark VARCHAR(1000)<br>
     * 获得 取消原因
     */
    public String getCancelRemark() {
        return cancelRemark;
    }

    /**
     * cancel_remark VARCHAR(1000)<br>
     * 设置 取消原因
     */
    public void setCancelRemark(String cancelRemark) {
        this.cancelRemark = cancelRemark == null ? null : cancelRemark.trim();
    }

    /**
     * created_by VARCHAR(50) 默认值[system]<br>
     * 获得 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * created_by VARCHAR(50) 默认值[system]<br>
     * 设置 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 获得 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19)<br>
     * 设置 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_by VARCHAR(50) 默认值[system]<br>
     * 获得 修改人
     */
    public String getModifiedBy() {
        return modifiedBy;
    }

    /**
     * modified_by VARCHAR(50) 默认值[system]<br>
     * 设置 修改人
     */
    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy == null ? null : modifiedBy.trim();
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 获得 修改时间
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19)<br>
     * 设置 修改时间
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    /**
     * request_id VARCHAR(100)<br>
     * 获得 请求id
     */
    public String getRequestId() {
        return requestId;
    }

    /**
     * request_id VARCHAR(100)<br>
     * 设置 请求id
     */
    public void setRequestId(String requestId) {
        this.requestId = requestId == null ? null : requestId.trim();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", trfOrderId=").append(trfOrderId);
        sb.append(", trfId=").append(trfId);
        sb.append(", systemId=").append(systemId);
        sb.append(", orderId=").append(orderId);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", orderStatus=").append(orderStatus);
        sb.append(", boundStatus=").append(boundStatus);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", pendingFlag=").append(pendingFlag);
        sb.append(", pendingType=").append(pendingType);
        sb.append(", pendingRemark=").append(pendingRemark);
        sb.append(", cancelType=").append(cancelType);
        sb.append(", cancelRemark=").append(cancelRemark);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedBy=").append(modifiedBy);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append(", requestId=").append(requestId);
        sb.append("]");
        return sb.toString();
    }
}