package com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist;

import com.sgs.customerbiz.dbstorages.mybatis.model.TrfApproveExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfApprovePO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TrfApproveMapper {
    int countByExample(TrfApproveExample example);

    int deleteByExample(TrfApproveExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TrfApprovePO record);

    int insertSelective(TrfApprovePO record);

    List<TrfApprovePO> selectByExample(TrfApproveExample example);

    TrfApprovePO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TrfApprovePO record, @Param("example") TrfApproveExample example);

    int updateByExample(@Param("record") TrfApprovePO record, @Param("example") TrfApproveExample example);

    int updateByPrimaryKeySelective(TrfApprovePO record);

    int updateByPrimaryKey(TrfApprovePO record);

    int batchInsert(List<TrfApprovePO> list);

    int batchUpdate(List<TrfApprovePO> list);
}