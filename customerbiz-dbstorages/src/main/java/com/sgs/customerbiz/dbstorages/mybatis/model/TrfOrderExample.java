package com.sgs.customerbiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TrfOrderExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TrfOrderExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTrfIdIsNull() {
            addCriterion("trf_id is null");
            return (Criteria) this;
        }

        public Criteria andTrfIdIsNotNull() {
            addCriterion("trf_id is not null");
            return (Criteria) this;
        }

        public Criteria andTrfIdEqualTo(Long value) {
            addCriterion("trf_id =", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdNotEqualTo(Long value) {
            addCriterion("trf_id <>", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdGreaterThan(Long value) {
            addCriterion("trf_id >", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdGreaterThanOrEqualTo(Long value) {
            addCriterion("trf_id >=", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdLessThan(Long value) {
            addCriterion("trf_id <", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdLessThanOrEqualTo(Long value) {
            addCriterion("trf_id <=", value, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdIn(List<Long> values) {
            addCriterion("trf_id in", values, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdNotIn(List<Long> values) {
            addCriterion("trf_id not in", values, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdBetween(Long value1, Long value2) {
            addCriterion("trf_id between", value1, value2, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfIdNotBetween(Long value1, Long value2) {
            addCriterion("trf_id not between", value1, value2, "trfId");
            return (Criteria) this;
        }

        public Criteria andTrfNoIsNull() {
            addCriterion("trf_no is null");
            return (Criteria) this;
        }

        public Criteria andTrfNoIsNotNull() {
            addCriterion("trf_no is not null");
            return (Criteria) this;
        }

        public Criteria andTrfNoEqualTo(String value) {
            addCriterion("trf_no =", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoNotEqualTo(String value) {
            addCriterion("trf_no <>", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoGreaterThan(String value) {
            addCriterion("trf_no >", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoGreaterThanOrEqualTo(String value) {
            addCriterion("trf_no >=", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoLessThan(String value) {
            addCriterion("trf_no <", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoLessThanOrEqualTo(String value) {
            addCriterion("trf_no <=", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoLike(String value) {
            addCriterion("trf_no like", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoNotLike(String value) {
            addCriterion("trf_no not like", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoIn(List<String> values) {
            addCriterion("trf_no in", values, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoNotIn(List<String> values) {
            addCriterion("trf_no not in", values, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoBetween(String value1, String value2) {
            addCriterion("trf_no between", value1, value2, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoNotBetween(String value1, String value2) {
            addCriterion("trf_no not between", value1, value2, "trfNo");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdIsNull() {
            addCriterion("ref_system_id is null");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdIsNotNull() {
            addCriterion("ref_system_id is not null");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdEqualTo(Integer value) {
            addCriterion("ref_system_id =", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdNotEqualTo(Integer value) {
            addCriterion("ref_system_id <>", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdGreaterThan(Integer value) {
            addCriterion("ref_system_id >", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("ref_system_id >=", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdLessThan(Integer value) {
            addCriterion("ref_system_id <", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdLessThanOrEqualTo(Integer value) {
            addCriterion("ref_system_id <=", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdIn(List<Integer> values) {
            addCriterion("ref_system_id in", values, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdNotIn(List<Integer> values) {
            addCriterion("ref_system_id not in", values, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdBetween(Integer value1, Integer value2) {
            addCriterion("ref_system_id between", value1, value2, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdNotBetween(Integer value1, Integer value2) {
            addCriterion("ref_system_id not between", value1, value2, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNull() {
            addCriterion("system_id is null");
            return (Criteria) this;
        }

        public Criteria andSystemIdIsNotNull() {
            addCriterion("system_id is not null");
            return (Criteria) this;
        }

        public Criteria andSystemIdEqualTo(Integer value) {
            addCriterion("system_id =", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotEqualTo(Integer value) {
            addCriterion("system_id <>", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThan(Integer value) {
            addCriterion("system_id >", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("system_id >=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThan(Integer value) {
            addCriterion("system_id <", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdLessThanOrEqualTo(Integer value) {
            addCriterion("system_id <=", value, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdIn(List<Integer> values) {
            addCriterion("system_id in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotIn(List<Integer> values) {
            addCriterion("system_id not in", values, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdBetween(Integer value1, Integer value2) {
            addCriterion("system_id between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andSystemIdNotBetween(Integer value1, Integer value2) {
            addCriterion("system_id not between", value1, value2, "systemId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(String value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(String value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(String value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(String value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(String value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(String value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLike(String value) {
            addCriterion("order_id like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotLike(String value) {
            addCriterion("order_id not like", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<String> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<String> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(String value1, String value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(String value1, String value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoIsNull() {
            addCriterion("enquiry_no is null");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoIsNotNull() {
            addCriterion("enquiry_no is not null");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoEqualTo(String value) {
            addCriterion("enquiry_no =", value, "enquiryNo");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoNotEqualTo(String value) {
            addCriterion("enquiry_no <>", value, "enquiryNo");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoGreaterThan(String value) {
            addCriterion("enquiry_no >", value, "enquiryNo");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoGreaterThanOrEqualTo(String value) {
            addCriterion("enquiry_no >=", value, "enquiryNo");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoLessThan(String value) {
            addCriterion("enquiry_no <", value, "enquiryNo");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoLessThanOrEqualTo(String value) {
            addCriterion("enquiry_no <=", value, "enquiryNo");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoLike(String value) {
            addCriterion("enquiry_no like", value, "enquiryNo");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoNotLike(String value) {
            addCriterion("enquiry_no not like", value, "enquiryNo");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoIn(List<String> values) {
            addCriterion("enquiry_no in", values, "enquiryNo");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoNotIn(List<String> values) {
            addCriterion("enquiry_no not in", values, "enquiryNo");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoBetween(String value1, String value2) {
            addCriterion("enquiry_no between", value1, value2, "enquiryNo");
            return (Criteria) this;
        }

        public Criteria andEnquiryNoNotBetween(String value1, String value2) {
            addCriterion("enquiry_no not between", value1, value2, "enquiryNo");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("order_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("order_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(Integer value) {
            addCriterion("order_status =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(Integer value) {
            addCriterion("order_status <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(Integer value) {
            addCriterion("order_status >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_status >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(Integer value) {
            addCriterion("order_status <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(Integer value) {
            addCriterion("order_status <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<Integer> values) {
            addCriterion("order_status in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<Integer> values) {
            addCriterion("order_status not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(Integer value1, Integer value2) {
            addCriterion("order_status between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("order_status not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderExpectDueDateIsNull() {
            addCriterion("order_expect_due_date is null");
            return (Criteria) this;
        }

        public Criteria andOrderExpectDueDateIsNotNull() {
            addCriterion("order_expect_due_date is not null");
            return (Criteria) this;
        }

        public Criteria andOrderExpectDueDateEqualTo(Date value) {
            addCriterion("order_expect_due_date =", value, "orderExpectDueDate");
            return (Criteria) this;
        }

        public Criteria andOrderExpectDueDateNotEqualTo(Date value) {
            addCriterion("order_expect_due_date <>", value, "orderExpectDueDate");
            return (Criteria) this;
        }

        public Criteria andOrderExpectDueDateGreaterThan(Date value) {
            addCriterion("order_expect_due_date >", value, "orderExpectDueDate");
            return (Criteria) this;
        }

        public Criteria andOrderExpectDueDateGreaterThanOrEqualTo(Date value) {
            addCriterion("order_expect_due_date >=", value, "orderExpectDueDate");
            return (Criteria) this;
        }

        public Criteria andOrderExpectDueDateLessThan(Date value) {
            addCriterion("order_expect_due_date <", value, "orderExpectDueDate");
            return (Criteria) this;
        }

        public Criteria andOrderExpectDueDateLessThanOrEqualTo(Date value) {
            addCriterion("order_expect_due_date <=", value, "orderExpectDueDate");
            return (Criteria) this;
        }

        public Criteria andOrderExpectDueDateIn(List<Date> values) {
            addCriterion("order_expect_due_date in", values, "orderExpectDueDate");
            return (Criteria) this;
        }

        public Criteria andOrderExpectDueDateNotIn(List<Date> values) {
            addCriterion("order_expect_due_date not in", values, "orderExpectDueDate");
            return (Criteria) this;
        }

        public Criteria andOrderExpectDueDateBetween(Date value1, Date value2) {
            addCriterion("order_expect_due_date between", value1, value2, "orderExpectDueDate");
            return (Criteria) this;
        }

        public Criteria andOrderExpectDueDateNotBetween(Date value1, Date value2) {
            addCriterion("order_expect_due_date not between", value1, value2, "orderExpectDueDate");
            return (Criteria) this;
        }

        public Criteria andBoundStatusIsNull() {
            addCriterion("bound_status is null");
            return (Criteria) this;
        }

        public Criteria andBoundStatusIsNotNull() {
            addCriterion("bound_status is not null");
            return (Criteria) this;
        }

        public Criteria andBoundStatusEqualTo(Integer value) {
            addCriterion("bound_status =", value, "boundStatus");
            return (Criteria) this;
        }

        public Criteria andBoundStatusNotEqualTo(Integer value) {
            addCriterion("bound_status <>", value, "boundStatus");
            return (Criteria) this;
        }

        public Criteria andBoundStatusGreaterThan(Integer value) {
            addCriterion("bound_status >", value, "boundStatus");
            return (Criteria) this;
        }

        public Criteria andBoundStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("bound_status >=", value, "boundStatus");
            return (Criteria) this;
        }

        public Criteria andBoundStatusLessThan(Integer value) {
            addCriterion("bound_status <", value, "boundStatus");
            return (Criteria) this;
        }

        public Criteria andBoundStatusLessThanOrEqualTo(Integer value) {
            addCriterion("bound_status <=", value, "boundStatus");
            return (Criteria) this;
        }

        public Criteria andBoundStatusIn(List<Integer> values) {
            addCriterion("bound_status in", values, "boundStatus");
            return (Criteria) this;
        }

        public Criteria andBoundStatusNotIn(List<Integer> values) {
            addCriterion("bound_status not in", values, "boundStatus");
            return (Criteria) this;
        }

        public Criteria andBoundStatusBetween(Integer value1, Integer value2) {
            addCriterion("bound_status between", value1, value2, "boundStatus");
            return (Criteria) this;
        }

        public Criteria andBoundStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("bound_status not between", value1, value2, "boundStatus");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNull() {
            addCriterion("active_indicator is null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIsNotNull() {
            addCriterion("active_indicator is not null");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorEqualTo(Integer value) {
            addCriterion("active_indicator =", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotEqualTo(Integer value) {
            addCriterion("active_indicator <>", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThan(Integer value) {
            addCriterion("active_indicator >", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("active_indicator >=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThan(Integer value) {
            addCriterion("active_indicator <", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorLessThanOrEqualTo(Integer value) {
            addCriterion("active_indicator <=", value, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorIn(List<Integer> values) {
            addCriterion("active_indicator in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotIn(List<Integer> values) {
            addCriterion("active_indicator not in", values, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andActiveIndicatorNotBetween(Integer value1, Integer value2) {
            addCriterion("active_indicator not between", value1, value2, "activeIndicator");
            return (Criteria) this;
        }

        public Criteria andPendingFlagIsNull() {
            addCriterion("pending_flag is null");
            return (Criteria) this;
        }

        public Criteria andPendingFlagIsNotNull() {
            addCriterion("pending_flag is not null");
            return (Criteria) this;
        }

        public Criteria andPendingFlagEqualTo(Integer value) {
            addCriterion("pending_flag =", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagNotEqualTo(Integer value) {
            addCriterion("pending_flag <>", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagGreaterThan(Integer value) {
            addCriterion("pending_flag >", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("pending_flag >=", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagLessThan(Integer value) {
            addCriterion("pending_flag <", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagLessThanOrEqualTo(Integer value) {
            addCriterion("pending_flag <=", value, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagIn(List<Integer> values) {
            addCriterion("pending_flag in", values, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagNotIn(List<Integer> values) {
            addCriterion("pending_flag not in", values, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagBetween(Integer value1, Integer value2) {
            addCriterion("pending_flag between", value1, value2, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("pending_flag not between", value1, value2, "pendingFlag");
            return (Criteria) this;
        }

        public Criteria andPendingTypeIsNull() {
            addCriterion("pending_type is null");
            return (Criteria) this;
        }

        public Criteria andPendingTypeIsNotNull() {
            addCriterion("pending_type is not null");
            return (Criteria) this;
        }

        public Criteria andPendingTypeEqualTo(String value) {
            addCriterion("pending_type =", value, "pendingType");
            return (Criteria) this;
        }

        public Criteria andPendingTypeNotEqualTo(String value) {
            addCriterion("pending_type <>", value, "pendingType");
            return (Criteria) this;
        }

        public Criteria andPendingTypeGreaterThan(String value) {
            addCriterion("pending_type >", value, "pendingType");
            return (Criteria) this;
        }

        public Criteria andPendingTypeGreaterThanOrEqualTo(String value) {
            addCriterion("pending_type >=", value, "pendingType");
            return (Criteria) this;
        }

        public Criteria andPendingTypeLessThan(String value) {
            addCriterion("pending_type <", value, "pendingType");
            return (Criteria) this;
        }

        public Criteria andPendingTypeLessThanOrEqualTo(String value) {
            addCriterion("pending_type <=", value, "pendingType");
            return (Criteria) this;
        }

        public Criteria andPendingTypeLike(String value) {
            addCriterion("pending_type like", value, "pendingType");
            return (Criteria) this;
        }

        public Criteria andPendingTypeNotLike(String value) {
            addCriterion("pending_type not like", value, "pendingType");
            return (Criteria) this;
        }

        public Criteria andPendingTypeIn(List<String> values) {
            addCriterion("pending_type in", values, "pendingType");
            return (Criteria) this;
        }

        public Criteria andPendingTypeNotIn(List<String> values) {
            addCriterion("pending_type not in", values, "pendingType");
            return (Criteria) this;
        }

        public Criteria andPendingTypeBetween(String value1, String value2) {
            addCriterion("pending_type between", value1, value2, "pendingType");
            return (Criteria) this;
        }

        public Criteria andPendingTypeNotBetween(String value1, String value2) {
            addCriterion("pending_type not between", value1, value2, "pendingType");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkIsNull() {
            addCriterion("pending_remark is null");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkIsNotNull() {
            addCriterion("pending_remark is not null");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkEqualTo(String value) {
            addCriterion("pending_remark =", value, "pendingRemark");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkNotEqualTo(String value) {
            addCriterion("pending_remark <>", value, "pendingRemark");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkGreaterThan(String value) {
            addCriterion("pending_remark >", value, "pendingRemark");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("pending_remark >=", value, "pendingRemark");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkLessThan(String value) {
            addCriterion("pending_remark <", value, "pendingRemark");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkLessThanOrEqualTo(String value) {
            addCriterion("pending_remark <=", value, "pendingRemark");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkLike(String value) {
            addCriterion("pending_remark like", value, "pendingRemark");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkNotLike(String value) {
            addCriterion("pending_remark not like", value, "pendingRemark");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkIn(List<String> values) {
            addCriterion("pending_remark in", values, "pendingRemark");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkNotIn(List<String> values) {
            addCriterion("pending_remark not in", values, "pendingRemark");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkBetween(String value1, String value2) {
            addCriterion("pending_remark between", value1, value2, "pendingRemark");
            return (Criteria) this;
        }

        public Criteria andPendingRemarkNotBetween(String value1, String value2) {
            addCriterion("pending_remark not between", value1, value2, "pendingRemark");
            return (Criteria) this;
        }

        public Criteria andCancelTypeIsNull() {
            addCriterion("cancel_type is null");
            return (Criteria) this;
        }

        public Criteria andCancelTypeIsNotNull() {
            addCriterion("cancel_type is not null");
            return (Criteria) this;
        }

        public Criteria andCancelTypeEqualTo(Integer value) {
            addCriterion("cancel_type =", value, "cancelType");
            return (Criteria) this;
        }

        public Criteria andCancelTypeNotEqualTo(Integer value) {
            addCriterion("cancel_type <>", value, "cancelType");
            return (Criteria) this;
        }

        public Criteria andCancelTypeGreaterThan(Integer value) {
            addCriterion("cancel_type >", value, "cancelType");
            return (Criteria) this;
        }

        public Criteria andCancelTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("cancel_type >=", value, "cancelType");
            return (Criteria) this;
        }

        public Criteria andCancelTypeLessThan(Integer value) {
            addCriterion("cancel_type <", value, "cancelType");
            return (Criteria) this;
        }

        public Criteria andCancelTypeLessThanOrEqualTo(Integer value) {
            addCriterion("cancel_type <=", value, "cancelType");
            return (Criteria) this;
        }

        public Criteria andCancelTypeIn(List<Integer> values) {
            addCriterion("cancel_type in", values, "cancelType");
            return (Criteria) this;
        }

        public Criteria andCancelTypeNotIn(List<Integer> values) {
            addCriterion("cancel_type not in", values, "cancelType");
            return (Criteria) this;
        }

        public Criteria andCancelTypeBetween(Integer value1, Integer value2) {
            addCriterion("cancel_type between", value1, value2, "cancelType");
            return (Criteria) this;
        }

        public Criteria andCancelTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("cancel_type not between", value1, value2, "cancelType");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkIsNull() {
            addCriterion("cancel_remark is null");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkIsNotNull() {
            addCriterion("cancel_remark is not null");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkEqualTo(String value) {
            addCriterion("cancel_remark =", value, "cancelRemark");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkNotEqualTo(String value) {
            addCriterion("cancel_remark <>", value, "cancelRemark");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkGreaterThan(String value) {
            addCriterion("cancel_remark >", value, "cancelRemark");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("cancel_remark >=", value, "cancelRemark");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkLessThan(String value) {
            addCriterion("cancel_remark <", value, "cancelRemark");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkLessThanOrEqualTo(String value) {
            addCriterion("cancel_remark <=", value, "cancelRemark");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkLike(String value) {
            addCriterion("cancel_remark like", value, "cancelRemark");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkNotLike(String value) {
            addCriterion("cancel_remark not like", value, "cancelRemark");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkIn(List<String> values) {
            addCriterion("cancel_remark in", values, "cancelRemark");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkNotIn(List<String> values) {
            addCriterion("cancel_remark not in", values, "cancelRemark");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkBetween(String value1, String value2) {
            addCriterion("cancel_remark between", value1, value2, "cancelRemark");
            return (Criteria) this;
        }

        public Criteria andCancelRemarkNotBetween(String value1, String value2) {
            addCriterion("cancel_remark not between", value1, value2, "cancelRemark");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("created_date is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("created_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("created_date =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("created_date <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("created_date >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("created_date >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("created_date <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("created_date <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("created_date in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("created_date not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("created_date between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("created_date not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("modified_by is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("modified_by is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("modified_by =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("modified_by <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("modified_by >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("modified_by >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("modified_by <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("modified_by <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("modified_by like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("modified_by not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("modified_by in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("modified_by not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("modified_by between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("modified_by not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("modified_date is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("modified_date is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("modified_date =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("modified_date <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("modified_date >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("modified_date >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("modified_date <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("modified_date <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("modified_date in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("modified_date not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("modified_date between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("modified_date not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
