package com.sgs.customerbiz.dbstorages.mybatis.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TrfTodoInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TrfTodoInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("Id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("Id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("Id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("Id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("Id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("Id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("Id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("Id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("Id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("Id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("Id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("Id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeIsNull() {
            addCriterion("ProductLineCode is null");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeIsNotNull() {
            addCriterion("ProductLineCode is not null");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeEqualTo(String value) {
            addCriterion("ProductLineCode =", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeNotEqualTo(String value) {
            addCriterion("ProductLineCode <>", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeGreaterThan(String value) {
            addCriterion("ProductLineCode >", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeGreaterThanOrEqualTo(String value) {
            addCriterion("ProductLineCode >=", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeLessThan(String value) {
            addCriterion("ProductLineCode <", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeLessThanOrEqualTo(String value) {
            addCriterion("ProductLineCode <=", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeLike(String value) {
            addCriterion("ProductLineCode like", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeNotLike(String value) {
            addCriterion("ProductLineCode not like", value, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeIn(List<String> values) {
            addCriterion("ProductLineCode in", values, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeNotIn(List<String> values) {
            addCriterion("ProductLineCode not in", values, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeBetween(String value1, String value2) {
            addCriterion("ProductLineCode between", value1, value2, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andProductLineCodeNotBetween(String value1, String value2) {
            addCriterion("ProductLineCode not between", value1, value2, "productLineCode");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdIsNull() {
            addCriterion("RefSystemId is null");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdIsNotNull() {
            addCriterion("RefSystemId is not null");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdEqualTo(Integer value) {
            addCriterion("RefSystemId =", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdNotEqualTo(Integer value) {
            addCriterion("RefSystemId <>", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdGreaterThan(Integer value) {
            addCriterion("RefSystemId >", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("RefSystemId >=", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdLessThan(Integer value) {
            addCriterion("RefSystemId <", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdLessThanOrEqualTo(Integer value) {
            addCriterion("RefSystemId <=", value, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdIn(List<Integer> values) {
            addCriterion("RefSystemId in", values, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdNotIn(List<Integer> values) {
            addCriterion("RefSystemId not in", values, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdBetween(Integer value1, Integer value2) {
            addCriterion("RefSystemId between", value1, value2, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andRefSystemIdNotBetween(Integer value1, Integer value2) {
            addCriterion("RefSystemId not between", value1, value2, "refSystemId");
            return (Criteria) this;
        }

        public Criteria andLabCodeIsNull() {
            addCriterion("LabCode is null");
            return (Criteria) this;
        }

        public Criteria andLabCodeIsNotNull() {
            addCriterion("LabCode is not null");
            return (Criteria) this;
        }

        public Criteria andLabCodeEqualTo(String value) {
            addCriterion("LabCode =", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotEqualTo(String value) {
            addCriterion("LabCode <>", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeGreaterThan(String value) {
            addCriterion("LabCode >", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeGreaterThanOrEqualTo(String value) {
            addCriterion("LabCode >=", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLessThan(String value) {
            addCriterion("LabCode <", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLessThanOrEqualTo(String value) {
            addCriterion("LabCode <=", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeLike(String value) {
            addCriterion("LabCode like", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotLike(String value) {
            addCriterion("LabCode not like", value, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeIn(List<String> values) {
            addCriterion("LabCode in", values, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotIn(List<String> values) {
            addCriterion("LabCode not in", values, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeBetween(String value1, String value2) {
            addCriterion("LabCode between", value1, value2, "labCode");
            return (Criteria) this;
        }

        public Criteria andLabCodeNotBetween(String value1, String value2) {
            addCriterion("LabCode not between", value1, value2, "labCode");
            return (Criteria) this;
        }

        public Criteria andTrfNoIsNull() {
            addCriterion("TrfNo is null");
            return (Criteria) this;
        }

        public Criteria andTrfNoIsNotNull() {
            addCriterion("TrfNo is not null");
            return (Criteria) this;
        }

        public Criteria andTrfNoEqualTo(String value) {
            addCriterion("TrfNo =", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoNotEqualTo(String value) {
            addCriterion("TrfNo <>", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoGreaterThan(String value) {
            addCriterion("TrfNo >", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoGreaterThanOrEqualTo(String value) {
            addCriterion("TrfNo >=", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoLessThan(String value) {
            addCriterion("TrfNo <", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoLessThanOrEqualTo(String value) {
            addCriterion("TrfNo <=", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoLike(String value) {
            addCriterion("TrfNo like", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoNotLike(String value) {
            addCriterion("TrfNo not like", value, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoIn(List<String> values) {
            addCriterion("TrfNo in", values, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoNotIn(List<String> values) {
            addCriterion("TrfNo not in", values, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoBetween(String value1, String value2) {
            addCriterion("TrfNo between", value1, value2, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfNoNotBetween(String value1, String value2) {
            addCriterion("TrfNo not between", value1, value2, "trfNo");
            return (Criteria) this;
        }

        public Criteria andTrfStatusIsNull() {
            addCriterion("TrfStatus is null");
            return (Criteria) this;
        }

        public Criteria andTrfStatusIsNotNull() {
            addCriterion("TrfStatus is not null");
            return (Criteria) this;
        }

        public Criteria andTrfStatusEqualTo(Integer value) {
            addCriterion("TrfStatus =", value, "trfStatus");
            return (Criteria) this;
        }

        public Criteria andTrfStatusNotEqualTo(Integer value) {
            addCriterion("TrfStatus <>", value, "trfStatus");
            return (Criteria) this;
        }

        public Criteria andTrfStatusGreaterThan(Integer value) {
            addCriterion("TrfStatus >", value, "trfStatus");
            return (Criteria) this;
        }

        public Criteria andTrfStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("TrfStatus >=", value, "trfStatus");
            return (Criteria) this;
        }

        public Criteria andTrfStatusLessThan(Integer value) {
            addCriterion("TrfStatus <", value, "trfStatus");
            return (Criteria) this;
        }

        public Criteria andTrfStatusLessThanOrEqualTo(Integer value) {
            addCriterion("TrfStatus <=", value, "trfStatus");
            return (Criteria) this;
        }

        public Criteria andTrfStatusIn(List<Integer> values) {
            addCriterion("TrfStatus in", values, "trfStatus");
            return (Criteria) this;
        }

        public Criteria andTrfStatusNotIn(List<Integer> values) {
            addCriterion("TrfStatus not in", values, "trfStatus");
            return (Criteria) this;
        }

        public Criteria andTrfStatusBetween(Integer value1, Integer value2) {
            addCriterion("TrfStatus between", value1, value2, "trfStatus");
            return (Criteria) this;
        }

        public Criteria andTrfStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("TrfStatus not between", value1, value2, "trfStatus");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("CreatedBy is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("CreatedBy is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("CreatedBy =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("CreatedBy <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("CreatedBy >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("CreatedBy >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("CreatedBy <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("CreatedBy <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("CreatedBy like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("CreatedBy not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("CreatedBy in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("CreatedBy not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("CreatedBy between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("CreatedBy not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNull() {
            addCriterion("CreatedDate is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIsNotNull() {
            addCriterion("CreatedDate is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDateEqualTo(Date value) {
            addCriterion("CreatedDate =", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotEqualTo(Date value) {
            addCriterion("CreatedDate <>", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThan(Date value) {
            addCriterion("CreatedDate >", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("CreatedDate >=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThan(Date value) {
            addCriterion("CreatedDate <", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateLessThanOrEqualTo(Date value) {
            addCriterion("CreatedDate <=", value, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateIn(List<Date> values) {
            addCriterion("CreatedDate in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotIn(List<Date> values) {
            addCriterion("CreatedDate not in", values, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateBetween(Date value1, Date value2) {
            addCriterion("CreatedDate between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andCreatedDateNotBetween(Date value1, Date value2) {
            addCriterion("CreatedDate not between", value1, value2, "createdDate");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNull() {
            addCriterion("ModifiedBy is null");
            return (Criteria) this;
        }

        public Criteria andModifiedByIsNotNull() {
            addCriterion("ModifiedBy is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedByEqualTo(String value) {
            addCriterion("ModifiedBy =", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotEqualTo(String value) {
            addCriterion("ModifiedBy <>", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThan(String value) {
            addCriterion("ModifiedBy >", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByGreaterThanOrEqualTo(String value) {
            addCriterion("ModifiedBy >=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThan(String value) {
            addCriterion("ModifiedBy <", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLessThanOrEqualTo(String value) {
            addCriterion("ModifiedBy <=", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByLike(String value) {
            addCriterion("ModifiedBy like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotLike(String value) {
            addCriterion("ModifiedBy not like", value, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByIn(List<String> values) {
            addCriterion("ModifiedBy in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotIn(List<String> values) {
            addCriterion("ModifiedBy not in", values, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByBetween(String value1, String value2) {
            addCriterion("ModifiedBy between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedByNotBetween(String value1, String value2) {
            addCriterion("ModifiedBy not between", value1, value2, "modifiedBy");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNull() {
            addCriterion("ModifiedDate is null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIsNotNull() {
            addCriterion("ModifiedDate is not null");
            return (Criteria) this;
        }

        public Criteria andModifiedDateEqualTo(Date value) {
            addCriterion("ModifiedDate =", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotEqualTo(Date value) {
            addCriterion("ModifiedDate <>", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThan(Date value) {
            addCriterion("ModifiedDate >", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateGreaterThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate >=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThan(Date value) {
            addCriterion("ModifiedDate <", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateLessThanOrEqualTo(Date value) {
            addCriterion("ModifiedDate <=", value, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateIn(List<Date> values) {
            addCriterion("ModifiedDate in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotIn(List<Date> values) {
            addCriterion("ModifiedDate not in", values, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }

        public Criteria andModifiedDateNotBetween(Date value1, Date value2) {
            addCriterion("ModifiedDate not between", value1, value2, "modifiedDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}