<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfLabMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.customerbiz.dbstorages.mybatis.model.TrfLabPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="lab_id" property="labId" jdbcType="INTEGER" />
    <result column="trf_id" property="trfId" jdbcType="BIGINT" />
    <result column="lab_code" property="labCode" jdbcType="VARCHAR" />
    <result column="bu_code" property="buCode" jdbcType="VARCHAR" />
    <result column="contact_name" property="contactName" jdbcType="VARCHAR" />
    <result column="email" property="email" jdbcType="VARCHAR" />
    <result column="telephone" property="telephone" jdbcType="VARCHAR" />
    <result column="active_indicator" property="activeIndicator" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, lab_id, trf_id, lab_code, bu_code, contact_name, email, telephone, active_indicator, 
    created_by, created_date, modified_by, modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfLabExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trf_lab
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_trf_lab
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_trf_lab
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfLabExample" >
    delete from tb_trf_lab
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfLabPO" >
    insert into tb_trf_lab (id, lab_id, trf_id, 
      lab_code, bu_code, contact_name, 
      email, telephone, active_indicator, 
      created_by, created_date, modified_by, 
      modified_date)
    values (#{id,jdbcType=BIGINT}, #{labId,jdbcType=INTEGER}, #{trfId,jdbcType=BIGINT}, 
      #{labCode,jdbcType=VARCHAR}, #{buCode,jdbcType=VARCHAR}, #{contactName,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{telephone,jdbcType=VARCHAR}, #{activeIndicator,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfLabPO" >
    insert into tb_trf_lab
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="labId != null" >
        lab_id,
      </if>
      <if test="trfId != null" >
        trf_id,
      </if>
      <if test="labCode != null" >
        lab_code,
      </if>
      <if test="buCode != null" >
        bu_code,
      </if>
      <if test="contactName != null" >
        contact_name,
      </if>
      <if test="email != null" >
        email,
      </if>
      <if test="telephone != null" >
        telephone,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="labId != null" >
        #{labId,jdbcType=INTEGER},
      </if>
      <if test="trfId != null" >
        #{trfId,jdbcType=BIGINT},
      </if>
      <if test="labCode != null" >
        #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="buCode != null" >
        #{buCode,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null" >
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfLabExample" resultType="java.lang.Integer" >
    select count(*) from tb_trf_lab
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trf_lab
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.labId != null" >
        lab_id = #{record.labId,jdbcType=INTEGER},
      </if>
      <if test="record.trfId != null" >
        trf_id = #{record.trfId,jdbcType=BIGINT},
      </if>
      <if test="record.labCode != null" >
        lab_code = #{record.labCode,jdbcType=VARCHAR},
      </if>
      <if test="record.buCode != null" >
        bu_code = #{record.buCode,jdbcType=VARCHAR},
      </if>
      <if test="record.contactName != null" >
        contact_name = #{record.contactName,jdbcType=VARCHAR},
      </if>
      <if test="record.email != null" >
        email = #{record.email,jdbcType=VARCHAR},
      </if>
      <if test="record.telephone != null" >
        telephone = #{record.telephone,jdbcType=VARCHAR},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trf_lab
    set id = #{record.id,jdbcType=BIGINT},
      lab_id = #{record.labId,jdbcType=INTEGER},
      trf_id = #{record.trfId,jdbcType=BIGINT},
      lab_code = #{record.labCode,jdbcType=VARCHAR},
      bu_code = #{record.buCode,jdbcType=VARCHAR},
      contact_name = #{record.contactName,jdbcType=VARCHAR},
      email = #{record.email,jdbcType=VARCHAR},
      telephone = #{record.telephone,jdbcType=VARCHAR},
      active_indicator = #{record.activeIndicator,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfLabPO" >
    update tb_trf_lab
    <set >
      <if test="labId != null" >
        lab_id = #{labId,jdbcType=INTEGER},
      </if>
      <if test="trfId != null" >
        trf_id = #{trfId,jdbcType=BIGINT},
      </if>
      <if test="labCode != null" >
        lab_code = #{labCode,jdbcType=VARCHAR},
      </if>
      <if test="buCode != null" >
        bu_code = #{buCode,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null" >
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        telephone = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfLabPO" >
    update tb_trf_lab
    set lab_id = #{labId,jdbcType=INTEGER},
      trf_id = #{trfId,jdbcType=BIGINT},
      lab_code = #{labCode,jdbcType=VARCHAR},
      bu_code = #{buCode,jdbcType=VARCHAR},
      contact_name = #{contactName,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      telephone = #{telephone,jdbcType=VARCHAR},
      active_indicator = #{activeIndicator,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_trf_lab
      (`id`,`lab_id`,`trf_id`,
      `lab_code`,`bu_code`,`contact_name`,
      `email`,`telephone`,`active_indicator`,
      `created_by`,`created_date`,`modified_by`,
      `modified_date`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.labId, jdbcType=INTEGER},#{ item.trfId, jdbcType=BIGINT},
      #{ item.labCode, jdbcType=VARCHAR},#{ item.buCode, jdbcType=VARCHAR},#{ item.contactName, jdbcType=VARCHAR},
      #{ item.email, jdbcType=VARCHAR},#{ item.telephone, jdbcType=VARCHAR},#{ item.activeIndicator, jdbcType=INTEGER},
      #{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},#{ item.modifiedBy, jdbcType=VARCHAR},
      #{ item.modifiedDate, jdbcType=TIMESTAMP}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_trf_lab 
      <set>
        <if test="item.labId != null"> 
          `lab_id` = #{item.labId, jdbcType = INTEGER},
        </if> 
        <if test="item.trfId != null"> 
          `trf_id` = #{item.trfId, jdbcType = BIGINT},
        </if> 
        <if test="item.labCode != null"> 
          `lab_code` = #{item.labCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.buCode != null"> 
          `bu_code` = #{item.buCode, jdbcType = VARCHAR},
        </if> 
        <if test="item.contactName != null"> 
          `contact_name` = #{item.contactName, jdbcType = VARCHAR},
        </if> 
        <if test="item.email != null"> 
          `email` = #{item.email, jdbcType = VARCHAR},
        </if> 
        <if test="item.telephone != null"> 
          `telephone` = #{item.telephone, jdbcType = VARCHAR},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = INTEGER},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>