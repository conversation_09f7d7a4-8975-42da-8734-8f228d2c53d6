<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfTestSampleMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.customerbiz.dbstorages.mybatis.model.TrfTestSamplePO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="trf_id" property="trfId" jdbcType="BIGINT" />
    <result column="test_sample_instance_id" property="testSampleInstanceId" jdbcType="VARCHAR" />
    <result column="parent_test_sample_id" property="parentTestSampleId" jdbcType="VARCHAR" />
    <result column="test_sample_no" property="testSampleNo" jdbcType="VARCHAR" />
    <result column="external_sample_no" property="externalSampleNo" jdbcType="VARCHAR" />
    <result column="test_sample_type" property="testSampleType" jdbcType="INTEGER" />
    <result column="test_sample_seq" property="testSampleSeq" jdbcType="INTEGER" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, trf_id, test_sample_instance_id, parent_test_sample_id, test_sample_no, external_sample_no, 
    test_sample_type, test_sample_seq, active_indicator, created_by, created_date, modified_by, 
    modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfTestSampleExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_trf_test_sample
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_trf_test_sample
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_trf_test_sample
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfTestSampleExample" >
    delete from tb_trf_test_sample
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfTestSamplePO" >
    insert into tb_trf_test_sample (id, trf_id, test_sample_instance_id, 
      parent_test_sample_id, test_sample_no, external_sample_no, 
      test_sample_type, test_sample_seq, active_indicator, 
      created_by, created_date, modified_by, 
      modified_date)
    values (#{id,jdbcType=BIGINT}, #{trfId,jdbcType=BIGINT}, #{testSampleInstanceId,jdbcType=VARCHAR}, 
      #{parentTestSampleId,jdbcType=VARCHAR}, #{testSampleNo,jdbcType=VARCHAR}, #{externalSampleNo,jdbcType=VARCHAR}, 
      #{testSampleType,jdbcType=INTEGER}, #{testSampleSeq,jdbcType=INTEGER}, #{activeIndicator,jdbcType=TINYINT}, 
      #{createdBy,jdbcType=VARCHAR}, #{createdDate,jdbcType=TIMESTAMP}, #{modifiedBy,jdbcType=VARCHAR}, 
      #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfTestSamplePO" >
    insert into tb_trf_test_sample
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="trfId != null" >
        trf_id,
      </if>
      <if test="testSampleInstanceId != null" >
        test_sample_instance_id,
      </if>
      <if test="parentTestSampleId != null" >
        parent_test_sample_id,
      </if>
      <if test="testSampleNo != null" >
        test_sample_no,
      </if>
      <if test="externalSampleNo != null" >
        external_sample_no,
      </if>
      <if test="testSampleType != null" >
        test_sample_type,
      </if>
      <if test="testSampleSeq != null" >
        test_sample_seq,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedBy != null" >
        modified_by,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="trfId != null" >
        #{trfId,jdbcType=BIGINT},
      </if>
      <if test="testSampleInstanceId != null" >
        #{testSampleInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="parentTestSampleId != null" >
        #{parentTestSampleId,jdbcType=VARCHAR},
      </if>
      <if test="testSampleNo != null" >
        #{testSampleNo,jdbcType=VARCHAR},
      </if>
      <if test="externalSampleNo != null" >
        #{externalSampleNo,jdbcType=VARCHAR},
      </if>
      <if test="testSampleType != null" >
        #{testSampleType,jdbcType=INTEGER},
      </if>
      <if test="testSampleSeq != null" >
        #{testSampleSeq,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfTestSampleExample" resultType="java.lang.Integer" >
    select count(*) from tb_trf_test_sample
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_trf_test_sample
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.trfId != null" >
        trf_id = #{record.trfId,jdbcType=BIGINT},
      </if>
      <if test="record.testSampleInstanceId != null" >
        test_sample_instance_id = #{record.testSampleInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.parentTestSampleId != null" >
        parent_test_sample_id = #{record.parentTestSampleId,jdbcType=VARCHAR},
      </if>
      <if test="record.testSampleNo != null" >
        test_sample_no = #{record.testSampleNo,jdbcType=VARCHAR},
      </if>
      <if test="record.externalSampleNo != null" >
        external_sample_no = #{record.externalSampleNo,jdbcType=VARCHAR},
      </if>
      <if test="record.testSampleType != null" >
        test_sample_type = #{record.testSampleType,jdbcType=INTEGER},
      </if>
      <if test="record.testSampleSeq != null" >
        test_sample_seq = #{record.testSampleSeq,jdbcType=INTEGER},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedBy != null" >
        modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_trf_test_sample
    set id = #{record.id,jdbcType=BIGINT},
      trf_id = #{record.trfId,jdbcType=BIGINT},
      test_sample_instance_id = #{record.testSampleInstanceId,jdbcType=VARCHAR},
      parent_test_sample_id = #{record.parentTestSampleId,jdbcType=VARCHAR},
      test_sample_no = #{record.testSampleNo,jdbcType=VARCHAR},
      external_sample_no = #{record.externalSampleNo,jdbcType=VARCHAR},
      test_sample_type = #{record.testSampleType,jdbcType=INTEGER},
      test_sample_seq = #{record.testSampleSeq,jdbcType=INTEGER},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_by = #{record.modifiedBy,jdbcType=VARCHAR},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfTestSamplePO" >
    update tb_trf_test_sample
    <set >
      <if test="trfId != null" >
        trf_id = #{trfId,jdbcType=BIGINT},
      </if>
      <if test="testSampleInstanceId != null" >
        test_sample_instance_id = #{testSampleInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="parentTestSampleId != null" >
        parent_test_sample_id = #{parentTestSampleId,jdbcType=VARCHAR},
      </if>
      <if test="testSampleNo != null" >
        test_sample_no = #{testSampleNo,jdbcType=VARCHAR},
      </if>
      <if test="externalSampleNo != null" >
        external_sample_no = #{externalSampleNo,jdbcType=VARCHAR},
      </if>
      <if test="testSampleType != null" >
        test_sample_type = #{testSampleType,jdbcType=INTEGER},
      </if>
      <if test="testSampleSeq != null" >
        test_sample_seq = #{testSampleSeq,jdbcType=INTEGER},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedBy != null" >
        modified_by = #{modifiedBy,jdbcType=VARCHAR},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.TrfTestSamplePO" >
    update tb_trf_test_sample
    set trf_id = #{trfId,jdbcType=BIGINT},
      test_sample_instance_id = #{testSampleInstanceId,jdbcType=VARCHAR},
      parent_test_sample_id = #{parentTestSampleId,jdbcType=VARCHAR},
      test_sample_no = #{testSampleNo,jdbcType=VARCHAR},
      external_sample_no = #{externalSampleNo,jdbcType=VARCHAR},
      test_sample_type = #{testSampleType,jdbcType=INTEGER},
      test_sample_seq = #{testSampleSeq,jdbcType=INTEGER},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_by = #{modifiedBy,jdbcType=VARCHAR},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_trf_test_sample
      (`id`,`trf_id`,`test_sample_instance_id`,
      `parent_test_sample_id`,`test_sample_no`,`external_sample_no`,
      `test_sample_type`,`test_sample_seq`,`active_indicator`,
      `created_by`,`created_date`,`modified_by`,
      `modified_date`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.trfId, jdbcType=BIGINT},#{ item.testSampleInstanceId, jdbcType=VARCHAR},
      #{ item.parentTestSampleId, jdbcType=VARCHAR},#{ item.testSampleNo, jdbcType=VARCHAR},#{ item.externalSampleNo, jdbcType=VARCHAR},
      #{ item.testSampleType, jdbcType=INTEGER},#{ item.testSampleSeq, jdbcType=INTEGER},#{ item.activeIndicator, jdbcType=TINYINT},
      #{ item.createdBy, jdbcType=VARCHAR},#{ item.createdDate, jdbcType=TIMESTAMP},#{ item.modifiedBy, jdbcType=VARCHAR},
      #{ item.modifiedDate, jdbcType=TIMESTAMP}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_trf_test_sample 
      <set>
        <if test="item.trfId != null"> 
          `trf_id` = #{item.trfId, jdbcType = BIGINT},
        </if> 
        <if test="item.testSampleInstanceId != null"> 
          `test_sample_instance_id` = #{item.testSampleInstanceId, jdbcType = VARCHAR},
        </if> 
        <if test="item.parentTestSampleId != null"> 
          `parent_test_sample_id` = #{item.parentTestSampleId, jdbcType = VARCHAR},
        </if> 
        <if test="item.testSampleNo != null"> 
          `test_sample_no` = #{item.testSampleNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.externalSampleNo != null"> 
          `external_sample_no` = #{item.externalSampleNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.testSampleType != null"> 
          `test_sample_type` = #{item.testSampleType, jdbcType = INTEGER},
        </if> 
        <if test="item.testSampleSeq != null"> 
          `test_sample_seq` = #{item.testSampleSeq, jdbcType = INTEGER},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = TINYINT},
        </if> 
        <if test="item.createdBy != null"> 
          `created_by` = #{item.createdBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedBy != null"> 
          `modified_by` = #{item.modifiedBy, jdbcType = VARCHAR},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>