<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.EventSubscribeExtMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.customerbiz.dbstorages.mybatis.model.EventSubscribePO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="subscriber" property="subscriber" jdbcType="INTEGER" />
    <result column="ref_system_id" property="refSystemId" jdbcType="INTEGER" />
    <result column="event_code" property="eventCode" jdbcType="INTEGER" />
    <result column="api_id" property="apiId" jdbcType="BIGINT" />
    <result column="priority" property="priority" jdbcType="INTEGER" />
    <result column="notify_rule" property="notifyRule" jdbcType="INTEGER" />
    <result column="notify_data" property="notifyData" jdbcType="INTEGER" />
    <result column="condition" property="condition" jdbcType="INTEGER" />
    <result column="condition_params" property="conditionParams" jdbcType="INTEGER" />
    <result column="handler_name" property="handlerName" jdbcType="VARCHAR" />
    <result column="error_handle" property="errorHandle" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_by" property="modifiedBy" jdbcType="VARCHAR" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, subscriber,ref_system_id, event_code, api_id, priority, notify_rule, notify_data, `condition`, `condition_params`, handler_name,
    error_handle, created_by, created_date, modified_by, modified_date
  </sql>
  <select id="get" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_cfg_event_subscribe
    where event_code = #{eventCode} and ref_system_id = #{refSystemId}
    order by priority desc
  </select>
  <select id="getBySubscriber" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_cfg_event_subscribe
    where event_code = #{eventCode} and subscriber = #{subscriber}
    order by priority desc
  </select>

  <select id="getBySubscriberId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_cfg_event_subscribe
    where event_code = #{eventCode} and api_id = #{apiId}
    order by priority desc
  </select>

  <select id="getEventCodeByRefSystem" resultType="java.lang.String" parameterType="java.lang.Integer">
    select event_code
    from tb_cfg_event_subscribe
where ref_system_id = #{refSystemId} and subscriber = #{refSystemId}
order by event_code asc
  </select>

</mapper>
