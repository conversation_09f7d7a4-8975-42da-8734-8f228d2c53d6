# TrfDomainService Summary

## Overview
`TrfDomainService` is a core domain service class in the customerbiz domain that manages Test Request Forms (TRF) throughout their lifecycle. It handles TRF creation, status management, binding/unbinding with orders, and various operations related to TRF processing.

## Key Responsibilities
- TRF creation and management
- TRF status control and transitions
- Order binding and unbinding
- Report management
- Customer integration

## Dependencies
The service depends on numerous other domain services and repositories:
- Various mapper classes for database operations
- Other domain services (TrfCustomerDomainService, TrfOrderDomainService, etc.)
- Configuration clients
- Kafka producer for event publishing

## Method Summary

### TRF Query Methods

| Method | Description |
|--------|-------------|
| `selectSimple(Integer refSystemId, String trfNo)` | Retrieves a simplified TRF header by reference system ID and TRF number |
| `selectActiveAndNotCancelByTrfNos(List<String> trfNo)` | Retrieves active and not canceled TRFs by TRF numbers |
| `selectByTrfNos(List<String> trfNo)` | Retrieves TRFs by TRF numbers |
| `selectActiveByTrfNo(String trfNo)` | Retrieves active TRFs by TRF number |
| `getByRefSystemIdOrThrow(Integer refSystemId, String trfNo, List<TrfInfoPO> trfList)` | Gets TRF by reference system ID or throws exception |
| `selectActiveAndNotCancelByTrfNo(String trfNo)` | Retrieves active and not canceled TRFs by TRF number |
| `selectByTrfNo(Integer refSystemId, String trfNo)` | Retrieves TRF by reference system ID and TRF number |
| `selectByTrfId(Long trfId)` | Retrieves TRF by TRF ID |
| `selectByTrfIds(List<Long> trfIds)` | Retrieves TRFs by TRF IDs |
| `selectByTrfIdsActive(List<Long> trfIds)` | Retrieves active TRFs by TRF IDs |
| `getTrfInfoByTrfNo(String trfNo, Integer refSystemId)` | Gets TRF info by TRF number and reference system ID |
| `getTrfBaseInfo(String trfNo, Integer refSystemId)` | Gets comprehensive TRF base information |
| `getTrfBaseInfo(String trfNo)` | Gets comprehensive TRF base information by TRF number only |

### TRF Creation and Update Methods

| Method | Description |
|--------|-------------|
| `createTrf(TrfDOV2 trfDO)` | Creates a new TRF with locking mechanism to prevent concurrent creation |
| `createTrfImpl(TrfDOV2 trfDO)` | Implementation of TRF creation within transaction |
| `save(TrfDOV2 trfDOV2)` | Saves TRF data and returns TRF ID and message request for Kafka |
| `updateTrf(TrfInfoPO trfInfoPO)` | Updates TRF information |
| `updateTrfStatus(TrfInfoPO trfInfoPO, Integer trfStatus)` | Updates TRF status |
| `updateByPrimaryKey(TrfInfoPO trfInfoPO)` | Updates TRF by primary key |
| `deleteTrf(Long trfId)` | Deletes a TRF by marking it inactive |
| `setupSampleLevelAndIntegrationLevel(TrfDOV2 trfDO)` | Sets up sample level and integration level for TRF |

### TRF Status Control Methods

| Method | Description |
|--------|-------------|
| `statusControl(TrfStatusControlDO ctrl)` | Controls TRF status transitions based on configuration |
| `doStatusControl(TrfStatusControlDO statusCtrlDo)` | Implementation of status control logic |
| `toOrder(TrfStatusControlDO statusControlDO)` | Transitions TRF to Order status |
| `toQuotation(TrfStatusControlDO statusControlDO)` | Transitions TRF to Quotation status |
| `confirm(TrfStatusControlDO statusControlDO)` | Confirms TRF |
| `testing(TrfStatusControlDO statusControlDO)` | Transitions TRF to Testing status |
| `reviseReport(TrfStatusControlDO statusControlDO)` | Handles report revision |
| `complete(TrfStatusControlDO trfDO)` | Completes TRF |
| `close(TrfStatusControlDO trfDO)` | Closes TRF |
| `calculateTrfStatus(Integer trfStatusTo, Integer trfCurrentStatus, String action)` | Calculates new TRF status based on current status and action |
| `calcTrfStatusIndex(Integer[] statusFlow, Integer trfStatus)` | Calculates TRF status index in status flow |

### Order Binding Methods

| Method | Description |
|--------|-------------|
| `bind(TrfStatusControlDO trfDO)` | Binds TRF to order |
| `bind(TrfHeaderDOV2 trfDO, TrfOrderDOV2 trfOrderDOV2)` | Binds TRF to order with different parameters |
| `unbind(TrfStatusControlDO statusCtrlDo)` | Unbinds TRF from order |
| `cancelOrder(TrfStatusControlDO statusCtrlDo)` | Cancels order associated with TRF |
| `checkBind(Long trfId)` | Checks if TRF can be bound |
| `checkUnbind(Long trfId, Integer systemId, TrfUnbindReq unbindReq)` | Checks if TRF can be unbound |
| `getTrfOrder(String trfNo, Integer refSystemId, String orderNo, String realOrderNo)` | Gets TRF order |
| `getTrfOrderList(Integer systemId, String orderNo)` | Gets TRF order list |

### Pending/Unpending Methods

| Method | Description |
|--------|-------------|
| `pending(TrfStatusControlDO statusCtrlDo)` | Sets TRF to pending status |
| `unpending(TrfStatusControlDO statusCtrlDo)` | Removes pending status from TRF |

### Report Management Methods

| Method | Description |
|--------|-------------|
| `createTrfReport(TrfInfoPO trfInfoPO, TrfReportDOV2 trfReportDO)` | Creates TRF report |
| `createTrfReport(TrfInfoPO trfInfoPO, List<TrfReportDOV2> trfReportDOList)` | Creates multiple TRF reports |
| `createTrfReportImpl(Long trfId, TrfReportDOV2 trfReportDO)` | Implementation of TRF report creation |
| `queryTrfReport(Integer systemId, String orderNo)` | Queries TRF reports |
| `getReportDueDate(Integer refSystemId, String trfNo)` | Gets report due date |
| `customerConfirmReport(Integer refSystemId, String trfNo, String reportNo)` | Confirms customer report |
| `queryReportConfirmFlag(Integer refSystemId, String trfNo, List<String> reportNoList)` | Queries report confirmation flags |
| `updateReportList(List<TrfReportDTO> reportList, Integer labId)` | Updates report list |
| `updateReportDeliveryFlagByTrfNo(String trfNo, Integer refSystemId)` | Updates report delivery flag by TRF number |

### Report Delivery Methods

| Method | Description |
|--------|-------------|
| `completedDeliveryReport(List<String> reportNoList, Long apiId)` | Marks reports as successfully delivered |
| `failedDeliveryReport(List<String> reportNoList, Long apiId)` | Marks reports as failed delivery |
| `conditionDeliveryReport(List<String> reportNoList, Long apiId)` | Marks reports as conditionally delivered |
| `repeatedDeliveryReport(List<String> reportNoList, Long apiId)` | Marks reports for repeated delivery |
| `markDeliveryReport(Long subscriberId, Integer subscriber, Long apiId, List<String> reportNoList)` | Marks delivery information for reports |
| `completedDeliveryQuotation(Long trfId, List<String> quotationNoList)` | Marks quotations as successfully delivered |
| `failedDeliveryQuotation(Long trfId, List<String> quotationNoList)` | Marks quotations as failed delivery |
| `markDeliveryQuotation(Long trfId, List<String> quotationNoList)` | Marks delivery information for quotations |

### Configuration Methods

| Method | Description |
|--------|-------------|
| `getTrfStatusControlConfig(Integer refSystemId, String buCode)` | Gets TRF status control configuration |
| `getTrfStatusControlConfig(Integer refSystemId, String buCode, String action, boolean thrEx)` | Gets TRF status control configuration with action |
| `getTrfStatusControlConfigOrDefaultConfig(TrfStatusControlDO statusControlDO, TrfInfoPO trfInfoPO)` | Gets TRF status control configuration or default configuration |
| `getTrfStatusControlConfig(TrfStatusControlDO statusCtrlDo, TrfInfoPO trfInfoPO, ErrorCode configErrorCode)` | Gets TRF status control configuration with error handling |
| `getStatusRule(TrfStatusControlDO statusCtrlDo, Integer nodeStatusRule, TrfInfoPO trfInfoPO, ErrorCode configErrorCode, Integer statusRule)` | Gets status rule |
| `updateTrfIntegrationLevel(TrfInfoPO trfInfoPO, TrfStatusControlConfig statusControlConfig)` | Updates TRF integration level |

### TRF Status and Relationship Methods

| Method | Description |
|--------|-------------|
| `calcIntegrationLevel(TrfDOV2 trfDO)` | Calculates integration level for TRF |
| `calcIntegrationLevel(Integer refSysytemId, String buCode)` | Calculates integration level by reference system ID and BU code |
| `getTrfOrderRelationshipRule(Integer refSystemId, String buCode)` | Gets TRF order relationship rule |
| `calcSampleLevel(TrfDOV2 trfDO)` | Calculates sample level for TRF |
| `checkCustomer(Integer refSystemId)` | Checks customer configuration |

### TRF Cancellation and Removal Methods

| Method | Description |
|--------|-------------|
| `returnTrf(TrfStatusControlDO trfDO)` | Returns TRF (marks as inactive) |
| `cancelTrf(TrfStatusControlDO statusCtrlDo)` | Cancels TRF |
| `deleteTodoInfoData(Integer refSystemId, String trfNo)` | Deletes todo info data |
| `deleteTrfReportLevel(String orderNo, String trfNo, Integer refSystemId)` | Deletes TRF report level |
| `deleteAllTrfReportLevel(String trfNo, Integer refSystemId)` | Deletes all TRF report levels |

### TRF Relationship Methods

| Method | Description |
|--------|-------------|
| `buildTrfRel(TrfStatusControlDO statusCtrlDo, TrfInfoPO trfInfoPO, TrfActionStatusRule actionStatusRule)` | Builds TRF relationships |
| `buildTrfReportRel(TrfStatusControlDO statusCtrlDo, TrfInfoPO trfInfoPO)` | Builds TRF report relationships |
| `getBoundTrfInfoList(BoundTrfInfoSearchReq reqObject)` | Gets bound TRF info list |
| `getBoundTrfInfoList(BoundTrfInfoSearchReq reqObject, List<Integer> allTrfStatus)` | Gets bound TRF info list with status filter |
| `getBoundTrfStatus()` | Gets bound TRF status list |
| `getAllTrfStatus()` | Gets all TRF status list |
| `getUnBoundTrfStatus()` | Gets unbound TRF status list |

### Customer TRF Methods

| Method | Description |
|--------|-------------|
| `saveCustomerTrf(CustomerTrfInfoPO trf)` | Saves customer TRF |
| `batchSaveCustomerTrf(List<CustomerTrfInfoPO> trfs)` | Batch saves customer TRFs |

### Utility Methods

| Method | Description |
|--------|-------------|
| `partition(List<?> list, int pageSize, int pageNum)` | Partitions list for pagination |
| `convertTrfHeader(TrfInfoPO trfInfoPO, TrfServiceRequirementDOV2 trfServiceRequirementPOS, List<TrfAttachmentPO> trfAttachment)` | Converts TRF info to header |
| `getResultTrfDO(TrfInfoPO trfInfoPO)` | Gets result TRF DO |
| `checkStatusCtrlDoParam(TrfStatusControlDO statusCtrlDo, ErrorCode errorCode)` | Checks status control parameters |
| `checkStatusCtrlDoData(TrfStatusControlDO statusCtrlDo, ErrorCode errorCode, TrfOrderDOV2 order)` | Checks status control data |
| `checkOrderInfo(TrfOrderDOV2 order, ErrorCode errorCode)` | Checks order info |
| `checkOrderInfo(TrfOrderDOV2 order, String The_order_status_is_required)` | Checks order info with message |
| `checkOrderSystemId(List<TrfOrderDOV2> trfOrderList, TrfOrderDOV2 order, ErrorCode configErrorCode)` | Checks order system ID |
| `getRequest(TrfStatusControlDO statusCtrlDo, TrfInfoPO trfInfoPO, TrfStatusControlConfig statusControlConfig, TrfActionStatusRule actionStatusRule, Map<String, Integer> statusMapping, Integer[] statusFlow, TrfOrderDOV2 order, List<TrfOrderDOV2> trfOrderList)` | Gets request for status calculation |
| `getInterfaceExclude(String trfNo, Integer refSystemId)` | Gets interface exclude flag |
| `saveTrfReportLevelData(List<String> externalSampleNoList, String trfNo, Integer refSystemId, String orderNo, String bizType)` | Saves TRF report level data |
| `updateTrfStatusAndDeliveryFlag(String trfNo, Integer refSystemId, Optional<Integer> changeStatus, List<TrfReportDOV2> reportList)` | Updates TRF status and delivery flag |
| `queryInfoByTrfNo(String trfNo, Integer refSystemId)` | Queries info by TRF number |
| `needSendTrfUpdatedToKafka(TrfInfoPO trfInfoPO)` | Checks if TRF update should be sent to Kafka |

## Key Business Logic

1. **TRF Creation Process**:
   - Creates TRF header
   - Saves customer information
   - Saves lab information
   - Saves service requirements
   - Saves attachments
   - Saves product and sample information
   - Saves test samples and test items

2. **Status Control Mechanism**:
   - Uses configuration-driven status transitions
   - Implements different status control strategies
   - Validates status transitions against allowed rules
   - Maintains status history through logs

3. **Order Binding/Unbinding**:
   - Manages relationships between TRFs and orders
   - Handles 1:1, 1:N, and N:1 relationships
   - Updates TRF status based on order status changes

4. **Report Management**:
   - Creates and updates reports
   - Manages report delivery status
   - Handles customer confirmation of reports
   - Supports report revision process

5. **Integration Level Management**:
   - Calculates and maintains integration levels
   - Supports different customer integration modes
   - Uses configuration to determine behavior

## Key Business Rules

1. TRF status can only transition according to configured rules
2. Completed or closed TRFs can only transition to Revise status
3. TRF in Revise status can only transition to Completed status
4. TRF status is influenced by the status of bound orders
5. Different integration levels (1:1, 1:N, N:1) have different behavior rules
6. Pending/unpending affects TRF processing
7. Canceled TRFs cannot be modified
