package com.sgs.customerbiz.domain.enums;

/**
 * <AUTHOR>
 */

public enum TrfNotifyActionEnum {
    syncCallback("syncCallback", "回调通知"),
    syncCancelTrf("syncCancelTrf", "cancel通知");
    private final String code;
    private final String desc;

    TrfNotifyActionEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
