package com.sgs.customerbiz.domain.service.strategy;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sgs.customerbiz.core.annotation.EventTypeAnnotation;
import com.sgs.customerbiz.core.util.DateUtils;
import com.sgs.customerbiz.dbstorages.mybatis.extmodel.info.callbackinfo.TRFCommonCallbackInfo;
import com.sgs.customerbiz.domain.domainobject.TrfStrategyServiceExtObjDO;
import com.sgs.customerbiz.facade.model.trf.UpdateTrfStatusInfo;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.EventTypeEnum;
import com.sgs.framework.model.enums.TrfStatusEnum;
import com.sgs.framework.model.trf.TrfStatusInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * @ClassName TRFCompletedService
 * @Description TRF相关策略类
 * <AUTHOR>
 * @Date 2023/2/20
 */
@Service
@Slf4j
@EventTypeAnnotation(
        eventType={
            EventTypeEnum.Completed
        }
)
public class TRFCompletedService extends TRFAbstractBaseInterface<TRFCommonCallbackInfo> {
    @Override
    public CustomResult<TRFCommonCallbackInfo> invoke(TrfStatusInfo trfStatusInfo, TrfStrategyServiceExtObjDO extObj) {
        String logID = extObj.getLogID();
        log.info("logId:{}[TRFCompletedService]执行",logID);

        CustomResult<TRFCommonCallbackInfo> result = CustomResult.newSuccessInstance();

        TRFCommonCallbackInfo info = new TRFCommonCallbackInfo();
        UpdateTrfStatusInfo updateTrf = new UpdateTrfStatusInfo();
        updateTrf.setRefSystemId(trfStatusInfo.getRefSystemId());
        updateTrf.setTrfNos(trfStatusInfo.getTrfNos());
        updateTrf.setTrfStatus(TrfStatusEnum.Detected.getStatus());
        updateTrf.setModifiedBy(extObj.getUserName());
        updateTrf.setModifiedDate(DateUtils.getNow());
        //sl 的更新状态就可以了，非SL 需要给ialyer发MQ
        if(super.productLineCodeIsSL()){
            info.setUpdateTrfStatusInfo(updateTrf);
            result.setData(info);
            log.info("logId:{}-[TRFCompletedService]-SL数据组装完成",logID);
            return result;
        }

        //GPO的进来需要先check
        extObj.setCheckCommonParam(true);
        result =  super.checkParameter(trfStatusInfo,extObj);
        if(!result.isSuccess()){
            return result;
        }
        //参数进行透传
        Set<String> trfNos = trfStatusInfo.getTrfNos();
        List<String> trfNoList = Lists.newArrayList(trfNos);
        trfNoList.sort(String::compareTo);

        String key = StringUtils.join(trfNoList, "_");
        key = StringUtils.substring(key,0,key.length()>50?50:(key.length()-1));
        log.info("logId:{} [TRFCompletedService] 定义MQ Key：{}",logID,key);

        String contentReq = trfStatusInfo.getContentReq();
        JSONObject bodyContent = JSONObject.parseObject(contentReq);

        info.setBodyContent(bodyContent);
        info.setMqKey(key);
        info.setRequestId(logID);
        info.setUpdateTrfStatusInfo(updateTrf);
        log.info("logId:{} [TRFCompletedService] 数据组装完成",logID);

        result.setData(info);
        return result;
    }

    @Override
    public CustomResult doDeal(TRFCommonCallbackInfo callbackInfo) {
        log.info("logId:{} [TRFCompletedService] 执行doDeal",callbackInfo.getRequestId());
        return super.doDealForCallback(callbackInfo);
    }
}
