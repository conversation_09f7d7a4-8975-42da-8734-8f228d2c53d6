package com.sgs.customerbiz.domain.service.strategy;

import com.sgs.customerbiz.core.annotation.EventTypeAnnotation;
import com.sgs.customerbiz.dbstorages.mybatis.extmodel.info.callbackinfo.TRFCommonCallbackInfo;
import com.sgs.customerbiz.domain.domainobject.TrfStrategyServiceExtObjDO;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.EventTypeEnum;
import com.sgs.framework.model.trf.TrfStatusInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @ClassName TRFEnquiryCompletedService
 * @Description TRF相关策略类
 * <AUTHOR>
 * @Date 2023/2/20
 */
@Service
@Slf4j
@EventTypeAnnotation(
        eventType={
            EventTypeEnum.EnquiryCompleted
        }
)
public class TRFEnquiryCompletedService extends TRFAbstractBaseInterface<TRFCommonCallbackInfo> {
    @Override
    public CustomResult<TRFCommonCallbackInfo> invoke(TrfStatusInfo trfStatusInfo, TrfStrategyServiceExtObjDO extObj) {
        CustomResult<TRFCommonCallbackInfo> result = CustomResult.newSuccessInstance();
        result.setIgnore(true);
        result.setData(new TRFCommonCallbackInfo());
        return result;
    }

    @Override
    public CustomResult doDeal(TRFCommonCallbackInfo callbackInfo) {
        return super.doDealForCallback(callbackInfo);
    }
}
