package com.sgs.customerbiz.domain.domainobject.v2;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class TrfCareLabelDOV2 implements Serializable {
    private Long id;

    private Long trfId;

    private List<String> testSampleIds;

    private String careInstruction;

    private Integer radioType;

    private String selectCountry;

    private Integer careLabelSeq;

    private List<String> imgArray;


    private List<String> selectImgIds;


    private String cloudId;


    private String careLabelFilePath;

    private String careLabelFileId;


    private String productItemNo;


}
