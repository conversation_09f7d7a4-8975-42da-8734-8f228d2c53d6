package com.sgs.customerbiz.domain.convertor;

import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.dbstorages.mybatis.model.*;
import com.sgs.customerbiz.domain.domainobject.v2.*;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.framework.tool.utils.CollectionUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.ObjectUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/22 15:13
 */
public abstract class TrfInfoPOConvertor {


    public static TrfDOV2 convertTrfInfoPO(TrfInfoPO trfInfoPO) {
        TrfDOV2 trfDO = new TrfDOV2();

        TrfHeaderDOV2 header = new TrfHeaderDOV2();
        BeanUtils.copyProperties(trfInfoPO, header);
        header.setTrfId(trfInfoPO.getId());
        header.setTrfStatus(trfInfoPO.getStatus());
        trfDO.setHeader(header);
        return trfDO;
    }

    public static TrfDTO convertTrfDTO(TrfInfoPO trfInfoPO, CustomerTrfInfoRsp customerTrf) {
        TrfDTO trfDTO = new TrfDTO();
        TrfHeaderDTO header = new TrfHeaderDTO();
        BeanUtils.copyProperties(trfInfoPO, header);
        header.setCustomerTrfContent(Func.isNotEmpty(customerTrf) ? customerTrf.getContent() : null);
        header.setTrfId(trfInfoPO.getId());
        header.setTrfStatus(trfInfoPO.getStatus());
        header.setTrfReportLevel(trfInfoPO.getTrfReportLevel());
        TrfOtherDTO others = new TrfOtherDTO();
        TrfPendingDTO trfPendingDTO = new TrfPendingDTO();
        trfPendingDTO.setPendingFlag(trfInfoPO.getPendingFlag());
        trfPendingDTO.setPendingType(trfInfoPO.getPendingType());
        trfPendingDTO.setPendingRemark(trfInfoPO.getPendingRemark());
        others.setPending(trfPendingDTO);
        TrfCancelDTO trfCancelDTO = new TrfCancelDTO();
        trfCancelDTO.setCancelType(trfInfoPO.getCancelType());
        trfCancelDTO.setCancelReason(trfInfoPO.getCancelRemark());
        trfCancelDTO.setSamplePhotos(null);
        trfCancelDTO.setMaterialItem(null);
        others.setCancel(trfCancelDTO);
        TrfRemoveDTO trfRemoveDTO = new TrfRemoveDTO();
        trfRemoveDTO.setRemoveType(trfInfoPO.getRemoveType());
        trfRemoveDTO.setRemoveReason(trfInfoPO.getRemoveRemark());
        others.setRemove(trfRemoveDTO);
        others.setTrfRemark(trfInfoPO.getTrfRemark());
        header.setOthers(others);
        // TODO 待确认处理
//        header.setTrfSubmissionDate();
        trfDTO.setHeader(header);
        return trfDTO;
    }

    public static void convertTrfProductDTO(TrfDTO trfDTO, List<TrfProductPO> trfProductPOList, List<TrfProductAttrPO> trfProductAttrPOList) {
        TrfProductDTO trfProductDTO = convertTrfProductDTO(trfProductPOList, trfProductAttrPOList);
        trfDTO.setProduct(trfProductDTO);
    }

    public static TrfProductDTO convertTrfProductDTO(List<TrfProductPO> trfProductPOList, List<TrfProductAttrPO> trfProductAttrPOList) {
        TrfProductDTO trfProduct = new TrfProductDTO();
        List<TrfProductDTO> trfProductDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(trfProductPOList)) {
            for (TrfProductPO trfProductPO : trfProductPOList) {
                BeanUtils.copyProperties(trfProductPO, trfProduct);
                trfProductDTOList.add(trfProduct);
            }
            trfProduct = trfProductDTOList.get(Constants.ZERO);
        }
        if (!CollectionUtils.isEmpty(trfProductAttrPOList)) {
            // 先试用labCode 分组，用于拼凑TrfProductAttrDTO下的 languageList
            Map<String, List<TrfProductAttrPO>> trfProductAttrPOMap = trfProductAttrPOList.stream()
//                    .sorted(Comparator.comparing(trfProductAttrPO -> trfProductAttrPO.getAttrSeq()))
                    .collect(Collectors.groupingBy(TrfProductAttrPO::getLabelCode));
            List<TrfProductAttrLangDTO> trfProductAttrLangDTOList;
            List<TrfProductAttrDTO> trfProductAttrDTOArrayList = new ArrayList<>();
            TrfProductAttrDTO trfProductAttrDTO;
            for (String labCode : trfProductAttrPOMap.keySet()) {
                trfProductAttrLangDTOList = new ArrayList<>();
                List<TrfProductAttrPO> trfProductAttrPOS = trfProductAttrPOMap.get(labCode);
                trfProductAttrDTO = new TrfProductAttrDTO();
                if (CollectionUtil.isNotEmpty(trfProductAttrPOS)) {
                    BeanUtils.copyProperties(trfProductAttrPOS.get(Constants.ZERO), trfProductAttrDTO);
                }
                TrfProductAttrLangDTO trfProductAttrLangDTO;
                for (TrfProductAttrPO trfProductAttrPO : trfProductAttrPOS) {
                    trfProductAttrLangDTO = new TrfProductAttrLangDTO();
                    BeanUtils.copyProperties(trfProductAttrPO, trfProductAttrLangDTO);
                    trfProductAttrLangDTOList.add(trfProductAttrLangDTO);
                }
                trfProductAttrDTO.setLanguageList(trfProductAttrLangDTOList);
                trfProductAttrDTOArrayList.add(trfProductAttrDTO);
                trfProduct.setProductAttrList(trfProductAttrDTOArrayList);
            }
        }
        return trfProduct;
    }

    public static TrfProductDOV2 convertTrfProductDO(List<TrfProductPO> trfProductPOList, List<TrfProductAttrPO> trfProductAttrPOList) {
        TrfProductDOV2 trfProduct = new TrfProductDOV2();
        List<TrfProductDOV2> trfProductDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(trfProductPOList)) {
            for (TrfProductPO trfProductPO : trfProductPOList) {
                BeanUtils.copyProperties(trfProductPO, trfProduct);
                trfProductDTOList.add(trfProduct);
            }
            trfProduct = trfProductDTOList.get(Constants.ZERO);
        }
        if (!CollectionUtils.isEmpty(trfProductAttrPOList)) {
            // 先试用labCode 分组，用于拼凑TrfProductAttrDTO下的 languageList
            Map<String, List<TrfProductAttrPO>> trfProductAttrPOMap = trfProductAttrPOList.stream()
//                    .sorted(Comparator.comparing(trfProductAttrPO -> trfProductAttrPO.getAttrSeq()))
                    .collect(Collectors.groupingBy(TrfProductAttrPO::getLabelCode));
            List<TrfProductAttrLangDOV2> trfProductAttrLangDTOList;
            List<TrfProductAttrDOV2> trfProductAttrDTOArrayList = new ArrayList<>();
            TrfProductAttrDOV2 trfProductAttrDTO;
            for (String labCode : trfProductAttrPOMap.keySet()) {
                trfProductAttrLangDTOList = new ArrayList<>();
                List<TrfProductAttrPO> trfProductAttrPOS = trfProductAttrPOMap.get(labCode);
                trfProductAttrDTO = new TrfProductAttrDOV2();
                if (CollectionUtil.isNotEmpty(trfProductAttrPOS)) {
                    BeanUtils.copyProperties(trfProductAttrPOS.get(Constants.ZERO), trfProductAttrDTO);
                }
                TrfProductAttrLangDOV2 trfProductAttrLangDTO;
                for (TrfProductAttrPO trfProductAttrPO : trfProductAttrPOS) {
                    trfProductAttrLangDTO = new TrfProductAttrLangDOV2();
                    BeanUtils.copyProperties(trfProductAttrPO, trfProductAttrLangDTO);
                    trfProductAttrLangDTOList.add(trfProductAttrLangDTO);
                }
                trfProductAttrDTO.setLanguageList(trfProductAttrLangDTOList);
                trfProductAttrDTOArrayList.add(trfProductAttrDTO);
                trfProduct.setProductAttrList(trfProductAttrDTOArrayList);
            }
        }
        return trfProduct;
    }

    public static void convertTrfTestItemDTO(TrfDTO trfDTO, List<TrfTestItemPO> trfTestItemPOList) {
        List<TrfTestItemDTO> trfTestItemDTOList = new ArrayList<>();
        TrfTestItemDTO trfTestItemDTO = new TrfTestItemDTO();
        if (CollectionUtil.isNotEmpty(trfTestItemPOList)) {
            for (TrfTestItemPO trfTestItemPO : trfTestItemPOList) {
                BeanUtils.copyProperties(trfTestItemPO, trfTestItemDTO);
                trfTestItemDTOList.add(trfTestItemDTO);
            }
        }
        trfDTO.setTestLineList(trfTestItemDTOList);
    }

    public static void convertTrfOrderDTO(TrfDTO trfDTO, List<TrfOrderDOV2> trfOrderDOV2List) {
        List<TrfOrderDTO> trfOrderDTOList = new ArrayList<>();
        TrfOrderDTO trfOrderDTO = new TrfOrderDTO();
        if (CollectionUtil.isNotEmpty(trfOrderDOV2List)) {
            for (TrfOrderDOV2 trfOrderDOV2 : trfOrderDOV2List) {
                BeanUtils.copyProperties(trfOrderDOV2, trfOrderDTO);
                trfOrderDTOList.add(trfOrderDTO);
            }
        }
        if (CollectionUtil.isNotEmpty(trfOrderDTOList)) {
            trfDTO.setOrder(trfOrderDTOList.get(Constants.ZERO));
        }
    }

    public static void convertTrfCareLabelDTO(TrfDTO trfDTO, List<TrfCareLabelPO> trfCareLabelPOList) {
        List<TrfCareLabelDTO> trfCareLabelDTOList = new ArrayList<>();
        TrfCareLabelDTO trfCareLabelDTO = new TrfCareLabelDTO();
        if (CollectionUtil.isNotEmpty(trfCareLabelPOList)) {
            for (TrfCareLabelPO trfCareLabelPO : trfCareLabelPOList) {
                BeanUtils.copyProperties(trfCareLabelPO, trfCareLabelDTO);
                trfCareLabelDTOList.add(trfCareLabelDTO);
            }
        }
        trfDTO.setCareLabelList(trfCareLabelDTOList);
    }

    public static void convertTrfServiceRequirementDOV2(TrfDTO trfDTO, TrfServiceRequirementDOV2 trfServiceRequirementDOV2) {
        List<TrfServiceRequirementDTO> serviceRequirementDTOS = new ArrayList<>();
        TrfServiceRequirementDTO trfServiceRequirementDTO = new TrfServiceRequirementDTO();
        if (ObjectUtil.isNotEmpty(trfServiceRequirementDOV2)) {
            TrfServiceRequirementReportDTO trfServiceRequirementReportDTO = new TrfServiceRequirementReportDTO();
            BeanUtils.copyProperties(trfServiceRequirementDOV2.getReport(), trfServiceRequirementReportDTO);
            if (ObjectUtil.isNotEmpty(trfServiceRequirementDOV2.getReport())) {
                TrfDeliveryDOV2 trfSoftDeliveryDOV2 = trfServiceRequirementDOV2.getReport().getSoftcopy();
                TrfDeliveryDOV2 trfHardDeliveryDOV2 = trfServiceRequirementDOV2.getReport().getHardcopy();
                TrfDeliveryDTO trfDeliveryDTO = new TrfDeliveryDTO();
                if(Objects.nonNull(trfSoftDeliveryDOV2)) {
                    BeanUtils.copyProperties(trfSoftDeliveryDOV2, trfDeliveryDTO);
                }
                trfServiceRequirementReportDTO.setSoftcopy(trfDeliveryDTO);
                trfDeliveryDTO = new TrfDeliveryDTO();
                if(Objects.nonNull(trfHardDeliveryDOV2)) {
                    BeanUtils.copyProperties(trfHardDeliveryDOV2, trfDeliveryDTO);
                }
                trfServiceRequirementReportDTO.setHardcopy(trfDeliveryDTO);
            }
            if (ObjectUtil.isNotEmpty(trfServiceRequirementReportDTO) && trfServiceRequirementReportDTO.getReportLanguage() == Constants.FOUR.intValue()) {
                trfServiceRequirementReportDTO.setReportLanguage(Constants.ONE);
            }
            trfServiceRequirementDTO.setReport(trfServiceRequirementReportDTO);
            TrfServiceRequirementSampleDTO sample = new TrfServiceRequirementSampleDTO();
            BeanUtils.copyProperties(trfServiceRequirementDOV2.getSample(), sample);
            trfServiceRequirementDTO.setSample(sample);

            TrfServiceRequirementInvoiceDTO invoice = new TrfServiceRequirementInvoiceDTO();
            BeanUtils.copyProperties(trfServiceRequirementDOV2.getInvoice(), invoice);
            trfServiceRequirementDTO.setInvoice(invoice);

            trfServiceRequirementDTO.setOtherRequestRemark(trfServiceRequirementDOV2.getOtherRequestRemark());
            serviceRequirementDTOS.add(trfServiceRequirementDTO);

        }
        if (!CollectionUtils.isEmpty(serviceRequirementDTOS)) {
            trfDTO.setServiceRequirement(serviceRequirementDTOS.get(Constants.ZERO));
        }
    }

    public static void convertTrfLabDTO(TrfDTO trfDTO, TrfLabPO trfLabPO) {
        TrfHeaderDTO header = trfDTO.getHeader();
        TrfLabDTO trfLabDTO = new TrfLabDTO();
        if (ObjectUtil.isNotEmpty(trfLabPO)) {
            BeanUtils.copyProperties(trfLabPO, trfLabDTO);
            if (Func.isNotEmpty(trfLabPO.getLabId())) {
                trfLabDTO.setLabId(Long.parseLong(trfLabPO.getLabId().toString()));
            }
            header.setLab(trfLabDTO);
            trfDTO.setHeader(header);
        }
    }

    public static void convertTrfTestSampleDTO(TrfDTO trfDTO, List<TrfTestSamplePO> trfTestSamplePOList) {
        List<TrfTestSampleDTO> trfTestSampleDTOList = new ArrayList<>();
        TrfTestSampleDTO trfTestSampleDTO;
        if (CollectionUtil.isNotEmpty(trfTestSamplePOList)) {
            for (TrfTestSamplePO trfTestSamplePO : trfTestSamplePOList) {
                trfTestSampleDTO = new TrfTestSampleDTO();
                BeanUtils.copyProperties(trfTestSamplePO, trfTestSampleDTO);
                trfTestSampleDTOList.add(trfTestSampleDTO);
            }
        }
        trfDTO.setTestSampleList(trfTestSampleDTOList);
    }

    public static void convertTrfAttachmentDTO(TrfDTO trfDTO, List<TrfAttachmentPO> trfAttachmentPOs) {
        List<TrfFileDTO> trfFileDTOList = new ArrayList<>();
        TrfFileDTO trfFileDTO = new TrfFileDTO();
        if (CollectionUtil.isNotEmpty(trfAttachmentPOs)) {
            for (TrfAttachmentPO trfAttachmentPO : trfAttachmentPOs) {
                BeanUtils.copyProperties(trfAttachmentPO, trfFileDTO);
                trfFileDTOList.add(trfFileDTO);
            }
        }
        trfDTO.setAttachmentList(trfFileDTOList);
    }

    public static void convertTrfCustomerDTO(TrfDTO trfDTO, List<TrfCustomerDOV2> trfCustomerDOV2List) {
        List<TrfCustomerDTO> trfCustomerDTOList = new ArrayList<>();
        TrfCustomerDTO trfCustomerDTO;
        if (CollectionUtil.isNotEmpty(trfCustomerDOV2List)) {
            for (TrfCustomerDOV2 trfCustomerDOV2 : trfCustomerDOV2List) {
                trfCustomerDTO = new TrfCustomerDTO();
                BeanUtils.copyProperties(trfCustomerDOV2, trfCustomerDTO);
                if (ObjectUtil.isNotEmpty(trfCustomerDOV2.getCustomerContactList())) {
                    List<TrfCustomerContactDOV2> trfCustomerContactDOV2List = trfCustomerDOV2.getCustomerContactList();
                    List<TrfCustomerContactDTO> customerContactDTOS = new ArrayList<>();
                    trfCustomerContactDOV2List.stream().forEach(trfCustomerContactDOV2 -> {
                        TrfCustomerContactDTO trfCustomerContactDTO = new TrfCustomerContactDTO();
                        BeanUtils.copyProperties(trfCustomerContactDOV2, trfCustomerContactDTO);
                        customerContactDTOS.add(trfCustomerContactDTO);
                    });
                    trfCustomerDTO.setCustomerContactList(customerContactDTOS);
                }
                trfCustomerDTOList.add(trfCustomerDTO);
            }
        }
        trfDTO.setCustomerList(trfCustomerDTOList);
    }

    public static void convertTrfSampleDffDto(TrfDTO trfDTO, List<TrfProductPO> trfProductPOList, List<TrfProductAttrPO> trfProductAttrPOList) {
        TrfSampleDffDTO trfProduct = new TrfSampleDffDTO();
        List<TrfSampleDffDTO> trfProductDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(trfProductPOList)) {
            for (TrfProductPO trfProductPO : trfProductPOList) {
                BeanUtils.copyProperties(trfProductPO, trfProduct);
                trfProductDTOList.add(trfProduct);
            }
            trfDTO.setSampleList(trfProductDTOList);
        }
        if (!CollectionUtils.isEmpty(trfProductAttrPOList)) {
            // 先试用labCode 分组，用于拼凑TrfProductAttrDTO下的 languageList
            Map<String, List<TrfProductAttrPO>> trfProductAttrPOMap = trfProductAttrPOList.stream()
//                    .sorted(Comparator.comparing(trfProductAttrPO -> trfProductAttrPO.getAttrSeq()))
                    .collect(Collectors.groupingBy(TrfProductAttrPO::getLabelCode));
            List<TrfProductAttrLangDTO> trfProductAttrLangDTOList;
            List<TrfSampleAttrDTO> trfProductAttrDTOArrayList = new ArrayList<>();
            TrfSampleAttrDTO trfProductAttrDTO;
            for (String labCode : trfProductAttrPOMap.keySet()) {
                trfProductAttrLangDTOList = new ArrayList<>();
                List<TrfProductAttrPO> trfProductAttrPOS = trfProductAttrPOMap.get(labCode);
                trfProductAttrDTO = new TrfSampleAttrDTO();
                if (CollectionUtil.isNotEmpty(trfProductAttrPOS)) {
                    BeanUtils.copyProperties(trfProductAttrPOS.get(Constants.ZERO), trfProductAttrDTO);
                }
                TrfProductAttrLangDTO trfProductAttrLangDTO;
                for (TrfProductAttrPO trfProductAttrPO : trfProductAttrPOS) {
                    trfProductAttrLangDTO = new TrfProductAttrLangDTO();
                    BeanUtils.copyProperties(trfProductAttrPO, trfProductAttrLangDTO);
                    trfProductAttrLangDTOList.add(trfProductAttrLangDTO);
                }
                trfProductAttrDTO.setLanguageList(trfProductAttrLangDTOList);
                trfProductAttrDTOArrayList.add(trfProductAttrDTO);
                trfProduct.setSampleAttrList(trfProductAttrDTOArrayList);
            }
        }
    }

    public static List<TrfSampleDffDOV2> convertTrfSampleDffDO(List<TrfProductPO> trfProductPOList, List<TrfProductAttrPO> trfProductAttrPOList) {
        Map<Long, TrfSampleDffDOV2> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(trfProductPOList)) {
            for (TrfProductPO trfProductPO : trfProductPOList) {
                TrfSampleDffDOV2 trfProduct = new TrfSampleDffDOV2();
                BeanUtils.copyProperties(trfProductPO, trfProduct);
                map.put(trfProductPO.getId(), trfProduct);
            }
        }
        if (!CollectionUtils.isEmpty(trfProductAttrPOList)) {
            // 先试用labCode 分组，用于拼凑TrfProductAttrDTO下的 languageList
            Map<Long, List<TrfProductAttrPO>> collect = trfProductAttrPOList.stream().collect(Collectors.groupingBy(TrfProductAttrPO::getTrfProductId));

//            Map<String, List<TrfProductAttrPO>> trfProductAttrPOMap = trfProductAttrPOList.stream()
////                    .sorted(Comparator.comparing(trfProductAttrPO -> trfProductAttrPO.getAttrSeq()))
//                    .collect(Collectors.groupingBy(TrfProductAttrPO::getLabelCode));
            for (Long trfProductId : collect.keySet()) {
                List<TrfProductAttrPO> trfProductAttrPOS = collect.get(trfProductId);

                List<TrfSampleAttrDOV2> trfProductAttrDTOArrayList = new ArrayList<>();
                Map<String, List<TrfProductAttrPO>> trfProductAttrPOMap = trfProductAttrPOS.stream()
                        .collect(Collectors.groupingBy(TrfProductAttrPO::getLabelCode));
                for (String labCode : trfProductAttrPOMap.keySet()) {
                    List<TrfProductAttrLangDOV2> trfProductAttrLangDTOList = new ArrayList<>();
                    List<TrfProductAttrPO> productAttrPOS = trfProductAttrPOMap.get(labCode);
                    TrfSampleAttrDOV2 trfProductAttrDTO = new TrfSampleAttrDOV2();
                    if (CollectionUtil.isNotEmpty(productAttrPOS)) {
                        BeanUtils.copyProperties(productAttrPOS.get(Constants.ZERO), trfProductAttrDTO);
                    }
                    TrfProductAttrLangDOV2 trfProductAttrLangDTO;
                    for (TrfProductAttrPO trfProductAttrPO : productAttrPOS) {
                        trfProductAttrLangDTO = new TrfProductAttrLangDOV2();
                        BeanUtils.copyProperties(trfProductAttrPO, trfProductAttrLangDTO);
                        trfProductAttrLangDTOList.add(trfProductAttrLangDTO);
                    }
                    trfProductAttrDTO.setLanguageList(trfProductAttrLangDTOList);
                    trfProductAttrDTOArrayList.add(trfProductAttrDTO);
                    TrfSampleDffDOV2 trfSampleDffDOV2 = map.get(trfProductAttrDTO.getTrfProductId());
                    trfSampleDffDOV2.setSampleAttrList(trfProductAttrDTOArrayList);

                    map.put(trfProductAttrDTO.getTrfProductId(), trfSampleDffDOV2);
                }


            }
        }
        return new ArrayList<>(map.values());
    }
}
