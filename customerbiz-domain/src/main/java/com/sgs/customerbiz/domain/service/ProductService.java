//package com.sgs.customerbiz.domain.service;
//
//import com.alibaba.dubbo.common.utils.CollectionUtils;
//import com.sgs.customerbiz.core.util.DateUtils;
//import com.sgs.customerbiz.core.util.NumberUtil;
//import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.*;
//import com.sgs.customerbiz.dbstorages.mybatis.model.*;
//import com.sgs.customerbiz.domain.domainobject.*;
//import com.sgs.customerbiz.infrastructure.api.IdService;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
///**
// *
// */
//@Service
//public class ProductService {
//
//    @Autowired
//    private IdService idService;
//
//    @Autowired
//    private TrfProductSampleMapper productSampleMapper;
//
//    @Autowired
//    private TrfServiceRequirementService trfServiceRequirementService;
//
//    @Autowired
//    private TrfTestItemService trfTestItemService;
//
//    @Autowired
//    private TrfCareLabelService trfCareLabelService;
//
//    @Autowired
//    private TrfFileService trfFileService;
//
//
//    public void saveProductList(Long trfId, List<TrfProductDO> productList, List<TrfSampleDO> sampleList) {
//
//        // 删除 tb_trf_product_sample
//        TrfProductSampleExample productSampleExample = new TrfProductSampleExample();
//        productSampleExample.createCriteria().andTrfIdEqualTo(trfId);
//        productSampleMapper.deleteByExample(productSampleExample);
//
//        List<TrfProductSamplePO> productSamplePOList = buildProductSampleInfo(trfId, productList, sampleList);
//        if (CollectionUtils.isNotEmpty(productSamplePOList)) {
//            productSampleMapper.batchInsert(productSamplePOList);
//        }
//    }
//
//
//    /**
//     * @param trfId
//     * @return
//     */
//    public List<TrfProductSamplePO> getTrfProductSamplePOS(Long trfId) {
//        TrfProductSampleExample productSampleExample = new TrfProductSampleExample();
//        productSampleExample.createCriteria().andTrfIdEqualTo(trfId);
//        return productSampleMapper.selectByExample(productSampleExample);
//    }
//
//
//    private List<TrfProductSamplePO> buildProductSampleInfo(Long trfId, List<TrfProductDO> productList, List<TrfSampleDO> sampleList) {
//        List<TrfProductSamplePO> productSamplePOList = new ArrayList<>();
//        if (CollectionUtils.isNotEmpty(productList)) {
//            productList.forEach(
//                    l -> {
//                        TrfProductSamplePO productSamplePO = new TrfProductSamplePO();
//                        BeanUtils.copyProperties(l, productSamplePO);
//                        // TODO 静态常量赋值
//                        productSamplePO.setParentProductSampleId(0L);
//                        productSamplePO.setTrfId(trfId);
//                        long productSampleId = idService.nextId();
//                        productSamplePO.setId(productSampleId);
//                        productSamplePO.setCreatedTime(DateUtils.getNow());
//                        productSamplePO.setModifiedTime(DateUtils.getNow());
//                        productSamplePOList.add(productSamplePO);
//                    }
//            );
//        }
//
//        if (CollectionUtils.isNotEmpty(sampleList)) {
//            sampleList.forEach(
//                    l -> {
//                        TrfProductSamplePO productSamplePO = new TrfProductSamplePO();
//                        BeanUtils.copyProperties(l, productSamplePO);
//                        productSamplePO.setTrfId(trfId);
//                        productSamplePO.setCreatedTime(DateUtils.getNow());
//                        productSamplePO.setModifiedTime(DateUtils.getNow());
//                        productSamplePOList.add(productSamplePO);
//                    }
//            );
//        }
//
//        List<TrfProductSamplePO> productSamplePOS = productSamplePOList
//                .stream()
//                .filter(l -> NumberUtil.equals(l.getParentProductSampleId(), 0L))
//                .collect(Collectors.toList());
//
//        Map<Integer, Long> productMap = new HashMap<>();
//        for (TrfProductSamplePO productSamplePO : productSamplePOS) {
//            productMap.put(productSamplePO.getLanguageId(), productSamplePO.getId());
//        }
//
//        Map<Integer, List<TrfProductSamplePO>> sampleCollect = productSamplePOList
//                .stream()
//                .filter(l -> !NumberUtil.equals(l.getParentProductSampleId(), 0L))
//                .collect(Collectors.groupingBy(TrfProductSamplePO::getLanguageId));
//
//        Set<Integer> sampleKeyList = sampleCollect.keySet();
//
//        List<TrfProductSamplePO> productSamplePOAllList = new ArrayList<>(productSamplePOS);
//
//        for (Integer sampleKey : sampleKeyList) {
//            List<TrfProductSamplePO> sampleFireList = sampleCollect.get(sampleKey);
//            sampleFireList.forEach(
//                    v -> {
//                        v.setParentProductSampleId(productMap.get(sampleKey));
//                        productSamplePOAllList.add(v);
//                    }
//            );
//        }
//        return productSamplePOAllList;
//    }
//}
