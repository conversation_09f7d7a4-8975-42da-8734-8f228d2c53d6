package com.sgs.customerbiz.domain.strategy;

import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.domain.dto.TrfContext;
import com.sgs.customerbiz.domain.dto.TrfStatusCalculationRequest;
import com.sgs.customerbiz.domain.dto.statemachine.StateMachineControl;
import com.sgs.customerbiz.domain.enums.TrfActionEnum;
import com.sgs.customerbiz.domain.strategy.statemachine.TrfStateMachineService;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

@Slf4j
public abstract class AbstractTrfStatusStrategy implements TrfStatusStrategy {

    /**
     * 状态错误码常量
     */
    private static final String STATUS_ERROR_MSG = "TRF %s status transition from %s to %s is invalid";
    private static final String PARAM_ERROR_MSG = "Target status or current status cannot be null";
    private static final String SYSTEM_ERROR_MSG = "Status transition validation failed for TRF %s: %s";

    @Autowired
    private TrfStateMachineService stateMachineService;

    /**
     * 创建业务异常
     */
    private CustomerBizException createBizException(ErrorCategoryEnum category, 
                                                  ErrorTypeEnum errorType, 
                                                  String errorMsg) {
        return new CustomerBizException(
            ErrorCodeFactory.createNewErrorCode(
                category,
                ErrorBizModelEnum.TRFDOMAIN,
                ErrorFunctionTypeEnum.STATUSCONTROL,
                errorType
            ),
            ResponseCode.INTERNAL_SERVER_ERROR.getCode(),
            errorMsg
        );
    }


    /**
     * 校验状态跳转是否合法
     * 仅当allowJump=false时进行严格校验,其他情况(null/true)均允许跳转
     * 特殊处理:
     * 1. REVISE状态可以从任意状态跳转
     * 2. 允许从REVISE状态跳转到其他状态
     * 
     * @throws CustomerBizException 当状态转换不合法时抛出异常
     */
    protected Integer validateStatusTransition(Integer targetStatus, Integer currentStatus, TrfStatusCalculationRequest request) {
        // 参数校验
        if (targetStatus == null || currentStatus == null) {
            log.error(PARAM_ERROR_MSG);
            throw createBizException(
                ErrorCategoryEnum.PARAMETER_ERROR,
                ErrorTypeEnum.PARAMETERERROR,
                PARAM_ERROR_MSG
            );
        }
        String trfNo = request.getTrfNo();
        try {
            StateMachineControl stateMachineControl = request.getStateMachineControl();
            // 如果目标状态与当前状态相同，直接返回
            if (Objects.equals(targetStatus, currentStatus) || stateMachineControl == null) {
                log.debug("Target status equals current status or stateMachineControl is null, no transition needed");
                return targetStatus;
            }
            
            // 构建状态转换上下文
            TrfContext context = new TrfContext();
            context.setFromStatus(currentStatus);
            context.setToStatus(targetStatus);
            context.setTrfNo(trfNo);
            context.setSystemId(request.getSystemId());
            // 设置当前上下文
            TrfContext.setCurrentContext(context);

            // 使用状态机服务验证转换
            if (!stateMachineService.validateTransition(context, stateMachineControl)) {
                String errorMsg = String.format(STATUS_ERROR_MSG, 
                    trfNo,
                    TrfStatusEnum.getTextEn(currentStatus),
                    TrfStatusEnum.getTextEn(targetStatus)
                );
                log.error(errorMsg);
                throw createBizException(
                    ErrorCategoryEnum.BUSINESS_ERROR,
                    ErrorTypeEnum.STATUSERROR,
                    errorMsg
                );
            }

            log.debug("Status transition validated successfully: {} -> {}", 
                TrfStatusEnum.getTextEn(currentStatus), 
                TrfStatusEnum.getTextEn(targetStatus));
            return targetStatus;

        } catch (CustomerBizException e) {
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during status transition validation", e);
            String errorMsg = String.format(SYSTEM_ERROR_MSG, trfNo, e.getMessage());
            throw createBizException(
                ErrorCategoryEnum.SYSTEM_ERROR,
                ErrorTypeEnum.SYSTEMERROR,
                errorMsg
            );
        } finally {
            // 清除上下文
            TrfContext.clearCurrentContext();
        }
    }

    /**
     * 计算状态索引
     */
    protected int calcTrfStatusIndex(Integer[] statusFlow, Integer trfStatus) {
        if (statusFlow == null || statusFlow.length == 0) {
            return -1;
        }
        int idx = -1;
        for (int i = 0; i < statusFlow.length; i++) {
            int status = statusFlow[i];
            if (trfStatus.compareTo(status) == 0) {
                idx = i;
                break;
            }
        }
        return idx;
    }

    /**
     * 获取引用系统
     */
    protected String getRefSystem() {
        // 从当前线程上下文中获取systemId
        return TrfContext.getCurrentContext().getSystemId();
    }

    /**
     * 计算TRF状态的模板方法
     * 定义了状态计算的标准流程:
     * 1. 子类计算初始状态
     * 2. 应用通用状态规则
     * 3. 使用状态机验证状态转换
     */
    @Override
    public Integer calculateTrfStatus(TrfStatusCalculationRequest request) {
        // 1. 子类实现具体的状态计算逻辑
        Integer calculatedStatus = doCalculateTrfStatus(request);

        //2. 应用通用的状态转换规则
        return applyCommonStatusRules(calculatedStatus, request.getCurrentTrfStatus(), request.getAction());
    }

    /**
     * 应用通用的状态转换规则
     * 包含以下规则:
     * 1. Completed之前的状态可以自由变化
     * 2. Completed或Closed状态需要特殊处理
     * 3. Revise状态只能转到Completed
     * 4. 只有Completed和Closed状态可以转到Revise
     */
    private Integer applyCommonStatusRules(Integer targetStatus, Integer currentStatus, String action) {
        // 如果当前状态在Completed之前，允许自由变化
        if (currentStatus < TrfStatusEnum.Completed.getStatus()) {
            return targetStatus;
        }

        // 处理Completed或Closed状态的特殊情况
        if (isCompletedOrClosed(currentStatus)) {
            return handleCompletedOrClosedStatus(targetStatus, currentStatus, action);
        }

        // 限制Revise状态只能转到Completed
        if (isReviseStatus(currentStatus) && !isTargetCompleted(targetStatus)) {
            log.debug("TRF status in Revise can only transition to Completed");
            return currentStatus;
        }

        // 限制只有Completed和Closed状态可以转到Revise
        if (isTargetRevise(targetStatus) && !isCompletedOrClosed(currentStatus)) {
            log.debug("Only Completed or Closed status can transition to Revise");
            return currentStatus;
        }

        return targetStatus;
    }

    /**
     * 处理Completed或Closed状态的特殊情况
     * 当目标状态小于Completed时:
     * - 如果是修订报告动作，允许转到Revise状态
     * - 否则保持当前状态不变
     */
    private Integer handleCompletedOrClosedStatus(Integer targetStatus, Integer currentStatus, String action) {
        if (targetStatus < TrfStatusEnum.Completed.getStatus()) {
            if (Func.equals(TrfActionEnum.SYNC_REVISE_REPORT.getCode(), action)) {
                log.debug("Allow transition to Revise status for revise report action");
                return TrfStatusEnum.Revise.getStatus();
            }
            return currentStatus;
        }
        return targetStatus;
    }

    // 辅助方法，提高代码可读性
    private boolean isCompletedOrClosed(Integer status) {
        return status == TrfStatusEnum.Completed.getStatus() 
            || status == TrfStatusEnum.Closed.getStatus();
    }

    private boolean isReviseStatus(Integer status) {
        return Objects.equals(TrfStatusEnum.Revise.getStatus(), status);
    }

    private boolean isTargetCompleted(Integer status) {
        return Objects.equals(TrfStatusEnum.Completed.getStatus(), status);
    }

    private boolean isTargetRevise(Integer status) {
        return Objects.equals(TrfStatusEnum.Revise.getStatus(), status);
    }

    /**
     * 子类实现具体的状态计算逻辑
     */
    protected abstract Integer doCalculateTrfStatus(TrfStatusCalculationRequest request);
} 