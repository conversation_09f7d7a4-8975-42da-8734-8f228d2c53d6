package com.sgs.customerbiz.domain.strategy;

import com.sgs.customerbiz.domain.enums.TrfStatusRuleEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.sgs.customerbiz.domain.strategy.impl.*;

@Component
public class TrfStatusStrategyFactory {

    private final Map<Integer, TrfStatusStrategy> strategies = new ConcurrentHashMap<>();
    
    @Autowired
    public TrfStatusStrategyFactory(UnlimitedTrfStatusStrategy unlimitedStrategy,
                                  OneOrderTrfStatusStrategy oneOrderStrategy, 
                                  AllOrderTrfStatusStrategy allOrderStrategy,
                                  OnceTrfStatusStrategy onceStrategy,
                                  UpdateTrfStatusStrategy updateStrategy) {
        strategies.put(TrfStatusRuleEnum.UNLIMITED.getRule(), unlimitedStrategy);
        strategies.put(TrfStatusRuleEnum.ONE_ORDER.getRule(), oneOrderStrategy);
        strategies.put(TrfStatusRuleEnum.ALL_ORDER.getRule(), allOrderStrategy);
        strategies.put(TrfStatusRuleEnum.ONCE.getRule(), onceStrategy);
        strategies.put(TrfStatusRuleEnum.UPDATE.getRule(), updateStrategy);
    }

    public TrfStatusStrategy getStrategy(Integer statusRule) {
        TrfStatusStrategy strategy = strategies.get(statusRule);
        if (strategy == null) {
            throw new UnsupportedOperationException();
        }
        return strategy;
    }
} 