package com.sgs.customerbiz.domain.service.strategy;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sgs.customerbiz.core.common.KafkaProducer;
import com.sgs.customerbiz.core.util.DateUtils;
import com.sgs.customerbiz.core.util.JsonNodeUtils;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.BoundTrfRelExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmodel.info.callbackinfo.TRFCommonCallbackInfo;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfLogMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.*;
import com.sgs.customerbiz.domain.domainobject.TrfStrategyServiceExtObjDO;
import com.sgs.customerbiz.facade.model.dto.BoundTrfRelDTO;
import com.sgs.customerbiz.facade.model.dto.TRFSendMsgDTO;
import com.sgs.customerbiz.facade.model.trf.UpdateTrfStatusInfo;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.constant.KafkaTopicConsts;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.model.enums.ProductLineType;
import com.sgs.framework.model.enums.ReasonTypeEnum;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.enums.TrfStatusEnum;
import com.sgs.framework.model.trf.TrfStatusInfo;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.ProductLineContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName TRFAbstractBaseInterface
 * @Description 策略抽象类
 * <AUTHOR>
 * @Date 2023/2/8
 */
@Component
@Slf4j
public abstract class TRFAbstractBaseInterface<TOutput extends TRFCommonCallbackInfo> {

    @Autowired
    private BoundTrfRelExtMapper boundTrfRelExtMapper;
    @Autowired
    private TrfInfoExtMapper trfInfoExtMapper;
    @Autowired
    private KafkaProducer kafkaProducer;

    @Autowired
    private TrfInfoMapper trfInfoMapper;

    @Autowired
    private TrfLogMapper trfLogMapper;

    /**
     * 处理数据
     * @param trfStatusInfo
     * @param extObj
     * @return
     */
    public abstract CustomResult<TOutput> invoke(TrfStatusInfo trfStatusInfo, TrfStrategyServiceExtObjDO extObj);

    /**
     * 数据入库操作
     * @param output
     * @return
     */
    public abstract CustomResult doDeal(TOutput output);

    /**
     * 判断当前是否是SL的BU
     * @return
     */
    public Boolean productLineCodeIsSL(){
        String productLineCode = ProductLineContextHolder.getProductLineCode();
        if(StringUtils.isBlank(productLineCode)){
            return false;
        }
        ProductLineType productLineAbbr = ProductLineType.findProductLineAbbr(productLineCode);
        return ProductLineType.check(productLineAbbr.getProductLineId(),ProductLineType.SL);
    }


    /**
     * db操作
     * @return
     */
    public CustomResult doDealForCallback(TRFCommonCallbackInfo callbackInfo){

        log.info("logId:{} doDealForCallback 开始执行DB操作",callbackInfo.getRequestId());
        List<TrfOrderPO> updatePOList = callbackInfo.getUpdatePOList();
        List<TrfOrderPO> insertPOList = callbackInfo.getInsertPOList();
        UpdateTrfStatusInfo updateTrfStatusInfo = callbackInfo.getUpdateTrfStatusInfo();

        int i = 0;

        if(!CollectionUtils.isEmpty(insertPOList)){
            i = i | boundTrfRelExtMapper.batchInsert(insertPOList);
        }
        if(!CollectionUtils.isEmpty(updatePOList)){
            i = i | boundTrfRelExtMapper.updateOrderByTrfNoAndOrderId(updatePOList);
        }
        if(updateTrfStatusInfo != null){
            i = i | trfInfoExtMapper.batchUpdateTrfStatus(updateTrfStatusInfo);

            // 此处加上更新tb_trf表的操作
            trfInfoExtMapper.batchUpdateNewTrfStatus(updateTrfStatusInfo);
        }
        log.info("logId:{} doDealForCallback 执行DB操作结束 i={}",callbackInfo.getRequestId(),i);
        Object bodyContent = callbackInfo.getBodyContent();
        if(bodyContent==null){
            log.info("logId:{} doDealForCallback send MQ to other system 无bodyContent, 不发送",callbackInfo.getRequestId());
            return CustomResult.newSuccessInstance();
        }

        CustomResult result = new CustomResult<>();
        //如果当前是sgsmart，发送给ilayer的数据需要这边进行封装
        if(RefSystemIdEnum.check(callbackInfo.getRefSystemId(),RefSystemIdEnum.SGSMart)){
            log.info("logId:{} doDealForCallback send MQ to SGSMart system ",callbackInfo.getRequestId());
            this.doCreateBodyAndSend(callbackInfo);
            result.setSuccess(i>-1);
            return result;
        }


        //发送mq 到 DataEntry Tool
        kafkaProducer.doSend(KafkaTopicConsts.PREORDER_TO_FORILAYER_DATAENTRYTOOLS,callbackInfo.getMqKey(),callbackInfo.getBodyContent() );
        log.info("logId:{} doDealForCallback send MQ to other system 结束",callbackInfo.getRequestId());

        result.setSuccess(i>-1);
        return result;
    }

    private void doCreateBodyAndSend(TRFCommonCallbackInfo callbackInfo) {
        //需要将sgsmart 对象中的trf结构的status 替换为目前需要更新的trfStatus
        Object bodyContent = callbackInfo.getBodyContent();
        log.info("logId:{} doDealForCallback send MQ to SGSMart system bodyContent:{}",callbackInfo.getRequestId(),bodyContent);
        if(ObjectUtils.isEmpty(bodyContent)){
            return;
        }
        UpdateTrfStatusInfo updateTrfStatusInfo = callbackInfo.getUpdateTrfStatusInfo();
        Integer trfStatus = updateTrfStatusInfo==null ? null : updateTrfStatusInfo.getTrfStatus();
        TRFSendMsgDTO dto = callbackInfo.getDto();

        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(bodyContent));

        //替换trf下面的status
        if(TrfStatusEnum.findStatus(trfStatus)!=null){

            log.info("logId:{} doDealForCallback send MQ to SGSMart replace trfStatus:{}",callbackInfo.getRequestId(),trfStatus);

            JSONArray array = jsonObject.getJSONArray("trfList");
            JSONObject targetJson = new JSONObject();
            for (int i = 0; i < array.size(); i++) {
                targetJson.put("trfList["+i+"].trf.status",trfStatus);
            }
            jsonObject = JsonNodeUtils.replaceJSONValue(jsonObject,targetJson);
        }
        dto.setBody(jsonObject);

        kafkaProducer.doSend(KafkaTopicConsts.PREORDER_TO_FORILAYER_DATAENTRYTOOLS,callbackInfo.getMqKey(),dto );
        log.info("logId:{} doDealForCallback send SGSMart MQ to other system 结束",callbackInfo.getRequestId());
    }

    /**
     * 入参check
     * @param trfStatusInfo
     * @param extObj
     * @return
     */
    public CustomResult checkParameter(TrfStatusInfo trfStatusInfo, TrfStrategyServiceExtObjDO extObj) {
        CustomResult<Object> result = CustomResult.newSuccessInstance();

        String orderNo = trfStatusInfo.getOrderNo();
        Set<String> trfNos = trfStatusInfo.getTrfNos();
        String orderId = trfStatusInfo.getOrderId();
        Integer refSystemId = trfStatusInfo.getRefSystemId();


        String logID = extObj.getLogID();
        //用来check 是否有orderId，
        boolean checkOrderId = extObj.isCheckOrderId();
        boolean checkEnquiryToOrderStatus = extObj.isCheckToOrderStatus();

        log.info("logID:{}-Check req:入参 orderNO:{},trfNos:{},orderId:{},refSystemID:{}",logID,orderNo,StringUtils.join(trfNos,","),orderId,refSystemId);

        if(refSystemId == null || RefSystemIdEnum.getRefSystemId(refSystemId)==null){
            log.info("logID:{}-参数refSystemId为null或者无效",logID);
            return result.fail("Please check parameter refSystemId , your refSystemId is invalid");
        }

        if(CollectionUtils.isEmpty(trfNos) || StringUtils.isBlank(orderNo)){
            log.info("logID:{}-参数 orderNo/trfNos 存在空",logID);
            return result.fail("Please check parameter orderNo/trfNos , can't be null");
        }

        if(checkOrderId){
            if(StringUtils.isBlank(orderId)){
                log.info("logID:{}-参数orderId 存在空",logID);
                return result.fail("Please check parameter orderId , can't be null");
            }
        }
        //只check 普通参数
        if(extObj.isCheckCommonParam()){
            log.info("logID:{}-参数校验通过",logID);
            return result;
        }

        if(!checkEnquiryToOrderStatus){
            return result;
        }
        //校验当前enquiry是否绑定过其他trf 或者trf 绑定过其他order, boundStatus=1
        List<BoundTrfRelDTO> boundTrfRelDTOS = boundTrfRelExtMapper.queryTRFBoundInfoByTrfNos(Lists.newArrayList(trfNos));

        //这个flag 是 trf enquiry to order 过来的，只需要check 数据库是否存在数据即可，如果不存在，说明没有进行过enquiry推送，不能进行orderNo修改
        log.info("logID:{}-trfNos:{}验boundStatus=1 的数据 是否可以进行orderNo变更",logID,trfNos);
        if(CollectionUtils.isEmpty(boundTrfRelDTOS) && checkEnquiryToOrderStatus){
            log.info("logID:{}-trf:{}没有绑定过任何单号，参数校验 不通过",logID,trfNos);
            return result.fail(String.format("TRFNo:[%s] not bound any orderNo before your request",Lists.newArrayList(trfNos)));
        }
        //校验是否有当前orderId的数据（不排除一个TRF有多个Order的情况）
        List<BoundTrfRelDTO> hasReqOrderIdData = boundTrfRelDTOS.stream().filter(b -> StringUtils.equalsIgnoreCase(orderId, b.getOrderId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(hasReqOrderIdData)){
            log.info("logID:{}-trf:{}-orderId:{}没有绑定过任何单号，参数校验 不通过",logID,trfNos,orderId);
            return result.fail(String.format("TRFNo:[%s] OrderID:[%s] not bound any orderNo before your request",Lists.newArrayList(trfNos),orderId));
        }

        //校验当前能否进行enquriy to order 的变更，主要校验当前trf 是否已经绑定了其他order
        if(CollectionUtils.isEmpty(boundTrfRelDTOS)){
            log.info("logID:{}-trf:{}-orderId:{}没有绑定过任何单号，参数校验 不通过",logID,trfNos,orderId);
            return result.fail(String.format("TRFNo:[%s] OrderID:[%s] not bound any orderNo before your request",Lists.newArrayList(trfNos),orderId));
        }
        return result;
    }


    public void batchUpdateTrfAndSaveOpLog(List<CustomerTrfInfoPO> trfTodoInfoPOS,
                                           TrfStatusInfo trfStatusInfo,
                                           TrfStrategyServiceExtObjDO extObj) {
        for (CustomerTrfInfoPO customerTrfInfoPO :trfTodoInfoPOS) {
            TrfInfoExample trfInfoExample = new TrfInfoExample();
            trfInfoExample.createCriteria().andTrfNoEqualTo(customerTrfInfoPO.getTrfNo()).andRefSystemIdEqualTo(customerTrfInfoPO.getRefSystemId());
            List<TrfInfoPO> trfInfoPOList = trfInfoMapper.selectByExample(trfInfoExample);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(trfInfoPOList)) {
                TrfInfoPO trfInfoPO = CollUtil.get(trfInfoPOList, 0);

                TrfLogPO logPO = new TrfLogPO();
                logPO.setId(IdUtil.snowflakeId());
                logPO.setRefSystemId(trfInfoPO.getRefSystemId());
                logPO.setTrfNo(trfInfoPO.getTrfNo());
                logPO.setFromStatus(trfInfoPO.getStatus());
                logPO.setToStatus(TrfStatusEnum.ToBeBound.getStatus());
                logPO.setChangeType(1);
                logPO.setChangeSubtype(toChangeSubtype(trfStatusInfo.getRemarkReason()));
                logPO.setChangeRemark(trfStatusInfo.getRemark());
                logPO.setPendingFlag(trfInfoPO.getPendingFlag());
                logPO.setCreatedBy(extObj.getUserName());
                logPO.setCreatedDate(DateUtil.now());
                logPO.setModifiedBy(extObj.getUserName());
                logPO.setModifiedDate(DateUtil.now());
                trfLogMapper.insert(logPO);

                trfInfoPO.setStatus(TrfStatusEnum.ToBeBound.getStatus());
                trfInfoPO.setModifiedBy(extObj.getUserName());
                trfInfoPO.setModifiedDate(DateUtils.getNow());
                trfInfoMapper.updateByPrimaryKey(trfInfoPO);
            }
        }
    }

    private Integer toChangeSubtype(String reasonTypeText) {
        for (ReasonTypeEnum reasonTypeEnum : ReasonTypeEnum.values()) {
            if (Objects.equals(reasonTypeEnum.getText(), reasonTypeText)) {
                return reasonTypeEnum.getType();
            }
        }
        return ReasonTypeEnum.ClientRequired.getType();
    }

}
