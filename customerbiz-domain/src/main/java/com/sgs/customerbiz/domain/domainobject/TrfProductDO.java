
package com.sgs.customerbiz.domain.domainobject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Auto-generated: 2023-03-20 13:59:35
 *
 * <AUTHOR>
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrfProductDO implements Serializable {
    private String productSampleId;

    private String buyerOrgannization1;
    private String buyerOrgannization2;
    private String buyerOrgannization3;
    private String buyerOrgannizationCode1;
    private String buyerOrgannizationCode2;
    private String buyerOrgannizationCode3;
    private String buyerAliase;
    private String buyerSourcingOffice;
    private String countryOfOrigin;
    private String countryOfDestination;
    private String templateId;
    private String supplier;
    private String supplierNo;
    private String factoryId;
    private String factoryName;
    private String firstFPUNo;
    private String firstPassFPUNo;
    private String firstTimeApplicationFlag;
    private String fPUNo;
    private String fPUReportNo;
    private String gPUNo;
    private String lotNo;
    //    private String noOfSample;
    private String otherSampleInformation;
    private String peformanceCode;
    private String pONo;
    private String previousReportNo;
    private String trimReportNo;
    private String fabricReport;
    private String productCategory1;
    private String productCategory2;
    private String styleNo;
    private String refCode1;
    private String refCode2;
    private String refCode3;
    private String refCode4;
    private String refCode5;
    private String refCode6;
    private String refCode7;
    private String refCode8;
    private String refCode9;
    private String refCode10;
    private String productColor;
    private String productDescription;
    private String productionStage;
    private String sampleReceivedDate;
    private String ageGroup;
    private String endUse1;
    private String specialCustomerAttribute1;
    private String specialCustomerAttribute2;
    private String specialCustomerAttribute3;
    private String specialCustomerAttribute4;
    private String specialCustomerAttribute5;
    private String specialCustomerAttribute6;
    private String specialCustomerAttribute7;
    private String specialCustomerAttribute8;
    private String specialCustomerAttribute9;
    private String specialCustomerAttribute10;
    private String specialCustomerAttribute11;
    private String specialCustomerAttribute12;
    private String specialCustomerAttribute13;
    private String specialCustomerAttribute14;
    private String specialCustomerAttribute15;
    private String specialCustomerAttribute16;
    private String specialCustomerAttribute17;
    private String specialCustomerAttribute18;
    private String specialProductAttribute1;
    private String specialProductAttribute2;
    private String specialProductAttribute3;
    private String specialProductAttribute4;
    private String specialProductAttribute5;
    private String specialProductAttribute6;
    private String specialProductAttribute7;
    private String specialProductAttribute8;
    private String specialProductAttribute9;
    private String specialProductAttribute10;
    private String construction;
    private String yarnCount;
    private String threadCount;
    private String fiberComposition;
    private String fiberWeight;
    private String fabricWidth;
    private String season;
    private String size;
    private String specialFinishing;
    private String collection;
    private String careLabelFlag;
    private String careLabel;
    private String careLabelWording;
    private String productType;
    private String productItemNo;
    private String cancelFlag;
    private String itemNo;
    private Integer languageId;
    private String vendorNo;
//    private String productInstanceId;

}