package com.sgs.customerbiz.domain.enums;

import javax.annotation.Nullable;

/**
 * @author: shawn.yang
 * @create: 2023-07-27 10:45
 */
public enum TrfStatusModifyMode {

    UNLIMITED(0,"不限制"),
    ALL_STATUS_SYNC(1,"所有状态同步才能变更");

    private final Integer code;
    private final String desc;

    TrfStatusModifyMode(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    @Nullable
    public static TrfStatusModifyMode fromCode(Integer code){
        for (TrfStatusModifyMode value : values()) {
            if (value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }
}
