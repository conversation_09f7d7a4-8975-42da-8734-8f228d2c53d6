package com.sgs.customerbiz.domain.domainobject.v2;

import com.sgs.customerbiz.model.trf.dto.TrfCitationDTO;
import com.sgs.customerbiz.model.trf.dto.TrfPpTestLineLangDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TrfPpTestLineDOV2 implements Serializable {

    private String ppTlRelId;

    private Long ppArtifactRelId;

    private Long ppBaseId;

    private Long rootPPBaseId;

    private Integer ppNo;

    private Integer ppVersionId;

    private String ppName;

    private String ppNotes;

    private Integer sectionId;

    private Integer sectionLevel;

    private String sectionName;

    private Long aid;

    private List<TrfPpTestLineLangDOV2> languageList;

    private TrfCitationDOV2 citation;

    /**
     * @since SCI-1276 Target Inspectorio
     */
    private String testCategoryCode;
}
