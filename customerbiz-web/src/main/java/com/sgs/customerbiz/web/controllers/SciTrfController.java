package com.sgs.customerbiz.web.controllers;

import com.sgs.customerbiz.biz.service.preview.TestLineRow;
import com.sgs.customerbiz.facade.ITrfFacade;
import com.sgs.customerbiz.facade.model.trf.req.BoundTrfInfoSearchReq;
import com.sgs.customerbiz.facade.model.trf.req.CustomerTrfInfoSearchReq;
import com.sgs.customerbiz.model.trf.dto.TrfQuotationDTO;
import com.sgs.customerbiz.model.trf.dto.importtrfresp.TrfImportResult;
import com.sgs.customerbiz.model.trf.dto.req.*;
import com.sgs.customerbiz.model.trf.dto.resp.TestLinePreviewDTO;
import com.sgs.customerbiz.web.controllers.v1.TrfV1DTO;
import com.sgs.customerbiz.web.controllers.v1.convertor.TrfV1Convertor;
import com.sgs.customerbiz.web.controllers.v1.req.OrderToTrfV1Req;
import com.sgs.customerbiz.web.controllers.v1.req.TrfSyncV1Req;
import com.sgs.framework.core.base.BaseResponse;
import io.protostuff.Request;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Api(tags = "Trf")
public class SciTrfController {
    @Autowired
    private ITrfFacade trfFacade;

    /**
     * @return
     */
    @Deprecated
    @PostMapping("/v1/trf/importTrf")
    @ApiOperation(value = "import Trf")
    public BaseResponse importTrf(@RequestBody TrfImportReq importReq) {
        BaseResponse<TrfImportResult> importTrf = trfFacade.importTrf(importReq);
        if (importTrf.isSuccess()) {
//            TrfDTO data = importTrf.getData();
            TrfImportResult importTrfData = importTrf.getData();
            TrfV1DTO trfV1DTO = TrfV1Convertor.convertorImportTrf(importTrfData);
            return BaseResponse.newInstance(trfV1DTO);
        } else {
            return importTrf;
        }
    }

    /**
     * @return
     */
    @Deprecated
    @PostMapping("/v1/trf/orderToTrf")
    @ApiOperation(value = "order to trf")
    public BaseResponse orderToTrf(@RequestBody OrderToTrfV1Req orderToTrfReq) {
        return trfFacade.orderToTrf(TrfV1Convertor.convertorOrderToTrf(orderToTrfReq));
    }

    /**
     * @return
     */
    @Deprecated
    @PostMapping("/v1/trf/syncTrf")
    @ApiOperation(value = "Sync trf information")
    public BaseResponse syncTrf(@RequestBody TrfSyncV1Req syncReq) {
        return trfFacade.syncTrf(TrfV1Convertor.convertorSyncTrf(syncReq));
    }


    /**
     * @return
     */
    @PostMapping("/v2/trf/importTrf")
    @ApiOperation(value = "import Trf")
    public BaseResponse importTrf2(@RequestBody TrfImportReq importReq) {
        return trfFacade.importTrf(importReq);
    }

    /**
     * @return
     */
    @PostMapping("/v2/trf/orderToTrf")
    @ApiOperation(value = "order to trf")
    public BaseResponse orderToTrf2(@RequestBody OrderToTrfReq orderToTrfReq) {
        return trfFacade.orderToTrf(orderToTrfReq);
    }

    @PostMapping("/v2/trf/syncQuotationToTrf")
    @ApiOperation(value = "sync Quotation To Trf")
    public BaseResponse syncQuotationToTrf(@RequestBody TrfSyncQuotationReq trfSyncQuotationReq) {
        return trfFacade.syncQuotationToTrf(trfSyncQuotationReq);
    }


    /**
     * 解绑Trf
     *
     * @param unbindReq
     * @return
     */
    @PostMapping("/v2/trf/unbindTrf")
    @ApiOperation(value = "Unbind Trf")
    public BaseResponse unbindTrf(@RequestBody TrfUnbindReq unbindReq) {
        return trfFacade.unbindTrf(unbindReq);
    }

    /**
     * bindTrf
     *
     * @param trfBindReq
     * @return
     */
    @Deprecated
    @PostMapping("/v2/trf/bindTrf")
    @ApiOperation(value = "bind Trf")
    public BaseResponse bindTrf(@RequestBody TrfBindReq trfBindReq) {
        return trfFacade.bindTrf(trfBindReq);
    }

    @PostMapping("/v2/trf/getCustomerInfo")
    public BaseResponse getCustomerInfo(@RequestBody CustomerTrfReq req) {
        return trfFacade.getCustomerInfo(req);
    }

    @Deprecated
    @PostMapping("/v2/trf/exportSgsTrfByTrfNo")
    public BaseResponse exportSgsTrfByTrfNo(@RequestBody TrfImportReq trfImportReq) {
        return trfFacade.exportSgsTrfByTrfNo(trfImportReq);
    }

    @PostMapping("/v2/trf/getSgsTrfByTrfNo")
    public BaseResponse getSgsTrfByTrfNo(@RequestBody TrfImportReq trfImportReq) {
        return trfFacade.getSgsTrfByTrfNo(trfImportReq.getTrfNo());
    }

    /**
     * Check if a revised operation can be executed for the given SyncRequest
     *
     * @param syncReq The request
     * @return BaseResponse with Boolean indicating if the revised operation can be executed
     */
    @PostMapping("/v2/trf/canReviseReport")
    @ApiOperation(value = "Check if a revised operation can be executed")
    public BaseResponse<Boolean> canReviseReport(@RequestBody TrfSyncReq syncReq) {
        return trfFacade.canReviseReport(syncReq);
    }

    /**
     * @return
     */
    @PostMapping("/v2/trf/syncTrf")
    @ApiOperation(value = "Sync trf information v2")
    public BaseResponse syncTrf2(@RequestBody TrfSyncReq syncReq) {
        return trfFacade.syncTrf(syncReq);
    }

    /**
     *
     * @param syncReq
     * @return
     */
    @Deprecated
    @PostMapping("/v2/trf/syncReportToTrf")
    @ApiOperation(value = "Sync report to trf")
     public BaseResponse syncReportToTrf(@RequestBody TrfSyncReq syncReq) {
     return trfFacade.syncReportToTrf(syncReq);
     }

     /**
     * get Trf Info List
     *
     * @param reqObject
     * @return
     */
    @Deprecated
    @PostMapping("/trf/getBoundTrfInfoList")
    @ApiOperation(value = "get bound Trf Info List")
    public BaseResponse getBoundTrfInfoList(@RequestBody BoundTrfInfoSearchReq reqObject) {
        return trfFacade.getBoundTrfInfoList(reqObject);
    }

    /**
     * get Trf Info List
     *
     * @param reqObject
     * @return
     */
    @Deprecated
    @PostMapping("/trf/getCustomerTrfInfoList")
    @ApiOperation(value = "get customer Trf Info List")
    public BaseResponse getCustomerTrfInfoList(@RequestBody CustomerTrfInfoSearchReq reqObject) {
        return trfFacade.getCustomerTrfInfoList(reqObject.getTrfNos(), reqObject.getPackageBarcodes());
    }

    /**
     * executeSystem cancel
     *
     * @param reqObject
     * @return
     */
    @Deprecated
    @PostMapping("/v1/trf/cancelTrf")
    @ApiOperation(value = "executeSystem cancel trf")
    public BaseResponse cancelTrf(@RequestBody TrfCancelReq reqObject) {
        return trfFacade.cancelByPreOrder(reqObject);
    }

    /**
     * executeSystem cancel
     *
     * @param reqObject
     * @return
     */
    @Deprecated
    @PostMapping("/v1/trf/returnTrf")
    @ApiOperation(value = "executeSystem return trf")
    public BaseResponse returnTrf(@RequestBody TrfRemoveReq reqObject) {
        return trfFacade.returnByPreOrder(reqObject);
    }

    /**
     * preview sync report
     *
     * @param syncReq 准备回传的数据
     * @return 回传数据预览
     */
    @PostMapping("/v2/trf/previewSyncReport")
    @ApiOperation(value = "preview sync report")
    public BaseResponse<List<TestLinePreviewDTO>> previewSyncReport(@RequestBody TrfSyncReq syncReq) {
        return trfFacade.previewSyncReport(syncReq);
    }

}
