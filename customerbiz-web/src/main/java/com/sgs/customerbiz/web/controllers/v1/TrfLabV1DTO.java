
package com.sgs.customerbiz.web.controllers.v1;

import com.sgs.customerbiz.model.trf.dto.validgroups.TrfCreateGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * Auto-generated: 2023-03-20 13:59:35
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrfLabV1DTO implements Serializable {

    @NotNull(message = "labId不能为空！", groups = {TrfLabV1DTO.class, TrfCreateGroup.class})
    private Long labId;

    @NotBlank(message = "labCode不能为空！", groups = {TrfLabV1DTO.class, TrfCreateGroup.class})
    private String labCode;
    //TODO 新增的字段 20230324
    @NotBlank(message = "otherCode不能为空！", groups = {TrfLabV1DTO.class, TrfCreateGroup.class})
    private String otherCode;
    //TODO 新增的字段 20230324，英文
    @NotBlank(message = "labName不能为空！", groups = {TrfLabV1DTO.class, TrfCreateGroup.class})
    private String labName;
    //TODO 新增的字段 20230324，英文
    @NotBlank(message = "labAddress不能为空！", groups = {TrfLabV1DTO.class, TrfCreateGroup.class})
    private String labAddress;
    //TODO 新增的字段 20230324

    @NotNull(message = "labType不能为空！", groups = {TrfLabV1DTO.class, TrfCreateGroup.class})
    private Integer labType;

    //TODO 新增的字段 20230324
    @NotNull(message = "countryId不能为空！", groups = {TrfLabV1DTO.class, TrfCreateGroup.class})
    private Integer countryId;

    @NotNull(message = "locationId不能为空！", groups = {TrfLabV1DTO.class, TrfCreateGroup.class})
    private Integer locationId;

    //TODO 新增的字段 20230324
    private Integer buId;

    //TODO 删除 20230324
//    @NotNull(message = "productLineId不能为空！", groups = TrfLabDTO.class)
//    private Integer productLineId;

    //TODO  20230324
    // 删除，但db不删除，请从labCode解析，labCode样例 GZ HL，其中HL即productLineCode，中间是1个空格
//    @NotBlank(message = "productLineCode不能为空！", groups = TrfLabDTO.class)
//    private String productLineCode;


    //TODO 新增的字段 20230324
    private List<TrfLabContactV1DTO> contactList;

    //TODO 删除  20230324
//    @NotBlank(message = "locationCode不能为空！", groups = TrfLabDTO.class)
//    private String locationCode;
    private List<TrfLabLanguageV1DTO> languageList;

}