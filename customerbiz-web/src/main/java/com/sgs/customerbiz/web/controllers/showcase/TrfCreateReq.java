package com.sgs.customerbiz.web.controllers.showcase;

import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.dto.validgroups.TrfCompleteGroup;
import com.sgs.customerbiz.model.trf.dto.validgroups.TrfCreateGroup;
import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TrfCreateReq extends BaseRequest {

    @NotNull(message = "TRF HEADER信息不能为空", groups = {TrfCreateGroup.class, TrfCompleteGroup.class})
    private TrfHeaderDTO header;

    @NotNull(message = "Customer信息不能为空", groups = TrfCreateGroup.class)
    private List<@Valid TrfCustomerDTO> customerList;

    @NotNull(message = "Product信息不能为空", groups = TrfProductDTO.class)
    private List<@Valid TrfProductDTO> productList;

    @NotNull(message = "Sample信息不能为空", groups = TrfSampleDTO.class)
    private List<@Valid TrfSampleDTO> sampleList;

    @NotNull(message = "CareLabel信息不能为空", groups = TrfCareLabelDTO.class)
    private List<@Valid TrfCareLabelDTO> careLabelList;

    @NotNull(message = "TestItem信息不能为空", groups = TrfTestItemDTO.class)
    private List<@Valid TrfTestItemDTO> testItemList;

    @NotNull(message = "Requirement信息不能为空", groups = TrfServiceRequirementDTO.class)
    private @Valid TrfServiceRequirementDTO serviceRequirement;

    @NotNull(message = "File信息不能为空", groups = TrfFileDTO.class)
    private List<@Valid TrfFileDTO> attachmentList;
}
