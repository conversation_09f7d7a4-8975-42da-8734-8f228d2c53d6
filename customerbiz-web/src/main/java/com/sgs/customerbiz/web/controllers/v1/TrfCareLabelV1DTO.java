
package com.sgs.customerbiz.web.controllers.v1;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Auto-generated: 2023-03-20 13:59:35
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrfCareLabelV1DTO implements Serializable {
    private String careInstruction;
    private Integer radioType;
    private String selectCountry;
    private Integer careLabelSeq;
    
    private List<TrfCareLabelSampleV1DTO> careLabelSampleList;

}