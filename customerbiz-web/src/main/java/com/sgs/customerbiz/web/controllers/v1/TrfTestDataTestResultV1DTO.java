package com.sgs.customerbiz.web.controllers.v1;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrfTestDataTestResultV1DTO implements Serializable {

    /**
     * 测试结果
     */
    @NotBlank(message = "resultValue不能为空！",groups = TrfTestDataTestResultV1DTO.class)
    private String resultValue;

    /**
     * 测试结果备注
     */
    private String resultValueRemark;

    /**
     * 单位
     */
    @NotBlank(message = "resultUnit不能为空！",groups = TrfTestDataTestResultV1DTO.class)
    private String resultUnit;

    /**
     * 失败标记：0或1
     */
    private String failFlag;
}
