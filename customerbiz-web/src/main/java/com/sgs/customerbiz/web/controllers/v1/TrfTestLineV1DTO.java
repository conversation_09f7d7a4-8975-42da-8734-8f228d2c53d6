/**
  * Copyright 2023 json.cn 
  */
package com.sgs.customerbiz.web.controllers.v1;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Auto-generated: 2023-03-20 13:59:35
 *
 * <AUTHOR>
 * 
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrfTestLineV1DTO implements Serializable {
    private Integer ppNo;
    private Integer ppName;
    private Integer ppVersionId;
    private Long aid;
    private String sectionName;
    private Integer testLineId;
    private Integer testLineVersionId;
    private Integer testLineSeq;
    private Integer citationId;
    private Integer citationType;
    private Integer citationVersionId;
    private Integer citationSectionId;
    private String citationSectionName;
    private String citationName;
    private String testLineInstanceId;
    private String testLineName;
    private String testLineConclusion;
    private String customerReviewConclusion;

    //TODO extenral
}