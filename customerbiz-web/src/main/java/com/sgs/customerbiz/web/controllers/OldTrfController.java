package com.sgs.customerbiz.web.controllers;

import com.sgs.customerbiz.domain.service.TrfStandardService;
import com.sgs.customerbiz.facade.ITrfFacade;
import com.sgs.customerbiz.facade.model.todolist.req.*;
import com.sgs.customerbiz.facade.model.trf.req.CustomerTrfInfoReq;
import com.sgs.framework.core.base.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/trf")
@Api(value = "/trf", tags = "Trf")
public class OldTrfController {
    @Autowired
    private ITrfFacade trfFacade;

    @Autowired
    private TrfStandardService trfStandardService;

    /**
     * Reject Inspectorio Trf
     *
     * @param reqObject
     * @return
     */
    @PostMapping("/inspectorio/reject")
    @ApiOperation(value = "Reject Inspectorio Trf")
    public BaseResponse rejectInspectorio(@RequestBody RejectInspectorio reqObject) {
        return trfFacade.rejectInspectorio(reqObject);
    }

    /**
     * Remove Trf
     *
     * @param reqObject
     * @return
     */
    @PostMapping("/remove")
    @ApiOperation(value = "Remove Trf")
    public BaseResponse remove(@RequestBody RemoveTrfReq reqObject) {
        return trfFacade.remove(reqObject);
    }

    /**
     * Cancel Trf
     *
     * @param reqObject
     * @return
     */
    @PostMapping("/cancel")
    @ApiOperation(value = "Cancel Trf")
    public BaseResponse cancel(@RequestBody CancelTrfReq reqObject) {
        return trfFacade.cancel(reqObject);
    }


    /**
     * @param reqObject
     * @return
     */
    @PostMapping("/unDisplay")
    @ApiOperation(value = "UnDisplay TRF")
    public BaseResponse<?> unDisplay(@RequestBody TrfDisplayReq reqObject) {
        return trfFacade.unDisplay(reqObject);
    }

    /**
     * @param reqObject
     * @return
     */
    @PostMapping("/display")
    @ApiOperation(value = "Display TRF")
    public BaseResponse<?> display(@RequestBody TrfDisplayReq reqObject) {
        return trfFacade.display(reqObject);
    }

    @Deprecated
    @PostMapping("/getTrfInfoList")
    @ApiOperation(value = "get Trf Info List")
    public BaseResponse getTrfInfoList(@RequestBody CustomerTrfInfoReq reqObject) {
        return trfFacade.getTrfInfoList(reqObject);
    }

    /**
     * @param reqObject
     * @return
     */
    @PostMapping("/getRemoveCancelData")
    @ApiOperation(value = "Get Remove/Cancel Data")
    public BaseResponse getRemoveCancelData(@RequestBody RemoveCancelReq reqObject) {
        return trfFacade.getRemoveCancelData(reqObject);
    }

    @Deprecated
    @GetMapping("/getTestDataInfo")
    @ApiOperation(value = "Test Data Info")
    public BaseResponse getTestDataInfo() {
        return BaseResponse.newInstance(trfStandardService.getTestDataInfo());
    }

}
