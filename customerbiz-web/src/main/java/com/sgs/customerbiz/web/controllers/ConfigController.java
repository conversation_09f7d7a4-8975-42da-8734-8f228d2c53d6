package com.sgs.customerbiz.web.controllers;

import com.sgs.config.api.dto.req.ConfigGetReq;
import com.sgs.customerbiz.dfv.domain.DictValueService;
import com.sgs.customerbiz.dfv.domain.domainobject.DictValueDO;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.framework.core.base.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *
 */
@RestController
@RequestMapping("/config")
@Slf4j
@Api(value = "/config", tags = "sci config")
public class ConfigController {

    @Autowired
    private ConfigClient configClient;
    @Autowired
    private DictValueService dictValueService;

    @PostMapping("/queryConfig")
    @ApiOperation(value = "获取cfg配置")
    public BaseResponse importTrf2(@RequestBody ConfigGetReq importReq) {
        String config = configClient.getConfig(importReq);
        return BaseResponse.newInstance(config);
    }


    @PostMapping("/queryDictValue")
    @ApiOperation(value = "获取字典值")
    public BaseResponse queryDictValue(@RequestParam Integer systemId, @RequestParam String dictType) {
        List<DictValueDO> dictValues = dictValueService.selectByDictType(systemId, dictType);
        return BaseResponse.newInstance(dictValues);
    }
}
