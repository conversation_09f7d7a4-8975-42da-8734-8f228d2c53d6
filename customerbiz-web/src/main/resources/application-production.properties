jdbc.datasource.read=get,select,count,list,query,find,search,sum
jdbc.datasource.write=add,create,update,delete,remove,insert

##\u6570\u636E\u5E93\u914D\u7F6E
validationQuery=SELECT 'x'


# SODA PreOrder Master DB
datasource.dynamic.soda.product-lines=SL,HL,MR,AUTO,IND-PL,EE,RSTS,AFL,EEC,CPCH,EMC,ACAD
datasource.dynamic.soda.schema.todolist.master.driver-class-name=com.mysql.jdbc.Driver
datasource.dynamic.soda.schema.todolist.master.url=***********************************************************************************************************************************************************************************************************************************
datasource.dynamic.soda.schema.todolist.master.username=todo_user_prod
datasource.dynamic.soda.schema.todolist.master.password=dJaGz5FEH^av7%riFww2

# SODA PreOrder Slave DB
datasource.dynamic.soda.schema.todolist.slave.driver-class-name=com.mysql.jdbc.Driver
datasource.dynamic.soda.schema.todolist.slave.url=**************************************************************************************************************************************************************************************************************
datasource.dynamic.soda.schema.todolist.slave.username=todo_user_prod
datasource.dynamic.soda.schema.todolist.slave.password=Ahs5_qRt1_yt88Q


#redis config
spring.redis.nodes=10.168.128.239:7000,10.168.128.239:7001,10.168.128.239:7002,10.168.128.237:7003,10.168.128.237:7004,10.168.128.237:7005
# Redis\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD == redis.cluster.password
spring.redis.password=
# \uFFFD\uFFFD\uFFFD\u04F3\uFFFD\u02B1\u02B1\uFFFD\u48E8\uFFFD\uFFFD\uFFFD\uB8E9
spring.redis.timeout=10000
# \u02B9\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u077F\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0163\uFFFD\u04BB\uFFFD\uFFFD\u02BE\uFFFD\uFFFD\uFFFD\uFFFD16\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u077F\uFFFD 0 \uFFFD\uFFFD 15
spring.redis.database=0
# \uFFFD\uFFFD\u023A\u0123\u02BD\uFFFD\u00A3\uFFFD\uFFFD\uFFFD\u023A\uFFFD\uFFFD\uFFFD\u05EA\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
spring.redis.max-redirects=6

# \uFFFD\uFFFD\uFFFD\u04F3\u0635\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u02B9\uFFFD\u00F8\uFFFD\u05B5\uFFFD\uFFFD\u02BE\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u013F\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
spring.redis.pool.max-idle=20
# \uFFFD\uFFFD\u0421\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u02B9\uFFFD\uFFFD\uFFFD\uFFFD\u05B5\uFFFD\uFFFD\uFFFD\uFFFD\u0427\uFFFD\uFFFD
spring.redis.pool.min-idle=5
# \uFFFD\uFFFD\uFFFD\u04F3\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u02B9\uFFFD\u00F8\uFFFD\u05B5\uFFFD\uFFFD\u02BE\u00FB\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u01A3\uFFFD==redis.pool.maxTotal
spring.redis.pool.max-active=20
# \uFFFD\uFFFD\uFFFD\u04F3\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0234\uFFFD\u02B1\uFFFD\u48E8\u02B9\uFFFD\u00F8\uFFFD\u05B5\uFFFD\uFFFD\u02BE\u00FB\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u01A3\uFFFD
spring.redis.pool.max-wait=1000
# redis.pool.testOnBorrow
spring.redis.pool.testOnBorrow=true


# \uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD
tomcat.protocol=org.apache.coyote.http11.Http11Nio2Protocol
# \uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u0738\uFFFD\uFFFD\u00E3\uFFFD\uFFFD\uFFFD\u04AA\uFFFD\uFFFD\u05F0
#tomcat.protocol=org.apache.coyote.http11.Http11AprProtocol
tomcat.connectionTimeout=20000
tomcat.maxConnections=2000
tomcat.maxThreads=700
tomcat.uriEncoding=UTF-8
tomcat.acceptCount=2000
#webEnvironment=false
tomcat.port=8091
# \uFFFD\uFFFDdubbo\u042D\uFFFD\uFFFD\uFFFD\uFFFD20880\uFFFD\u02FF\u06B1\uFFFD\u00B6\uFFFD\uFFFD\uFFFD\uFFFD
dubbo.port=29538
zookeeper.address=**************:2181,**************:2181,**************:2181
# kafka
kafka.bootstrap-servers=**************:9092,**************:9092
kafka.max-request-size=5242880

user.management.url=https://cnapp.sgs.net/UserManagementApi
localiLayer.url=https://cnlocalilayer.sgs.net
notification.url=https://cnapp.sgs.net/NotificationApi
frameWorkApi.url=https://cnapp.sgs.net/FrameWorkApi
customerApi.Url=https://cnapp.sgs.net/CustomerApi
preorder2Api.url=https://cnapp.sgs.net/preorder2api
testdatabiz.url=https://cnapp.sgs.net/testdatabiz
preorderApi.api.url = https://cnapp.sgs.net/OrderApi
base.url=https://cnapp.sgs.net/
digital.report.url=https://cnapp.sgs.net/DigitReport-TS
extsystemApi.url=https://cnapp.sgs.net/extsystemapi/api

# CommonService??dubbo group
commonService.group=prod
temp.alarm.email.switch=true
sgs.email.enabledEmail=true

convert.dff.form.map={"SL-DEFAULT": "f221d295-b762-47c6-b27d-a07c945fdbf7", "SL-HZ-WB-REGULAR": "cf86b8ff-8c58-449a-9d0c-a0654a7520d0"}
convert.dff.grid.map={"SL-DEFAULT": "6eb78ab2-b1a6-43c9-8ad6-7aa11113a545", "SL-HZ-WB-REGULAR": "bfae9ed9-34cc-4e74-9be8-1dd2d203ba29"}

xxl.job.enabled=true
xxl.job.admin.addresses=http://**************:8080/xxl-job-admin
xxl.job.accessToken=default_token
xxl.job.executor.appname=sci-customerbiz-prod
xxl.job.executor.address=
xxl.job.executor.ip=
xxl.job.executor.port=9998
xxl.job.executor.logpath=/usr/local/applogs/customerbiz.iapi.sgs.net/jobhandler
xxl.job.executor.logretentiondays=30

mail.from=<EMAIL>
spring.kafka.producer.max.request.size=
sgs.trace.kafka.servers=************:9092,************:9092
