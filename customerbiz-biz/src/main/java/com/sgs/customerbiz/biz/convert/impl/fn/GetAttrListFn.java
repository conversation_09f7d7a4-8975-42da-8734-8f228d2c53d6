package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
public class GetAttrListFn extends StringOperationFn {

    @Override
    protected Object invoke(Object arg1, Object arg2) {
        if (Func.isEmpty(arg2)) {
            throw new IllegalArgumentException("attr name is empty");
        }
        String attrName = arg2.toString();
        if (Func.isEmpty(arg1)) {
            return null;
        }
        List<Object> result = new ArrayList<>();
        if (arg1 instanceof List) {
            JSONArray sourceList = (JSONArray) arg1;
            for (int i = 0; i < sourceList.size(); i++) {
                JSONObject obj = sourceList.getJSONObject(i);
                if (obj.containsKey(attrName)) {
                    if (Func.isNotEmpty(obj.get(attrName))) {
                        result.add(obj.get(attrName));
                    }
                }
            }
        } else {
            JSONObject sourceList = (JSONObject) arg1;
            if (Func.isNotEmpty(sourceList.get(attrName))) {
                result.add(sourceList.get(attrName));
            }
        }
        return result;
    }

    @Override
    public String getName() {
        return "getAttrList";
    }

    @Override
    public String desc() {
        return "getAttrListFn";
    }
}
