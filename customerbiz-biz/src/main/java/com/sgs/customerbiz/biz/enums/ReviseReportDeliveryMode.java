package com.sgs.customerbiz.biz.enums;

/**
 * 报告回传模式
 * @author: shawn.yang
 * @create: 2023-09-16 10:13
 */
public enum ReviseReportDeliveryMode {
    INCR(1,"增量"),
    FULL(2,"全量");


    private final Integer code;
    private final String desc;

    ReviseReportDeliveryMode(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static ReviseReportDeliveryMode from(Integer code){
        if (code ==null){
            return null;
        }
        for (ReviseReportDeliveryMode value : values()) {
            if (value.getCode().equals(code)){
                return value;
            }
        }
        return null;
    }
}
