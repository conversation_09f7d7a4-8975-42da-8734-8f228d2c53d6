package com.sgs.customerbiz.biz.service.preconvert.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.customerbiz.biz.service.datacollector.CollectedData;
import com.sgs.customerbiz.biz.service.datacollector.StandardDataStructureConstants;
import com.sgs.customerbiz.biz.service.preconvert.DataPreConvertHandler;
import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.domain.domainevent.ObjectEvent;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.integration.FrameWorkClient;
import com.sgs.customerbiz.integration.dto.QueryExchangeRateReq;
import com.sgs.customerbiz.integration.dto.QueryExchangeRateRsp;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfInvoiceDTO;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.CollectionUtil;
import com.sgs.framework.tool.utils.DateUtil;
import com.sgs.framework.tool.utils.Func;
import com.sgs.framework.tool.utils.StringUtil;
import com.sgs.otsnotes.facade.model.common.CustomResult;
import com.sgs.preorder.facade.model.info.LabInfo;
import com.sgs.testdatabiz.facade.model.dto.rd.invoice.RdInvoiceDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.quotation.RdQuotationDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdReportDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdReportMatrixDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdServiceItemDTO;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdTestLineDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Desc
 * <AUTHOR>
 * @date 2023/10/17 23:00
 */
@Slf4j
public abstract class AbstractDataPreConvertHandler implements DataPreConvertHandler {

    @Autowired
    private TrfDomainService trfDomainService;

    @Autowired
    private FrameWorkClient frameWorkClient;

    @Autowired
    private ConfigClient configClient;

    @Override
    public void preHandler(EventSubscribeDTO subscribe, ObjectEvent trfEvent, CollectedData collectedData) {
        TrfInfoPO trfInfoPO = trfDomainService.selectByTrfNo(trfEvent.getRefSystemId(), trfEvent.getTrfNo());
        LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCode(trfInfoPO.getLabCode());

        // Do Nothing
        TrfFullDTO trfFullDTO = (TrfFullDTO) trfEvent.getPayload();

        String config = configClient.getConfig(trfEvent.getProductLineCode(), trfEvent.getRefSystemId(), Constants.CONFIG_INVOICE_CURRENCY);

        String configRateType = configClient.getConfig(trfEvent.getProductLineCode(), trfEvent.getRefSystemId(), Constants.CONFIG_INVOICE_RATE_TYPE);
        ;

        if (Func.isEmpty(config) || Func.isEmpty(configRateType)) {
            return;
        }

        JSONArray quotationListArray = collectedData.getJSONArray(StandardDataStructureConstants.JSON_SECTION_NAME_QUOTATION_LIST);
        List<RdQuotationDTO> quotationDTOList = null;
        if (Func.isEmpty(quotationListArray) && Func.isEmpty(trfFullDTO.getQuotationList())) {
            return;
        }
        if (Func.isEmpty(quotationListArray)) {
            if (Func.isEmpty(trfFullDTO.getQuotationList())) {
                return;
            }
            quotationDTOList = JSONArray.parseArray(JSONObject.toJSONString(trfFullDTO.getQuotationList()), RdQuotationDTO.class);
        } else {
            quotationDTOList = JSONObject.parseArray(quotationListArray.toString(), RdQuotationDTO.class);
        }
        Map<String, List<RdServiceItemDTO>> serviceItemMap = new HashMap<>();
        quotationDTOList.forEach(
                l -> {
                    if (Func.isNotEmpty(l.getServiceItemList())) {
                        serviceItemMap.put(l.getQuotationNo(), l.getServiceItemList());
                    }
                }
        );

        // 获取invoiceList
        List<TrfInvoiceDTO> invoiceList = trfFullDTO.getInvoiceList();

        JSONArray invoiceListArray = collectedData.getJSONArray(StandardDataStructureConstants.JSON_SECTION_NAME_INVOICE_LIST);
        if (Func.isEmpty(invoiceListArray) && Func.isEmpty(invoiceList)) {
            return;
        }
        List<RdInvoiceDTO> invoiceDTOList = new ArrayList<>();
        String currencyReq = null;
        if (Func.isNotEmpty(invoiceListArray)) {
            invoiceDTOList = JSONObject.parseArray(invoiceListArray.toString(), RdInvoiceDTO.class);
            currencyReq = invoiceDTOList.get(0).getCurrency();
        } else {
            currencyReq = invoiceList.get(0).getCurrency();
        }

        List<String> customerIds = configClient.getCustomerNos(trfEvent.getRefSystemId());
        if (Func.isEmpty(customerIds)) {
            return;
        }
        QueryExchangeRateReq queryExchangeRateReq = new QueryExchangeRateReq();
        queryExchangeRateReq.setBuCode(trfEvent.getProductLineCode());
        queryExchangeRateReq.setLocationCode(labInfo.getLocationCode());
        queryExchangeRateReq.setFromCurrencyCode(currencyReq);
        queryExchangeRateReq.setToCurrencyCode(config);
        queryExchangeRateReq.setConversionDate(DateUtil.formatDate(new Date()));
        queryExchangeRateReq.setCustomerNo(customerIds.get(0));

        CustomResult<QueryExchangeRateRsp> queryExchangeRateRspCustomResult = frameWorkClient.queryExchangeRate(queryExchangeRateReq);
        String exchangeRate;
        if (queryExchangeRateRspCustomResult.isSuccess()) {
            QueryExchangeRateRsp data = queryExchangeRateRspCustomResult.getData();
            if (!Objects.equals(data.getRateType(), configRateType) && !Objects.equals(config, currencyReq)) {
                throw new BizException("rateType data error！");
            }
            exchangeRate = data.getExchangeRate();
        } else {
            throw new BizException(queryExchangeRateRspCustomResult.getMsg());
        }

        String finalConfig = config;
        if (Func.isNotEmpty(invoiceDTOList)) {

            invoiceDTOList.forEach(
                    l -> {
                        if (!Objects.equals(finalConfig, l.getCurrency())) {
                            List<String> quotationNos = l.getQuotationNos();
                            if (Func.isNotEmpty(quotationNos)) {
                                List<RdServiceItemDTO> list = new ArrayList<>();
                                quotationNos.forEach(
                                        v -> {
                                            List<RdServiceItemDTO> rdServiceItemDTOS = serviceItemMap.get(v);
                                            if (Func.isNotEmpty(rdServiceItemDTOS)) {
                                                list.addAll(rdServiceItemDTOS);
                                            }
                                        }
                                );
                                l.setTotalAmount(exchangeRate(list, exchangeRate));
                                l.setCurrency(finalConfig);
                            }
                        }
                    }
            );
        }
        if (Func.isNotEmpty(invoiceList)) {

            invoiceList.forEach(
                    l -> {
                        if (!Objects.equals(finalConfig, l.getCurrency())) {
                            List<String> quotationNos = l.getQuotationNoList();
                            if (Func.isNotEmpty(quotationNos)) {
                                List<RdServiceItemDTO> list = new ArrayList<>();
                                quotationNos.forEach(
                                        v -> {
                                            List<RdServiceItemDTO> rdServiceItemDTOS = serviceItemMap.get(v);
                                            if (Func.isNotEmpty(rdServiceItemDTOS)) {
                                                list.addAll(rdServiceItemDTOS);
                                            }
                                        }
                                );
                                l.setTotalAmount(Objects.requireNonNull(exchangeRate(list, exchangeRate)).toString());
                                l.setCurrency(finalConfig);
                            }
                        }
                    }
            );
        }

        trfFullDTO.setInvoiceList(invoiceList);
        trfEvent.setPayload(trfFullDTO);

        collectedData.put(StandardDataStructureConstants.JSON_SECTION_NAME_INVOICE_LIST, invoiceDTOList);

    }

    private BigDecimal exchangeRate(List<RdServiceItemDTO> serviceItemDTOS, String exchangeRate) {
        if (Func.isEmpty(serviceItemDTOS) || Func.isEmpty(exchangeRate)) {
            return null;
        }

        int scale = 2;
        int roundingMode = BigDecimal.ROUND_HALF_UP;

        BigDecimal totalValue = BigDecimal.ZERO;

        for (RdServiceItemDTO item : serviceItemDTOS) {
            Integer quantity = item.getQuantity();
            BigDecimal serviceItemSalesUnitPrice = item.getServiceItemSalesUnitPrice();

            if (Func.isNotEmpty(quantity) && Func.isNotEmpty(serviceItemSalesUnitPrice)) {
                BigDecimal quantityBig = new BigDecimal(quantity);
                BigDecimal decimal = quantityBig.multiply(serviceItemSalesUnitPrice).multiply(new BigDecimal(exchangeRate)).setScale(scale, roundingMode);
                totalValue = totalValue.add(decimal);
            }
        }

        return totalValue;
    }


    /**
     * 根据MatrixId 获取对应的TestLine 对象
     * 注：可能会返回Null
     *
     * @param testMatriId
     * @param collectedData
     * @return
     */
    protected RdTestLineDTO getTestLineByMatrixId(String reportNo, String testMatriId, CollectedData collectedData) {

        if (StringUtil.isBlank(testMatriId)) {
            return null;
        }

        RdReportMatrixDTO reportMatrix = getReportMatrixByMatrixId(reportNo, testMatriId, collectedData);

        if (reportMatrix == null) {
            return null;
        }

        JSONArray testLineListArray = collectedData.getJSONArray(StandardDataStructureConstants.JSON_SECTION_NAME_TEST_LINE_LIST);
        if (Func.isEmpty(testLineListArray)) {
            //如果TestLine Section 为空，按False处理，继续走matrix逻辑
            return null;
        }


        List<RdTestLineDTO> testLineList = JSONObject.parseArray(testLineListArray.toString(), RdTestLineDTO.class);

        if (CollectionUtil.isEmpty(testLineList)) {
            return null;
        }


        RdTestLineDTO testLineDTO = testLineList.stream().filter(t -> t.getTestLineInstanceId().equals(reportMatrix.getTestLineInstanceId())).findFirst().get();

        return testLineDTO;
    }

    /**
     * 根据testMatrixId 定位到ReportTestMatrix节点
     *
     * @param testMatriId
     * @param collectedData
     * @return
     */
    protected RdReportMatrixDTO getReportMatrixByMatrixId(String reportNo, String testMatriId, CollectedData collectedData) {
        if (StringUtil.isBlank(testMatriId)) {
            return null;
        }

        //获取ReportList节点，根据TestMatrixId 确定ReportMatrix对象
        JSONArray testReportListArray = collectedData.getJSONArray(StandardDataStructureConstants.JSON_SECTION_NAME_REPORT_LIST);
        if (Func.isEmpty(testReportListArray)) {
            //如果TestLine Section 为空，按False处理，继续走matrix逻辑
            return null;
        }

        List<RdReportDTO> reportList = JSONObject.parseArray(testReportListArray.toString(), RdReportDTO.class);

        if (CollectionUtil.isEmpty(reportList)) {
            return null;
        }
        RdReportDTO rdReportDTO = reportList.stream().filter(l -> Objects.equals(reportNo, l.getReportNo())).findFirst().orElse(null);
        List<RdReportMatrixDTO> newReportMatrixList = new ArrayList<>();
        List<RdReportMatrixDTO> matrixList = rdReportDTO.getReportMatrixList();
        if (CollectionUtil.isNotEmpty(matrixList)) {
            newReportMatrixList.addAll(matrixList);
        }

        RdReportMatrixDTO reportMatrix = newReportMatrixList.stream().filter(reportMatrixDTO -> StringUtil.equals(testMatriId, reportMatrixDTO.getTestMatrixId())).findFirst().orElse(null);

        return reportMatrix;
    }

    protected RdReportDTO getReportByMatrixId(String testMatriId, CollectedData collectedData, String reportNo) {
        if (StringUtil.isBlank(testMatriId)) {
            return null;
        }

        //获取ReportList节点，根据TestMatrixId 确定ReportMatrix对象
        JSONArray testReportListArray = collectedData.getJSONArray(StandardDataStructureConstants.JSON_SECTION_NAME_REPORT_LIST);
        if (Func.isEmpty(testReportListArray)) {
            //如果TestLine Section 为空，按False处理，继续走matrix逻辑
            return null;
        }

        List<RdReportDTO> reportList = JSONObject.parseArray(testReportListArray.toString(), RdReportDTO.class);

        if (CollectionUtil.isEmpty(reportList)) {
            return null;
        }

        RdReportDTO rdReport = reportList.stream().filter(l -> Objects.equals(reportNo, l.getReportNo())).findFirst().orElse(null);
        if (Func.isEmpty(rdReport)) {
            return null;
        }

        List<RdReportMatrixDTO> newReportMatrixList = new ArrayList<>();
        List<RdReportMatrixDTO> reportMatrixList = rdReport.getReportMatrixList();
        if (CollectionUtil.isNotEmpty(reportMatrixList)) {
            newReportMatrixList.addAll(reportMatrixList);

            RdReportMatrixDTO reportMatrixDTO = reportMatrixList.stream().filter(r -> StringUtil.equals(testMatriId, r.getTestMatrixId())).findFirst().orElse(null);
            if (reportMatrixDTO != null) {
                return rdReport;
            }
        }

        return null;
    }

}
