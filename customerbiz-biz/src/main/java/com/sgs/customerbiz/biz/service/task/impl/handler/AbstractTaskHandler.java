package com.sgs.customerbiz.biz.service.task.impl.handler;

import com.sgs.customerbiz.biz.assembler.TaskExecuteLogAssembler;
import com.sgs.customerbiz.biz.enums.TaskResultEnum;
import com.sgs.customerbiz.biz.service.task.TaskService;
import com.sgs.customerbiz.dbstorages.mybatis.model.TaskExecuteLogPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TaskInfoPO;
import com.sgs.customerbiz.domain.manager.TaskLogManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;

/**
 * @author: shawn.yang
 * @create: 2023-05-09 14:11
 */
@Slf4j
@Component
public abstract class AbstractTaskHandler implements TaskHandler {

    @Autowired
    private TaskService taskService;

    @Autowired
    private TaskLogManager taskLogManager;

    @Override
    public final TaskExecResult handle(TaskInfoPO taskInfo) {
        log.info("TaskHandler:[{}] start execute... taskId:{}",getClass().getName(),taskInfo.getTaskId());
        TaskExecResult result = null;
        try {
            // 1.执行
            result = this.execute(taskInfo.getTaskParameters());
            if (result == null) {
                throw new IllegalArgumentException("未获取到task执行结果");
            }

            // 2.更新执行结果
            boolean updateTaskSuccess = taskService.updateTaskAfterExecute(taskInfo, result);
            if (updateTaskSuccess) {
                log.info("update task finish. taskId:{} ,result:{}", taskInfo.getTaskId(), result);
            } else {
                log.error("update task fail. taskId:{} ,result:{}", taskInfo.getTaskId(), result);
            }
            log.info("TaskHandler:[{}] finished execute... taskId:{}",getClass().getName(),taskInfo.getTaskId());
            return result;
        } catch (Exception e) {
            log.error("TaskHandler:[{}] execute error", getClass(), e);
            result =new TaskExecResult(TaskResultEnum.SYSTEM_ERROR,e.getMessage());
            return result;
        } finally {
            if (result != null) {
                // 保存task执行日志
                TaskExecuteLogPO taskExecuteLog = TaskExecuteLogAssembler.assembleTaskExecuteLog(taskInfo, result);
//                taskLogManager.saveLog(taskExecuteLog);
                log.info("task result: {}", taskExecuteLog);
            }


            //todo publish task event
        }
    }

    @NotNull
    protected abstract TaskExecResult execute(String parameters);

}
