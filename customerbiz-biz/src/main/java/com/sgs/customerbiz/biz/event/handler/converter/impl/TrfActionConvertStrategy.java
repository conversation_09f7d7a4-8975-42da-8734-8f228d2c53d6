package com.sgs.customerbiz.biz.event.handler.converter.impl;

import com.alibaba.fastjson.JSON;
import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.config.api.dto.SystemApiDTO;
import com.sgs.customerbiz.biz.convert.DataConvertor;
import com.sgs.customerbiz.biz.event.handler.converter.DataConvertStrategy;
import com.sgs.customerbiz.biz.service.datacollector.CollectedData;
import com.sgs.customerbiz.domain.domainevent.ObjectEvent;
import com.sgs.customerbiz.domain.domainevent.TrfActionEvent;
import org.springframework.stereotype.Component;
import com.sgs.customerbiz.domain.domainevent.actionevent.TrfUpdateInfoEvent;
import org.springframework.core.annotation.Order;

import javax.annotation.Resource;

/**
 * TRF动作事件转换策略
 */
@Order(3)
@Component
public class TrfActionConvertStrategy implements DataConvertStrategy {

    @Resource
    private DataConvertor<String, String, JSON> jsonDataConvertor;

    @Override
    public boolean supports(ObjectEvent event, EventSubscribeDTO subscribe) {
        return event instanceof TrfActionEvent && !(event instanceof TrfUpdateInfoEvent);
    }

    @Override
    public Object convert(ObjectEvent event, EventSubscribeDTO subscribe, 
            CollectedData collectedData, SystemApiDTO systemApi) {
        return jsonDataConvertor.convert(collectedData.toJSONString(), 
            systemApi.getRequestBodyTemplate());
    }
} 