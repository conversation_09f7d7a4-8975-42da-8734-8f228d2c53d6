package com.sgs.customerbiz.biz.service.preview;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sgs.config.api.dto.req.ConfigGetReq;
import com.sgs.customerbiz.biz.config.PreviewProps;
import com.sgs.customerbiz.biz.convert.impl.JsonDataConvertor;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.dto.resp.CustomerGeneralConfig;
import com.sgs.customerbiz.model.trf.dto.resp.TestLinePreviewDTO;
import com.sgs.framework.tool.utils.Func;
import lombok.Getter;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PreviewSyncReportService {

    public static final String TEST_LINE_TEMPLATES = "TestLineTemplates";

    private final PreviewProps previewProps;


    private final TrfInfoExtMapper trfInfoExtMapper;

    private final CustomerTestLineService customerTestLineService;

    private final SgsTestLineService testLineService;

    private final ConfigClient configClient;

    private final JsonDataConvertor jsonDataConvertor;

    public PreviewSyncReportService(PreviewProps previewProps,
                                    TrfInfoExtMapper trfInfoExtMapper,
                                    CustomerTestLineService customerTestLineService,
                                    SgsTestLineService testLineService,
                                    ConfigClient configClient, JsonDataConvertor jsonDataConvertor) {
        this.previewProps = previewProps;
        this.trfInfoExtMapper = trfInfoExtMapper;
        this.customerTestLineService = customerTestLineService;
        this.testLineService = testLineService;
        this.configClient = configClient;
        this.jsonDataConvertor = jsonDataConvertor;
    }

    public List<TestLinePreviewDTO> preview(TrfSyncReq request) {
        // 检查请求数据，并且根据请求数据，加载必要的如客户trf列表数据
        PreviewCtx ctx = loadAndCheck(request);
        String customerGroupCode = configClient.getCustomerConfigBy(request.getHeader().getRefSystemId())
                .map(CustomerGeneralConfig::getCustomerGroupCode)
                .orElseThrow(() -> new IllegalArgumentException("CustomerConfig Not Found From SCI by " + request.getHeader().getRefSystemId()));

        ConfigGetReq configGetReq = new ConfigGetReq();
        configGetReq.setProductLine(request.getBuCode());
        configGetReq.setIdentityId(Func.toStr(request.getHeader().getRefSystemId()));
        configGetReq.setConfigKey(TEST_LINE_TEMPLATES);
        String configValue = configClient.getConfig(configGetReq);
        String customerTestLineTemplate = Optional.ofNullable(configValue)
                .map(value -> JSONObject.parseObject(value, Feature.OrderedField))
                .map(json -> json.getJSONObject("customerTestLineTemplate"))
                .map(Objects::toString)
                .orElse("");
        String finalTestLineTemplate = Optional.ofNullable(configValue)
                .map(value -> JSONObject.parseObject(value, Feature.OrderedField))
                .map(json -> json.getJSONObject("finalTestLineTemplate"))
                .map(Objects::toString)
                .orElse("");
        // 根据模板，将客户的trf数据转换成需要预览的列表数据 List<CustomerTestLineJSONObject>
        // 根据CustomerTestLineJSONObject的itemNo属性查询ILayer得到 @TestLineMappingInfoV2DTO 多个customerTestLine可能对应到一个Mapping
        List<CustomerTestLine> customerTestLines = customerTestLineService.testLines(customerGroupCode, request.getBuCode(), customerTestLineTemplate, ctx.getCustomerTrfMap().values());
        // 根据request的testLineList通过ILayer查询得到 @TestLineMappingInfoV2DTO 一个testLine可能对应到多个Mapping
        List<SgsTestLine> trfTestLineDTOS = testLineService.testLines(customerGroupCode, request.getBuCode(), request.getTestLineList());
        // 通过CustomerTestLineJSONObjectData 对应的Mapping和testLineList的Mapping的asKey进行比较，执行一个zipWith操作
        //
        List<TestLineRow> rows = zip(customerTestLines, trfTestLineDTOS);
        JSONObject root = new JSONObject();
        root.put("testLines", rows);
        JSON convert = jsonDataConvertor.convert(JSON.toJSONString(root, SerializerFeature.WriteMapNullValue), finalTestLineTemplate);

        return Optional.ofNullable(convert)
                .map(c -> (JSONObject) c)
                .map(c -> c.getJSONArray("rows"))
                .map(rowList -> JSONArray.parseArray(rowList.toJSONString(), TestLinePreviewDTO.class))
                .orElse(Collections.emptyList());

    }

    public PreviewCtx loadAndCheck(TrfSyncReq request) {

        Map<String, CustomerTrfInfoRsp> customerTrfInfoMap = request.getHeader().getTrfList().stream()
                .map(trf -> trfInfoExtMapper.getTrfInfo(trf.getRefSystemId(), trf.getTrfNo()))
                .collect(Collectors.toMap(CustomerTrfInfoRsp::getTrfNo, Function.identity(), (v1, v2) -> v1));
        return new PreviewCtx(customerTrfInfoMap);
    }

    @Getter
    public static class PreviewCtx {

        private final Map<String, CustomerTrfInfoRsp> customerTrfMap;

        public PreviewCtx(Map<String, CustomerTrfInfoRsp> customerTrfMap) {
            this.customerTrfMap = customerTrfMap;
        }
    }

    public List<TestLineRow> zip(List<CustomerTestLine> customerTestLines, List<SgsTestLine> trfTestLineDTOS) {

        Map<Boolean, List<CustomerTestLine>> customerTestLineGroupByHasMapping = customerTestLines.stream().collect(Collectors.groupingBy(CustomerTestLine::hasMapping));
        Map<Boolean, List<SgsTestLine>> sgsTestLineGroupByMapping = trfTestLineDTOS.stream().collect(Collectors.groupingBy(SgsTestLine::hasMapping));

        List<TestLineRow> notMatchSgsTestLineList = sgsTestLineGroupByMapping.getOrDefault(false,Collections.emptyList()).stream()
                .map(TestLineRow::onlySgs)
                .collect(Collectors.toList());
        List<TestLineRow> notMatchCustomerTestLineList = customerTestLineGroupByHasMapping.getOrDefault(false,Collections.emptyList()).stream()
                .map(TestLineRow::onlyCustomer)
                .collect(Collectors.toList());

        List<CustomerTestLine> customerTestLineHasMappingList = customerTestLineGroupByHasMapping.getOrDefault(true, Collections.emptyList());
        List<SgsTestLine> sgsTestLineHasMappingList = sgsTestLineGroupByMapping.getOrDefault(true, Collections.emptyList());
        Map<String, SgsTestLine> sgsTestLineHasMappingMap = sgsTestLineHasMappingList.stream().collect(Collectors.toMap(SgsTestLine::asKey, Function.identity(), (v1, v2) -> v1));
        List<TestLineRow> rows = new ArrayList<>();
        for (CustomerTestLine customerTestLine : customerTestLineHasMappingList) {
            if(sgsTestLineHasMappingMap.containsKey(customerTestLine.asKey())){
                rows.add(TestLineRow.match(customerTestLine, sgsTestLineHasMappingMap.remove(customerTestLine.asKey())));
            } else {
                notMatchSgsTestLineList.add(TestLineRow.onlyCustomer(customerTestLine));
            }
        }
        sgsTestLineHasMappingMap.values().forEach((v) -> notMatchCustomerTestLineList.add(TestLineRow.onlySgs(v)));
        rows.addAll(notMatchCustomerTestLineList);
        rows.addAll(notMatchSgsTestLineList);
        // CustomerTestLine和 TrfTestLineDTO都实现了 TestMappingKey
        return rows;
    }
}
