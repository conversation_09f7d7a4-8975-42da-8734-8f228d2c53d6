package com.sgs.customerbiz.biz.event.eventhandler.customer.sgsmart;

import cn.hutool.core.collection.CollUtil;
import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.customerbiz.biz.convert.impl.fn.GetMappingValueFn;
import com.sgs.customerbiz.biz.event.eventhandler.customer.sgsmart.dto.*;
import com.sgs.customerbiz.biz.event.handler.HandleContextHolder;
import com.sgs.customerbiz.biz.service.StarLimsService;
import com.sgs.customerbiz.core.util.AF;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfOrderMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfReportPO;
import com.sgs.customerbiz.domain.domainevent.ObjectEvent;
import com.sgs.customerbiz.domain.domainevent.TrfPendingEvent;
import com.sgs.customerbiz.domain.domainevent.TrfTestingEvent;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfOrderDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfReportDomainService;
import com.sgs.customerbiz.facade.model.enums.BoundStatus;
import com.sgs.customerbiz.integration.DffClient;
import com.sgs.customerbiz.integration.FrameWorkClient;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.enums.*;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.ActiveType;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.Func;
import com.sgs.preorder.facade.model.info.LabInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/8/10 14:55
 */
public abstract class AbstractSgsMartEventHandler implements SgsMartNotifyDataAssembler {

    @Resource
    private TrfOrderMapper trfOrderMapper;

    @Resource
    private TrfInfoMapper trfInfoMapper;

    @Resource
    private TrfInfoExtMapper trfInfoExtMapper;

    @Autowired
    private TrfDomainService trfDomainService;

    @Autowired
    private TrfOrderDomainService trfOrderDomainService;

    private static final Integer STARLIMS = 30;
    private static final Integer PARENT_REPROT_STSTUS = 1;
    @Autowired
    private GetMappingValueFn getMappingValueFn;

    @Autowired
    private TrfReportDomainService trfReportDomainService;

    @Autowired
    private DffClient dffClient;

    @Autowired
    private FrameWorkClient frameWorkClient;

    @Autowired
    private StarLimsService starLimsService;

    public Object getCompleteReq(ObjectEvent trfEvent, EventSubscribeDTO subscribe, Integer actionType) {
        TrfFullDTO payload = (TrfFullDTO) trfEvent.getPayload();
        List<TrfOrderDTO> orderList = payload.getOrderList();
        if (Func.isEmpty(orderList)) {
            throw new BizException("Data Exception,Order cannot null!");
        }
        String trfNo = pickTrfNoFromTrfInfo(trfEvent);
        SgsMartBaseInfoReq baseInfoReq = new SgsMartBaseInfoReq();
        baseInfoReq.setMsgId(HandleContextHolder.getTaskId(trfEvent.getEventId(), subscribe.getApiId()));
        baseInfoReq.setActionType(actionType);
        // TODO SCI-900
        if (Objects.equals(trfEvent.getSystemId(), 10002)) {
            trfEvent.setSystemId(1);
        }
        trfEvent.setRefSystemId(RefSystemIdEnum.SGSMart.getRefSystemId());
        baseInfoReq.setSourceSystem(trfEvent.getSystemId());
        baseInfoReq.setProductLineCode(trfEvent.getProductLineCode());
        List<SgsMatDataInfo> dataInfoList = new ArrayList<>();
        baseInfoReq.setData(dataInfoList);
        List<TrfReportDTO> reportList = payload.getReportList();
        if (Func.isEmpty(reportList)) {
            throw new BizException("report cannot null!");
        }
        List<TrfHeaderDTO> trfList = payload.getTrfList();
        Date trfExpectDueDate;
        Date sampleReceiveDate;
        if (Func.isNotEmpty(trfList)) {
            trfExpectDueDate = trfList.get(0).getTrfExpectDueDate();
            sampleReceiveDate = trfList.get(0).getSampleReceiveDate();
        } else {
            trfExpectDueDate = null;
            sampleReceiveDate= null;
        }
        List<TrfTestLineDTO> testLineList = Func.isEmpty(payload.getTestLineList()) ? new ArrayList<>() : payload.getTestLineList();
        Map<String, List<TrfTestLineDTO>> testLineMap = testLineList.stream().collect(Collectors.groupingBy(TrfTestLineDTO::getTestLineInstanceId));
        List<TrfTestSampleDTO> testSampleList = Func.isEmpty(payload.getTestSampleList()) ? new ArrayList<>() : payload.getTestSampleList();
        Map<String, List<TrfTestSampleDTO>> sampleMap = testSampleList.stream().collect(Collectors.groupingBy(TrfTestSampleDTO::getTestSampleInstanceId));
        Map<String, List<TrfReportDTO>> reportMap = reportList.stream().collect(Collectors.groupingBy(TrfReportDTO::getOrderNo));
        Map<String, List<TrfTestLineDTO>> testLineByReport = safeCreateTestLineMapByReport(testLineList, reportList);
        orderList.forEach(
                l -> this.getInfoByReport(trfNo, trfEvent, trfExpectDueDate, sampleReceiveDate, dataInfoList, testLineMap, sampleMap, reportMap, l,
                        trfList, testLineByReport)
        );
        return baseInfoReq;
    }

    private static Map<String, List<TrfTestLineDTO>> safeCreateTestLineMapByReport(List<TrfTestLineDTO> testLineList, List<TrfReportDTO> reportList) {
        boolean hasBlankReportNo = testLineList.stream().anyMatch(testLine -> StringUtils.isBlank(testLine.getReportNo()));
        if(hasBlankReportNo) {
            Map<String, List<TrfTestLineDTO>> resultMap = new HashMap<>();
            for (TrfReportDTO report : reportList) {
                resultMap.put(report.getReportNo(), testLineList);
            }
            return resultMap;
        }
        return testLineList.stream().collect(Collectors.groupingBy(TrfTestLineDTO::getReportNo));
    }

    private String pickTrfNoFromTrfInfo(ObjectEvent trfEvent) {
        TrfInfoPO trfInfoPO = trfDomainService.selectByTrfNo(trfEvent.getRefSystemId(), trfEvent.getTrfNo());
        return StringUtils.isNotBlank(trfInfoPO.getSgsTrfNo()) ? trfInfoPO.getSgsTrfNo() : trfEvent.getTrfNo();
    }

    private void getInfoByReport(String trfNo, ObjectEvent trfEvent, Date trfExpectDueDate, Date sampleReceiveDate, List<SgsMatDataInfo> dataInfoList, Map<String, List<TrfTestLineDTO>> testLineMap, Map<String, List<TrfTestSampleDTO>> sampleMap, Map<String, List<TrfReportDTO>> reportMap, TrfOrderDTO order,
                                 List<TrfHeaderDTO> trfList,Map<String, List<TrfTestLineDTO>> testLineByReport) {
        List<TrfReportDTO> reports = reportMap.get(order.getOrderNo());
        List<TrfCustomerDTO> customerList = order.getCustomerList();
        String sgsUserId = null;
        String sgsAccountCode = null;
        if (Func.isNotEmpty(customerList)) {
            TrfCustomerDTO trfCustomerDTO = customerList.stream().filter(l -> Objects.equals(l.getCustomerUsage(), CustomerUsage.Applicant.getUsage())).findFirst().orElse(null);
            if (Func.isNotEmpty(trfCustomerDTO)) {
                List<TrfCustomerContactDTO> customerContactList = trfCustomerDTO.getCustomerContactList();
                if (Func.isNotEmpty(customerContactList)) {
                    sgsUserId = customerContactList.get(0).getSgsUserId();
                    sgsAccountCode = customerContactList.get(0).getSgsAccountCode();
                }
            }
        }

        List<TrfProductDTO> productList = Func.isEmpty(order.getProductList()) ? new ArrayList<>() : order.getProductList();
        List<TrfProductSampleDTO> sampleList = Func.isEmpty(order.getSampleList()) ? new ArrayList<>() : order.getSampleList();
        if (Objects.equals(trfEvent.getSystemId(), STARLIMS) && Func.isNotEmpty(reports.get(0).getLab())) {
            LabInfo labInfo = frameWorkClient.getLabCodeInfoByLabCodeFromCache(reports.get(0).getLab().getLabCode(), null);
            Optional<String> buCode = Optional.ofNullable(labInfo).map(LabInfo::getProductLineAbbr);
            Optional<String> templateId = Optional.ofNullable(productList).flatMap(ps -> ps.stream().findFirst()).map(TrfProductDTO::getTemplateId);
            Optional<Long> bossNo = Optional.ofNullable(customerList)
                    .flatMap(cs ->
                            cs.stream()
                                    .filter(c -> Objects.equals(c.getCustomerUsage(), CustomerUsage.Buyer.getUsage()))
                                    .map(TrfCustomerDTO::getBossNo)
                                    .filter(Objects::nonNull)
                                    .findFirst()
                    );
            Optional<StarLimsService.TemplateMapping> templateMapping = AF.ap3(starLimsService::getTemplateMapping, buCode, templateId, bossNo);
            if (templateMapping.isPresent()) {
                StarLimsService.TemplateMapping templateInfo = templateMapping.get();
                productList.forEach(l -> l.setTemplateId(templateInfo.getFormId()));
                sampleList.forEach(l -> l.setTemplateId(templateInfo.getGridId()));
            } else {
                productList.clear();
                sampleList.clear();
            }
        }
        if (Func.isNotEmpty(reports)) {
            String finalSgsUserId = sgsUserId;
            String finalSgsAccountCode = sgsAccountCode;
            TrfInfoPO trfInfoPO = trfDomainService.selectByTrfNo(trfEvent.getRefSystemId(), trfEvent.getTrfNo());
            //过滤reports列表中OriginalReportNo不为空的report获取OriginalReportNoList
            List<String> originalReportNoList = reports.stream().filter(report -> Func.isNotEmpty(report.getOriginalReportNo())).map(TrfReportDTO::getOriginalReportNo).collect(Collectors.toList());
            Map<String, TrfReportPO> trfReportPOMap = new HashMap<>();
            List<TrfReportPO> trfReportPOList = trfReportDomainService.selectByActiveReportNoList(originalReportNoList, trfInfoPO.getId());
            //如果结果不为空,则建立以reportNo为key，对象为value的map集合
            if (Func.isNotEmpty(trfReportPOList)) {
                trfReportPOMap = trfReportPOList.stream().collect(Collectors.toMap(TrfReportPO::getReportNo, Function.identity(), TrfReportPO::reduceByLeft));
            }
            Map<String, TrfReportPO> finalTrfReportPOMap = trfReportPOMap;
            reports.forEach(
                    report -> {
                        String reportNo = report.getReportNo();
                        SgsMatDataInfo data = new SgsMatDataInfo();
                        data.setReportFileList(mappingFileType(order.getAttachmentList(), report.getReportFileList()));
                        // testLineList
                        data.setTestLineList(Optional.ofNullable(testLineByReport.get(reportNo)).orElseGet(Collections::emptyList));
                        // productList
                        data.setProductList(
                                productList.stream()
                                        .filter(Objects::nonNull)
                                        .filter(product ->
                                                StringUtils.isBlank(product.getReportNo())
                                                        || Objects.equals(report.getReportNo(), product.getReportNo()))
                                        .collect(Collectors.toList())
                        );
                        // sampleList
                        data.setSampleList(
                                sampleList.stream().filter(Objects::nonNull)
                                        .filter(sample ->
                                                StringUtils.isBlank(sample.getReportNo())
                                                        || Objects.equals(report.getReportNo(), sample.getReportNo()))
                                        .collect(Collectors.toList())
                        );
                        // customerList
                        data.setCustomerList(customerList);
                        SgsMatRelationshipReq relationshipReq = new SgsMatRelationshipReq();
                        SgsMatRelationshipParentReq parentReq = new SgsMatRelationshipParentReq();
                        relationshipReq.setParent(parentReq);
                        SgsMatRelationshipChildrenReq childrenReq = new SgsMatRelationshipChildrenReq();
                        childrenReq.setOrderList(Arrays.asList(order));
                        relationshipReq.setChildren(childrenReq);
                        data.setRelationship(relationshipReq);
                        parentReq.setParcelNoList(trfList.get(0).getParcelNoList());
                        SgsMatRelationshipCustomerTrfReq customerTrfReq = new SgsMatRelationshipCustomerTrfReq();
                        customerTrfReq.setRefSystemId(trfList.get(0).getRefSystemId());
                        customerTrfReq.setCustomerTrfNo(trfList.get(0).getTrfNo());
                        parentReq.setCustomerTrf(customerTrfReq);
                        data.setDueDate(trfExpectDueDate);
                        data.setSampleReceivedDate(sampleReceiveDate);
                        data.setUserId(finalSgsUserId);
                        data.setAccountCode(finalSgsAccountCode);
                        data.setTrfNo(trfNo);
                        data.setOrderNo(order.getOrderNo());
                        String realOrderNo = Optional.ofNullable(order.getRealOrderNo()).filter(StringUtils::isNotBlank).orElse(order.getOrderNo());
                        data.setRealOrderNo(realOrderNo);
                        data.setGeneralOrderId(order.getOrderId());
                        data.setRootReportNo(report.getRootReportNo());
                        data.setReportVersion(report.getReportVersion());
                        data.setReportNo(reportNo);
                        TrfConclusionDTO conclusion = report.getConclusion();
                        if (Func.isNotEmpty(conclusion)) {
                            data.setReportConclusion(conclusion.getConclusionCode());
                            data.setCustomConclusion(conclusion.getCustomerConclusion());
                        }
                        // order.serviceStartDate
                        data.setServiceStartDate(order.getServiceStartDate());
                        // reportList.approveDate
                        data.setReportApprovalDate(report.getApproveDate());
                        // reportList.originalReportNo
                        data.setParentReportNo(report.getOriginalReportNo());
                        String parentReportStatus = getParentReportStatus(report, finalTrfReportPOMap);
                        data.setParentReportStatus(parentReportStatus);
                        data.setTrfNo(trfNo);
                        data.setOrderNo(order.getOrderNo());
                        data.setGeneralOrderId(order.getOrderId());
                        List<SgsMartMatrixInfo> martMatrixInfos = new ArrayList<>();
                        data.setMatrixInfo(martMatrixInfos);
                        List<TrfReportMatrixDTO> reportMatrixList = report.getReportMatrixList();
                        if (Func.isNotEmpty(reportMatrixList)) {
                            reportMatrixList.forEach(
                                    matrix -> {
                                        SgsMartMatrixInfo matrixInfo = new SgsMartMatrixInfo();
                                        // testLineList.testLineId
                                        matrixInfo.setTestLineId(Func.isNotEmpty(testLineMap.get(matrix.getTestLineInstanceId())) ? String.valueOf(testLineMap.get(matrix.getTestLineInstanceId()).get(0).getTestLineId()) : null);
                                        matrixInfo.setTestLineInstanceId(matrix.getTestLineInstanceId());
                                        //testLineList.evaluationAlias
                                        matrixInfo.setEvaluationAlias(Func.isNotEmpty(testLineMap.get(matrix.getTestLineInstanceId())) ? testLineMap.get(matrix.getTestLineInstanceId()).get(0).getEvaluationAlias() : null);
                                        List<TrfTestSampleDTO> trfTestSampleDTOS = sampleMap.get(matrix.getTestSampleInstanceId());
                                        if (Func.isNotEmpty(trfTestSampleDTOS)) {
                                            TrfTestSampleDTO trfTestSampleDTO = getTestSampleByReportNo(trfTestSampleDTOS, reportNo);
                                            matrixInfo.setSampleNo(trfTestSampleDTO.getTestSampleNo());
                                            TrfMaterialAttrDTO materialAttr = trfTestSampleDTO.getMaterialAttr();
                                            if (materialAttr != null) {
                                                matrixInfo.setSampleDescription(materialAttr.getMaterialDescription());
                                                matrixInfo.setSampleColor(materialAttr.getMaterialColor());
                                            }
                                        }
                                        TrfConclusionDTO matrixConclusion = matrix.getConclusion();
                                        if (Func.isNotEmpty(matrixConclusion)) {
                                            matrixInfo.setConclusionLevel(ConclusionTypeEnum.Matrix.getCode());
                                            // reportList.reportMatrixList.conclusion.customerConclusion
                                            matrixInfo.setConclusionDescription(matrixConclusion.getCustomerConclusion());
                                            // reportList.reportMatrixList.conclusion.conclusionRemark
                                            matrixInfo.setConclusionRemark(matrixConclusion.getConclusionRemark());
                                        }
                                        matrixInfo.setReportNo(report.getReportNo());
                                        matrixInfo.setConclusion(matrixConclusion);
                                        // reportList.reportMatrixList.testMatrixId
                                        matrixInfo.setTestMatrixId(matrix.getTestMatrixId());
                                        martMatrixInfos.add(matrixInfo);
                                    }
                            );
                        }
                        dataInfoList.add(data);
                    }
            );
        }
    }

    private static TrfTestSampleDTO getTestSampleByReportNo(List<TrfTestSampleDTO> trfTestSampleDTOS, String reportNo) {
        boolean hasBlankReportNo = trfTestSampleDTOS.stream().anyMatch(testSample -> StringUtils.isBlank(testSample.getReportNo()));
        if(hasBlankReportNo) {
            return trfTestSampleDTOS.get(0);
        }
        return trfTestSampleDTOS.stream().filter(testSample -> Objects.equals(testSample.getReportNo(), reportNo)).findFirst().orElseGet(() -> trfTestSampleDTOS.get(0));
    }

    private String getParentReportStatus(TrfReportDTO report, Map<String, TrfReportPO> finalTrfReportPOMap) {
        String parentReportStatus = "";//默认为空
        if (Func.isNotEmpty(report.getOriginalReportNo())) {
            TrfReportPO reportPO = finalTrfReportPOMap.get(report.getOriginalReportNo());

            if (Func.isNotEmpty(reportPO)) {
                Integer reportStatus = 0;
                if (reportPO.getReportStatus() == ReportStatusEnum.Approved.getCode() || reportPO.getReportStatus() == ReportStatusEnum.Completed.getCode()) {
                    reportStatus = PARENT_REPROT_STSTUS;
                }
                parentReportStatus = String.valueOf(reportStatus);
            }
        }
        return parentReportStatus;
    }

    private static List<TrfFileDTO> mappingFileType(List<TrfFileDTO> attachmentList, List<TrfFileDTO> reportFileList) {

        if (Func.isEmpty(attachmentList) && Func.isEmpty(reportFileList)) {
            return null;
        }
        List<TrfFileDTO> mergedList = new ArrayList<>();
        if (Func.isNotEmpty(attachmentList)) {
            mergedList.addAll(attachmentList);
        }
        if (Func.isNotEmpty(reportFileList)) {
            reportFileList.forEach(l -> l.setFileType("ReportFile"));
            mergedList.addAll(reportFileList);
        }
        return mergedList;
    }

    public Object getBaseReq(ObjectEvent trfEvent, EventSubscribeDTO subscribe, Integer actionType) {
        TrfFullDTO payload = (TrfFullDTO) trfEvent.getPayload();
        if (Func.isEmpty(payload)) {
            throw new BizException("payload cannot null!");
        }

        List<TrfOrderDTO> orderList = payload.getOrderList();
        if (Func.isEmpty(orderList)) {
            if (Func.isNotEmpty(payload.getOrder())) {
                orderList = Arrays.asList(payload.getOrder());
                payload.setOrderList(orderList);
            } else {
                throw new BizException("Data Exception,Order cannot null!");
            }
        }
        String trfNo = pickTrfNoFromTrfInfo(trfEvent);
        SgsMartBaseInfoReq baseInfoReq = new SgsMartBaseInfoReq();
        baseInfoReq.setActionType(actionType);
        baseInfoReq.setSourceSystem(trfEvent.getSystemId());
        baseInfoReq.setProductLineCode(trfEvent.getProductLineCode());
        trfEvent.setRefSystemId(RefSystemIdEnum.SGSMart.getRefSystemId());
        List<SgsMatDataInfo> dataInfoList = new ArrayList<>();
        baseInfoReq.setData(dataInfoList);
        baseInfoReq.setMsgId(HandleContextHolder.getTaskId(trfEvent.getEventId(), subscribe.getApiId()));
        if (CollectionUtils.isNotEmpty(orderList)) {
            orderList.forEach(
                    l -> {
//                       1vN 模式下数据过滤
                        if (trfEvent instanceof TrfTestingEvent) {
                            if (Func.isEmpty(l.getOrderStatus())) {
                                String orderNo = l.getOrderNo();
                                Integer systemId = l.getSystemId();
                                Integer orderStatus = trfOrderDomainService.selectOrderStatus(orderNo, systemId);
                                l.setOrderStatus(orderStatus);
                            }
                            if (!Objects.equals(TrfStatusEnum.Testing.getStatus(), l.getOrderStatus())) {
                                return;
                            }
                        }
                        if (trfEvent instanceof TrfPendingEvent) {
                            TrfOrderExample example = new TrfOrderExample();
                            example.createCriteria().andOrderNoEqualTo(l.getOrderNo()).andOrderIdEqualTo(l.getOrderId())
                                    .andActiveIndicatorEqualTo(ActiveType.Enable.getStatus())
                                    .andBoundStatusEqualTo(BoundStatus.BoundHasOrder.getType());
                            List<TrfOrderPO> trfOrderPOList = trfOrderMapper.selectByExample(example);
                            if (CollUtil.isNotEmpty(trfOrderPOList)) {
                                TrfOrderPO trfOrderPO = CollUtil.get(trfOrderPOList, 0);
                                if (!Objects.equals(trfOrderPO.getPendingFlag(), PendingFlagEnum.Pending.getType())) {
                                    return;
                                }
                            }
                        }
                        SgsMatDataInfo data = new SgsMatDataInfo();
                        data.setTrfNo(trfNo);
                        data.setOrderNo(l.getOrderNo());
                        String realOrderNo = Optional.ofNullable(l.getRealOrderNo()).filter(StringUtils::isNotBlank).orElse(l.getOrderNo());
                        data.setRealOrderNo(realOrderNo);
                        data.setGeneralOrderId(l.getOrderId());
                        data.setEnquiryNo(l.getEnquiryNo());
                        // TODO 此处暂时因为只有一对多的场景，并且SGSMart处结构也未升级，临时处理，只有一条数据，也只取第一条数据
                        List<TrfHeaderDTO> trfList = payload.getTrfList();
                        Date trfExpectDueDate;
                        Date sampleReceiveDate;
                        if (Func.isNotEmpty(trfList)) {
                            trfExpectDueDate = trfList.get(0).getTrfExpectDueDate();
                            sampleReceiveDate = trfList.get(0).getSampleReceiveDate();
                            TrfOtherDTO others = trfList.get(0).getOthers();
                            if (Func.isNotEmpty(others)) {
                                TrfPendingDTO pending = others.getPending();
                                if (Func.isNotEmpty(pending)) {
                                    data.setPendingType(pending.getPendingType());
                                }
                            }
                        } else {
                            if (Func.isNotEmpty(payload.getHeader())) {
                                trfExpectDueDate = payload.getHeader().getTrfExpectDueDate();
                                sampleReceiveDate = payload.getHeader().getSampleReceiveDate();
                            } else {
                                trfExpectDueDate = null;
                                sampleReceiveDate = null;
                            }
                        }
                        data.setSampleReceivedDate(sampleReceiveDate);
                        data.setDueDate(trfExpectDueDate);
                        data.setServiceStartDate(l.getServiceStartDate());
                        List<TrfCustomerDTO> customerList = l.getCustomerList();
                        if (Func.isEmpty(customerList)) {
                            customerList = payload.getCustomerList();
                        }
                        if (Func.isNotEmpty(customerList)) {
                            TrfCustomerDTO trfCustomerDTO = customerList.stream().filter(v -> Objects.equals(v.getCustomerUsage(), CustomerUsage.Applicant.getUsage())).findFirst().orElse(null);
                            if (Func.isNotEmpty(trfCustomerDTO)) {
                                List<TrfCustomerContactDTO> customerContactList = trfCustomerDTO.getCustomerContactList();
                                if (Func.isNotEmpty(customerContactList)) {
                                    data.setUserId(customerContactList.get(0).getSgsUserId());
                                    data.setAccountCode(customerContactList.get(0).getSgsAccountCode());
                                }
                            }
                        }
                        dataInfoList.add(data);
                    }
            );
        }
        return baseInfoReq;
    }
}
