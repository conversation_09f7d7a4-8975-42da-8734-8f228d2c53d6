package com.sgs.customerbiz.biz.event.eventhandler.customer.sgsmart;

import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.customerbiz.biz.event.handler.HandleContextHolder;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.message.IlayerTransparentReq;
import com.sgs.customerbiz.domain.domainevent.ObjectEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.sgs.customerbiz.biz.enums.PostBackActionType.Pending;

/**
 * <AUTHOR>
 */
@Component("sgsmart-TrfPendingEvent")
@Slf4j
public class SgsMartPendingEventHandler extends AbstractSgsMartEventHandler{

    public Object handle(ObjectEvent pendingEvent, EventSubscribeDTO subscribe) {
        Long taskId = HandleContextHolder.getTaskId(pendingEvent.getEventId(), subscribe.getApiId());
        return new IlayerTransparentReq(taskId, pendingEvent, getBaseReq(pendingEvent, subscribe, Pending.getType()));
    }
}
