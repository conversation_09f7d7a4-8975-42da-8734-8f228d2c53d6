package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
public class ToIntFn extends StringOperationFn {

    @Override
    protected Object invoke(Object arg1) {
        if(Objects.isNull(arg1) || StringUtils.isBlank(arg1.toString())) {
            return null;
        }
        try {
            return Integer.valueOf(arg1.toString());
        } catch (Throwable t) {
            log.warn("convert int from string ({}) error", arg1, t);
            return null;
        }
    }

    @Override
    public String getName() {
        return "toInt";
    }

    @Override
    public String desc() {
        return "toInt";
    }
}
