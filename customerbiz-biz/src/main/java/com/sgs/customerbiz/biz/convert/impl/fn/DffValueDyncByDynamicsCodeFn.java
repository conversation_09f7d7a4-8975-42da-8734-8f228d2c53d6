package com.sgs.customerbiz.biz.convert.impl.fn;

import com.alibaba.fastjson.JSONArray;
import com.sgs.customerbiz.biz.convert.impl.fn.base.StringOperationFn;
import com.sgs.customerbiz.integration.dto.DffMappingConfigRsp;
import com.sgs.framework.tool.utils.Func;
import com.sgs.testdatabiz.facade.model.dto.rd.report.RdProductSampleAttrDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Slf4j
@Component
public class DffValueDyncByDynamicsCodeFn extends StringOperationFn {

    @Override
    public Object invoke(Object[] args) {
        if (args == null) {
            return null;
        }
        String code = args[0].toString();
        Object codeList = args[1];
        if (Func.isEmpty(codeList)) {
            return null;
        }
        List<DffMappingConfigRsp> dffConfigRsps = JSONArray.parseArray(codeList.toString(), DffMappingConfigRsp.class);
        if (Func.isNotEmpty(dffConfigRsps)) {
            List<DffMappingConfigRsp> list = new ArrayList<>();
            dffConfigRsps.forEach(
                    l -> {
                        if (Func.isNotEmpty(l)) {
                            list.add(l);
                        }
                    }
            );
            String finalCode = code;
            DffMappingConfigRsp dffMappingRows = list.stream().filter(l -> Objects.equals(finalCode, l.getCustomerFieldCode())).findFirst().orElse(null);
            if (Func.isEmpty(dffMappingRows) || Func.isBlank(dffMappingRows.getMappingFieldCode())) {
                return null;
            }
            code = dffMappingRows.getMappingFieldCode();
        }
        String key = args[2].toString();
        List<RdProductSampleAttrDTO> rdProductDTOS = JSONArray.parseArray(key.toString(), RdProductSampleAttrDTO.class);
        String finalCode1 = code;
        RdProductSampleAttrDTO rdProductSampleAttrDTO = rdProductDTOS.stream().filter(l -> Objects.equals(l.getLabelCode(), finalCode1)).findFirst().orElse(null);
        if (Func.isNotEmpty(rdProductSampleAttrDTO)) {
            return rdProductSampleAttrDTO.getValue();
        }
        return null;
    }


    @Override
    public String getName() {
        return "dffValueDyncByDynamicsCodeFn";
    }

    @Override
    public String desc() {
        return "dffValueDyncByDynamicsCodeFn";
    }
}
