package com.sgs.customerbiz.biz.event.eventhandler.customer.shein;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.customerbiz.biz.event.eventhandler.customer.shein.dto.BodyReq;
import com.sgs.customerbiz.biz.event.eventhandler.customer.shein.dto.SheInBaseInfoReq;
import com.sgs.customerbiz.biz.event.eventhandler.customer.shein.dto.SyncTrfInfo;
import com.sgs.customerbiz.biz.event.handler.HandleContextHolder;
import com.sgs.customerbiz.domain.domainevent.ObjectEvent;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.customerbiz.domain.domainobject.v2.TrfHeaderDOV2;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.infrastructure.api.IdService;
import com.sgs.customerbiz.integration.CustomerConfigClient;
import com.sgs.customerbiz.model.trf.dto.*;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.extsystem.facade.model.customer.rsp.CustomerConfigRsp;
import com.sgs.framework.model.enums.EventTypeEnum;
import com.sgs.framework.model.enums.OrderStatusEnum;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component("shein-TrfConfirmedEvent")
@Slf4j
public class SheinConfirmedEventHandler implements SheinNotifyDataAssembler {

    @Resource
    private TrfDomainService trfDomainService;

    @Resource
    private CustomerConfigClient customerConfigClient;

    @Resource
    private IdService idService;


    public Object handle(ObjectEvent event, EventSubscribeDTO subscribe) {
        TrfFullDTO trfFullDTO = (TrfFullDTO) event.getPayload();
        TrfHeaderDTO header = trfFullDTO.getHeader();
        TrfLabDTO lab = header.getLab();
        TrfOrderDTO order = trfFullDTO.getOrder();
        TrfHeaderDOV2 trfHeaderDOV2 = trfDomainService.selectSimple(header.getRefSystemId(), header.getTrfNo());

        CustomerConfigRsp customerConfig = customerConfigClient.getCustomerConfig(header.getRefSystemId(), lab.getBuCode());
        SheInBaseInfoReq sheInBaseInfoReq = new SheInBaseInfoReq();
        sheInBaseInfoReq.setBu(lab.getBuCode());
        sheInBaseInfoReq.setCustomerGroupCode(customerConfig.getCustomerGroupCode());
        sheInBaseInfoReq.setObjectNumber(header.getTrfNo());
//        sheInBaseInfoReq.setApplicationMappingCode();
        sheInBaseInfoReq.setDataStatus(EventTypeEnum.ConfirmMatrix.name());

        SyncTrfInfo syncTrfInfo = new SyncTrfInfo();
        syncTrfInfo.setTrfNo(header.getTrfNo());
        syncTrfInfo.setOrderNo(order.getOrderNo());
        syncTrfInfo.setOrderCreateDate(Func.formatDateTimeQuietly(order.getServiceStartDate()));
        syncTrfInfo.setLocation(lab.getLocationCode());
        Integer sampleLevel = Func.toInteger(trfHeaderDOV2.getSampleLevel(), 1);
        syncTrfInfo.setCreateType(sampleLevel == 1 ? 2 : 1);
        syncTrfInfo.setOrderStatus(OrderStatusEnum.Confirmed.getStatus());
        syncTrfInfo.setProductLineCode(lab.getBuCode());
        syncTrfInfo.setOrderCategory(1);
        syncTrfInfo.setTrfStatus(TrfStatusEnum.Confirmed.getStatus());

        assembleReportInf(syncTrfInfo, trfFullDTO);

        BodyReq bodyReq = new BodyReq();
        bodyReq.setMsgId(HandleContextHolder.getTaskId(event.getEventId(), subscribe.getApiId()));
        bodyReq.setTrfList(Arrays.asList(syncTrfInfo));

        sheInBaseInfoReq.setBody(bodyReq);
        sheInBaseInfoReq.setRefSystemId(header.getRefSystemId());
        sheInBaseInfoReq.setRequestId(IdUtil.fastSimpleUUID());

        return sheInBaseInfoReq;

//        kafkaProducer.doSendExternal(TopicConstants.ORDER_FORILAYER, header.getTrfNo(), sheInBaseInfoReq);
    }


    public void assembleReportInf(SyncTrfInfo syncTrfInfo, TrfFullDTO trfFullDTO) {
        List<TrfReportDTO> reportList = trfFullDTO.getReportList();
        TrfReportDTO reportDTO = CollUtil.get(reportList, 0);
        if (Objects.nonNull(reportDTO)) {
            syncTrfInfo.setReportDueDate(Func.formatDateTimeQuietly(reportDTO.getReportDueDate()));
            syncTrfInfo.setReportNo(reportDTO.getReportNo());
        }
    }
}
