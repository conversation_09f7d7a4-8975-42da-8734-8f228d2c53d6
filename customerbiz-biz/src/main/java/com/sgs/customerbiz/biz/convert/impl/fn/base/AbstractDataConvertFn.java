package com.sgs.customerbiz.biz.convert.impl.fn.base;

/**
 * @author: shawn.yang
 * @create: 2023-09-07 10:08
 */
public abstract class AbstractDataConvertFn implements DataConvertFn {
    @Override
    public Object invoke(Object[] args) {
        if (args ==null || args.length ==0){
            return invoke();
        }

        if (args.length == 1){
            return invoke(args[0]);
        }else if (args.length == 2){
            return invoke(args[0],args[1]);
        }else if (args.length == 3){
            return invoke(args[0],args[1],args[2]);
        }else if (args.length == 4){
            return invoke(args[0],args[1],args[2],args[3]);
        }else if (args.length == 5){
            return invoke(args[0],args[1],args[2],args[3],args[4]);
        }else if (args.length == 6){
            return invoke(args[0],args[1],args[2],args[3],args[4],args[5]);
        }else if (args.length == 7){
            return invoke(args[0],args[1],args[2],args[3],args[4],args[5],args[6]);
        }else if (args.length == 8){
            return invoke(args[0],args[1],args[2],args[3],args[4],args[5],args[6],args[7]);
        }else if (args.length == 9){
            return invoke(args[0],args[1],args[2],args[3],args[4],args[5],args[6],args[7],args[8]);
        }else if (args.length == 10){
            return invoke(args[0],args[1],args[2],args[3],args[4],args[5],args[6],args[7],args[8],args[9]);
        }else {
            Object []extArgs =new Object[args.length-10];
            System.arraycopy(args,10,extArgs,0,extArgs.length);
            return invoke(args[0],args[1],args[2],args[3],args[4],args[5],args[6],args[7],args[8],args[9],extArgs);
        }
    }
    protected Object invoke() {
        throw new UnsupportedOperationException(String.format("DataConvertFn:%s ,illegal args length: 0",getName()));
    }

    protected Object invoke(Object arg1) {
        throw new UnsupportedOperationException(String.format("DataConvertFn:%s ,illegal args length: 1",getName()));
    }

    protected Object invoke(Object arg1,Object arg2) {
        throw new UnsupportedOperationException(String.format("DataConvertFn:%s ,illegal args length: 2",getName()));
    }

    protected Object invoke(Object arg1,Object arg2,Object arg3) {
        throw new UnsupportedOperationException(String.format("DataConvertFn:%s ,illegal args length: 3",getName()));
    }

    protected Object invoke(Object arg1,Object arg2,Object arg3,Object arg4) {
        throw new UnsupportedOperationException(String.format("DataConvertFn:%s ,illegal args length: 4",getName()));
    }

    protected Object invoke(Object arg1,Object arg2,Object arg3,Object arg4,Object arg5) {
        throw new UnsupportedOperationException(String.format("DataConvertFn:%s ,illegal args length: 5",getName()));
    }

    protected Object invoke(Object arg1,Object arg2,Object arg3,Object arg4,Object arg5,Object arg6) {
        throw new UnsupportedOperationException(String.format("DataConvertFn:%s ,illegal args length: 6",getName()));
    }

    protected Object invoke(Object arg1,Object arg2,Object arg3,Object arg4,Object arg5,Object arg6
            ,Object arg7) {
        throw new UnsupportedOperationException(String.format("DataConvertFn:%s ,illegal args length: 7",getName()));
    }

    protected Object invoke(Object arg1,Object arg2,Object arg3,Object arg4,Object arg5,Object arg6
            ,Object arg7,Object arg8) {
        throw new UnsupportedOperationException(String.format("DataConvertFn:%s ,illegal args length: 8",getName()));
    }

    protected Object invoke(Object arg1,Object arg2,Object arg3,Object arg4,Object arg5,Object arg6
            ,Object arg7,Object arg8,Object arg9) {
        throw new UnsupportedOperationException(String.format("DataConvertFn:%s ,illegal args length: 9",getName()));
    }

    protected Object invoke(Object arg1,Object arg2,Object arg3,Object arg4,Object arg5,Object arg6
            ,Object arg7,Object arg8,Object arg9,Object arg10) {
        throw new UnsupportedOperationException(String.format("DataConvertFn:%s ,illegal args length: 10",getName()));
    }

    protected Object invoke(Object arg1,Object arg2,Object arg3,Object arg4,Object arg5,Object arg6
            ,Object arg7,Object arg8,Object arg9,Object arg10,Object ...args) {
        throw new UnsupportedOperationException(String.format("DataConvertFn:%s ,illegal args length: %d",getName(),10+ args.length));
    }
}
