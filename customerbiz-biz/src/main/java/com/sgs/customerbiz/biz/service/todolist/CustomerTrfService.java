package com.sgs.customerbiz.biz.service.todolist;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sgs.customerbiz.core.util.DateUtils;
import com.sgs.customerbiz.core.util.NumberUtil;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.BoundTrfRelExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfTodoInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmodel.dto.SearchTrfInfoDTO;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.CustomerTrfInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfInfoMapper;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfReportMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.*;
import com.sgs.customerbiz.dfv.enums.ActiveIndicatorEnum;
import com.sgs.customerbiz.domain.enums.TrfOrderRelationshipRuleEnum;
import com.sgs.customerbiz.facade.model.dto.BoundTrfRelDTO;
import com.sgs.customerbiz.facade.model.enums.BoundStatus;
import com.sgs.customerbiz.facade.model.enums.CheckRspStatus;
import com.sgs.customerbiz.facade.model.enums.CreateType;
import com.sgs.customerbiz.facade.model.req.CustomerInfoReq;
import com.sgs.customerbiz.facade.model.req.SyncTrfInfoReq;
import com.sgs.customerbiz.facade.model.rsp.CodeValueRsp;
import com.sgs.customerbiz.facade.model.rsp.CustomerInfo;
import com.sgs.customerbiz.facade.model.todolist.dto.TrfContentDTO;
import com.sgs.customerbiz.facade.model.todolist.req.*;
import com.sgs.customerbiz.facade.model.todolist.rsp.CheckTrfInfoRsp;
import com.sgs.customerbiz.facade.model.todolist.rsp.TrfInfoRsp;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.integration.CustomerClient;
import com.sgs.customerbiz.integration.LocalILayerClient;
import com.sgs.customerbiz.integration.PreOrderClient;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.model.enums.OrderStatusEnum;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.model.enums.TrfStatusEnum;
import com.sgs.framework.tool.utils.Func;
import com.sgs.grus.async.AsyncUtils;
import com.sgs.preorder.facade.model.dto.todolist.TRFToDoListDTO;
import com.sgs.trimslocal.facade.model.enums.ProductLineTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.sgs.customerbiz.core.constants.Constants.*;

/**
 *
 */
@Service
public class CustomerTrfService {
    private static final Logger logger = LoggerFactory.getLogger(CustomerTrfService.class);
    @Autowired
    private TrfInfoExtMapper trfInfoExtMapper;
    @Autowired
    private PreOrderClient preOrderClient;
    @Autowired
    private LocalILayerClient todoListClient;
    @Autowired
    private TodoListService todoListService;
    @Autowired
    private BoundTrfRelExtMapper boundTrfRelExtMapper;
    @Autowired
    private CustomerClient customerClient;
    @Autowired
    private TrfTodoInfoExtMapper trfTodoInfoExtMapper;

    @Autowired
    private CustomerTrfInfoMapper customerTrfInfoMapper;

    @Resource
    private TrfInfoMapper trfInfoMapper;

    @Autowired
    private TrfReportMapper trfReportMapper;

    /**
     * @param reqObject
     * @return
     */
    public CustomResult<List<TrfInfoRsp>> getTrfInfo(TrfInfoReq reqObject) {
        CustomResult rspResult = new CustomResult();

        SearchTrfInfoDTO searchTrfInfoDTO = new SearchTrfInfoDTO();
        searchTrfInfoDTO.setHasOrder(false);
        searchTrfInfoDTO.setTrfNos(reqObject.getTrfNos());
        searchTrfInfoDTO.setPackageBarcodes(reqObject.getPackageBarcodes());
        searchTrfInfoDTO.setRefSystemId(reqObject.getRefSystemId());

        List<BoundTrfRelDTO> boundTrfRelDTOS = boundTrfRelExtMapper.queryBoundTrfInfo(searchTrfInfoDTO);

        if (CollectionUtils.isEmpty(boundTrfRelDTOS)) {
            return rspResult.fail(String.format("获取Trf(%s)信息失败", StringUtils.join(reqObject.getTrfNos(), ", ")));
        }
        List<TrfInfoRsp> trfInfoRsps = Lists.newArrayList();
        for (BoundTrfRelDTO infoRsp : boundTrfRelDTOS) {
            TrfInfoRsp trfInfoRsp = new TrfInfoRsp();
            trfInfoRsp.setTrfNo(infoRsp.getTrfNo());
            trfInfoRsp.setObjectNo(infoRsp.getObjectNo());
            trfInfoRsp.setOrderNo(infoRsp.getOrderNo());
            trfInfoRsp.setLabCode(infoRsp.getLabCode());
            trfInfoRsp.setBoundStatus(infoRsp.getBoundStatus());
            trfInfoRsp.setContent(infoRsp.getContent());
            trfInfoRsp.setRefSystemId(trfInfoRsp.getRefSystemId());
            if (StringUtils.isNotBlank(infoRsp.getContent()) && RefSystemIdEnum.check(reqObject.getRefSystemId(), RefSystemIdEnum.Shein)) {
                JSONObject content = JSONObject.parseObject(infoRsp.getContent());
                if (content.containsKey(CREATE_TYPE)) {
                    trfInfoRsp.setCreateType(NumberUtil.toInt(content.get(CREATE_TYPE)));
                }
            }
            trfInfoRsps.add(trfInfoRsp);
        }

        rspResult.setData(trfInfoRsps);
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * 绑定的时候 需要将订单号OrderNo 放到 Content中去/解绑的时候 需要将订单号OrderNo 属性的值 从Content中删除
     *
     * @param reqObject
     * @return
     */
    public CustomResult updateTrfBoundStatus(TrfBoundStatusReq reqObject) {
        CustomResult rspResult = new CustomResult();
        Integer boundStatus = reqObject.getBoundStatus();
        List<String> trfNos = reqObject.getTrfNos();
        Integer refSystemId = reqObject.getRefSystemId();
        String orderNo = reqObject.getOrderNo();
        String orderId = reqObject.getOrderId();

        String userName = reqObject.getUserName();

        SearchTrfInfoDTO searchTrfInfoDTO = new SearchTrfInfoDTO();
        searchTrfInfoDTO.setHasOrder(false);
        searchTrfInfoDTO.setTrfNos(trfNos);
        searchTrfInfoDTO.setLabCode(reqObject.getLabCode());
        searchTrfInfoDTO.setRefSystemId(reqObject.getRefSystemId());

        List<BoundTrfRelDTO> boundTrfRelDTOS = boundTrfRelExtMapper.queryBoundTrfInfo(searchTrfInfoDTO);
        if (CollectionUtils.isEmpty(boundTrfRelDTOS)) {
            return rspResult.fail(String.format("请检查数据, trf(%s)信息获取失败", StringUtils.join(trfNos, ",")));
        }

        if (BoundStatus.check(boundStatus, BoundStatus.BoundHasOrder) && StringUtils.isBlank(orderNo)) {
            return rspResult.fail(String.format("绑定操作需要OrderNo, trf(%s)", StringUtils.join(trfNos, ",")));
        }

        List<TrfOrderPO> boundTrfRels = Lists.newArrayList();
        for (BoundTrfRelDTO trfInfo : boundTrfRelDTOS) {
            TrfOrderPO boundTrfRel = new TrfOrderPO();
            // bound操作
            if (BoundStatus.check(boundStatus, BoundStatus.BoundHasOrder)) {
                boundTrfRel.setOrderNo(orderNo);
                boundTrfRel.setOrderId(orderId);
                boundTrfRel.setTrfNo(trfInfo.getTrfNo());
                boundTrfRel.setBoundStatus(BoundStatus.BoundHasOrder.getType());
                boundTrfRel.setRefSystemId(refSystemId);
                boundTrfRel.setCreatedDate(DateUtils.getNow());
                boundTrfRel.setCreatedBy(userName);
            } else {
                // 更新操作
                boundTrfRel.setId(trfInfo.getBoundId());
                boundTrfRel.setOrderNo(orderNo);
                boundTrfRel.setOrderId(orderId);
                boundTrfRel.setTrfNo(trfInfo.getTrfNo());
                boundTrfRel.setRefSystemId(refSystemId);
                boundTrfRel.setBoundStatus(BoundStatus.UnBound.getType());
                boundTrfRel.setModifiedDate(DateUtils.getNow());
                boundTrfRel.setModifiedBy(userName);
            }
            boundTrfRels.add(boundTrfRel);
        }

        rspResult.setSuccess(boundTrfRelExtMapper.batchInsert(boundTrfRels) > 0);
        return rspResult;
    }

    /**
     * 绑定Trf
     *
     * @param reqObject
     * @return
     */
    public CustomResult bindTrf(BindTrfReq reqObject) {
        CustomResult rspResult = new CustomResult();
        List<String> trfNos = reqObject.getTrfNos();
        if (trfNos == null || trfNos.isEmpty()) {
            return rspResult.fail("TrfNos 不能为空.");
        }
        Map<String, TrfOrderPO> bindTrfMaps = Maps.newHashMap();
        trfNos.forEach(trfNo -> {
            TrfOrderPO bindTrf = new TrfOrderPO();
            bindTrf.setOrderId(reqObject.getOrderId());
            bindTrf.setOrderNo(reqObject.getOrderNo());
            bindTrf.setTrfNo(trfNo);
            bindTrf.setBoundStatus(BoundStatus.BoundHasOrder.getType());
            bindTrf.setRefSystemId(reqObject.getRefSystemId());
            bindTrf.setCreatedDate(DateUtils.getNow());
            bindTrf.setCreatedBy(reqObject.getUserName());
            bindTrf.setModifiedDate(DateUtils.getNow());
            bindTrf.setModifiedBy(reqObject.getUserName());

            bindTrfMaps.put(trfNo.toUpperCase(), bindTrf);
        });
        List<String> bindTrfNos = Lists.newArrayList();
        // 查看是否已绑定Trf
        List<TrfOrderPO> bindTrfs = boundTrfRelExtMapper.getBoundTrfInfoList(trfNos, BoundStatus.BoundHasOrder.getType());
        for (TrfOrderPO bindTrf : bindTrfs) {
            // 如果订单Id一样，说明是自己绑定过的订单
            if (StringUtils.equalsIgnoreCase(bindTrf.getOrderId(), reqObject.getOrderId())) {
                // 是否已绑定的TrfNo
                bindTrfMaps.remove(bindTrf.getTrfNo().toUpperCase());
                continue;
            }
            bindTrfNos.add(bindTrf.getTrfNo());
        }
        // TRF 绑定订单规则：0：一对一   1：多(order)对一(trfNO)
        if (!bindTrfNos.isEmpty() && NumberUtil.equals(reqObject.getBindTrfRule(), 0)) {
            return rspResult.fail(String.format("Trf No(%s)已被其他订单绑定.", StringUtils.join(bindTrfNos, ",")));
        }
        if (bindTrfMaps.isEmpty()) {
            rspResult.setSuccess(true);
            return rspResult;
        }
        List<CustomerTrfInfoRsp> trfs = trfInfoExtMapper.getTrfInfoList(trfNos, false, Lists.newArrayList());
        /*if (trfs.isEmpty()){
            return rspResult.fail(String.format("请检查TrfNo(%s)是否都已导入系统或是否已取消！", StringUtils.join(trfNos, ",")));
        }*/
        for (CustomerTrfInfoRsp trf : trfs) {
            if (TrfStatusEnum.check(trf.getTrfStatus(), TrfStatusEnum.Canceled)) {
                return rspResult.fail(String.format("当前TrfNo(%s)已被Canceled！", trf.getTrfNo()));
            }
            if (!bindTrfMaps.containsKey(trf.getTrfNo().toUpperCase())) {
                continue;
            }
            if (!StringUtils.equalsIgnoreCase(trf.getLabCode(), reqObject.getLabCode())) {
                return rspResult.fail(String.format("Trf No(%s)非当前用户Lab，无法绑定.", trf.getTrfNo()));
            }
        }
        logger.info("trfNo:{}需要在tb_trf_todo_info中删除", trfNos);
        // 加号绑定需要删除基础表中的数据
        trfTodoInfoExtMapper.deleteTrfTodoInfo(reqObject.getRefSystemId(), trfNos);

        TrfInfoExample trfInfoExample = new TrfInfoExample();
        trfInfoExample.createCriteria().andRefSystemIdEqualTo(reqObject.getRefSystemId()).andTrfNoIn(trfNos).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
        List<TrfInfoPO> trfInfoPOS = trfInfoMapper.selectByExample(trfInfoExample);
        if (Func.isNotEmpty(trfInfoPOS)) {
            Map<String, Long> trfMap = trfInfoPOS.stream().collect(Collectors.toMap(TrfInfoPO::getTrfNo, TrfInfoPO::getId));
            bindTrfMaps.forEach(
                    (k, v) -> v.setTrfId(Func.isNotEmpty(trfMap.get(v.getTrfNo())) ? trfMap.get(v.getTrfNo()) : null)
            );
        }
        rspResult.setSuccess(boundTrfRelExtMapper.batchInsert(Lists.newArrayList(bindTrfMaps.values())) > 0);

        // 除SGSMart外，其他客户需要自行维护TRF状态
        if (!Objects.equals(RefSystemIdEnum.SGSMart.getRefSystemId(),reqObject.getRefSystemId())) {
            TrfInfoExample example1 = new TrfInfoExample();
            example1.createCriteria().andTrfNoIn(reqObject.getTrfNos()).andRefSystemIdEqualTo(reqObject.getRefSystemId());
            TrfInfoPO target1 = new TrfInfoPO();
            target1.setStatus(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.ToOrder.getStatus());
            trfInfoMapper.updateByExampleSelective(target1,example1);

            CustomerTrfInfoExample example2 = new CustomerTrfInfoExample();
            example2.createCriteria().andTrfNoIn(reqObject.getTrfNos()).andRefSystemIdEqualTo(reqObject.getRefSystemId());
            CustomerTrfInfoPO target2 = new CustomerTrfInfoPO();
            target2.setTrfStatus(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.ToOrder.getStatus());
            customerTrfInfoMapper.updateByExampleSelective(target2, example2);
        }

        return rspResult;
    }

    /**
     * 解绑Trf
     *
     * @param reqObject
     * @return
     */
    public CustomResult unBindTrf(UnBindTrfReq reqObject) {
        CustomResult rspResult = new CustomResult();

        List<TrfOrderPO> boundTrfs = boundTrfRelExtMapper.getBoundTrfInfoByOrderId(reqObject.getOrderId(), reqObject.getTrfNos());
        if (boundTrfs == null || boundTrfs.isEmpty()) {
            rspResult.setSuccess(true);
            return rspResult;
        }
        List<TrfOrderPO> unBindTrfs = Lists.newArrayList();
        for (TrfOrderPO unBindTrf : boundTrfs) {
            if (BoundStatus.check(unBindTrf.getBoundStatus(), BoundStatus.UnBound)) {
                continue;
            }
            unBindTrf.setBoundStatus(BoundStatus.UnBound.getType());
            unBindTrf.setModifiedDate(DateUtils.getNow());
            unBindTrf.setModifiedBy(reqObject.getUserName());

            unBindTrfs.add(unBindTrf);
        }
        if (unBindTrfs.isEmpty()) {
            rspResult.setSuccess(true);
            return rspResult;
        }
        rspResult.setSuccess(boundTrfRelExtMapper.batchUnBindTrf(unBindTrfs) > 0);

        // 除SGSMart外，其他客户需要自行维护TRF状态
        if (!Objects.equals(RefSystemIdEnum.SGSMart.getRefSystemId(),reqObject.getRefSystemId())) {
            TrfInfoExample example1 = new TrfInfoExample();
            example1.createCriteria().andTrfNoIn(reqObject.getTrfNos()).andRefSystemIdEqualTo(reqObject.getRefSystemId());
            TrfInfoPO target1 = new TrfInfoPO();
            target1.setStatus(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.New.getStatus());
            trfInfoMapper.updateByExampleSelective(target1,example1);

            CustomerTrfInfoExample example2 = new CustomerTrfInfoExample();
            example2.createCriteria().andTrfNoIn(reqObject.getTrfNos()).andRefSystemIdEqualTo(reqObject.getRefSystemId());
            CustomerTrfInfoPO target2 = new CustomerTrfInfoPO();
            target2.setTrfStatus(com.sgs.customerbiz.model.trf.enums.TrfStatusEnum.New.getStatus());
            customerTrfInfoMapper.updateByExampleSelective(target2, example2);
        }
        // 将report置为无效
//        trfReportMapper.updateReportAsInvalid(reqObject.getTrfNos(), reqObject.getRefSystemId());
        return rspResult;
    }

    /**
     * POSL-4667 希音订单urgency字段带入到Order中Service Type
     * GPO 调用这个接口，提供check是否已经绑定order
     *
     * @param reqObject
     * @return
     */
    public CustomResult<CheckTrfInfoRsp> checkTrfInfo(CheckTrfInfoReq reqObject) {
        CustomResult<CheckTrfInfoRsp> rspResult = new CustomResult(true);
        if (Objects.equals(reqObject.getMode(), "REMOTE")) {
            return rspResult;
        }
        RefSystemIdEnum refSystem = RefSystemIdEnum.getRefSystemId(reqObject.getRefSystemId());
        if (refSystem == null) {
            return rspResult.fail("请检查参数RefSystemId");
        }
        int refSystemId = refSystem.getRefSystemId();
        // TrfNos PackageBarcodes 不能同时为空 或者同时有值
        if ((!CollectionUtils.isEmpty(reqObject.getTrfNo()) && !CollectionUtils.isEmpty(reqObject.getPackageBarcode())) ||
                (CollectionUtils.isEmpty(reqObject.getTrfNo()) && CollectionUtils.isEmpty(reqObject.getPackageBarcode()))) {
            return rspResult.fail("Please Check TrfNos PackageBarcodes!");
        }
        String productLineCode = reqObject.getProductLineCode();
        if (StringUtils.isBlank(productLineCode)) {
            return rspResult.fail("请检查参数ProductLineCode");
        }

        CheckTrfInfoRsp checkTrfInfoRsp = new CheckTrfInfoRsp();

        SearchTrfInfoDTO searchTrfInfoDTO = new SearchTrfInfoDTO();
        searchTrfInfoDTO.setHasOrder(false);
        searchTrfInfoDTO.setTrfNos(reqObject.getTrfNo());
        searchTrfInfoDTO.setPackageBarcodes(reqObject.getPackageBarcode());
        searchTrfInfoDTO.setRefSystemId(refSystemId);
        List<BoundTrfRelDTO> boundTrfRelDTOS = boundTrfRelExtMapper.queryBoundTrfInfo(searchTrfInfoDTO);

        // 无数据 返回   全部未导入
        if (CollectionUtils.isEmpty(boundTrfRelDTOS)) {
            checkTrfInfoRsp.setCheckStatus(CheckRspStatus.ErrorStatus.getType());
            String msg = CollectionUtils.isEmpty(reqObject.getTrfNo()) ?
                    String.format("PackageBarcodes[%s] does not exist, please import first;", StringUtils.join(reqObject.getPackageBarcode(), ","))
                    : String.format("TRF[%s] does not exist, please import first;", StringUtils.join(reqObject.getTrfNo(), ","));
            checkTrfInfoRsp.setCheckMsg(msg);
            rspResult.setData(checkTrfInfoRsp);
            return rspResult;
        }

        // HL 希音供应商的check 会提供是否已经建立enquiry关系，是否已经绑定order，和下方的check 不能复用
        if (StringUtils.equalsIgnoreCase(productLineCode, ProductLineTypeEnum.HL.getProductLineAbbr())) {
            //如果给了orderId ，只需要check 是不是boundstatus=1，判断依据是 当前orderId 处于绑定状态就行
            if (StringUtils.isNotBlank(reqObject.getOrderId())) {
                BoundTrfRelDTO hasBound = boundTrfRelDTOS.stream().filter(b -> BoundStatus.check(b.getBoundStatus(), BoundStatus.BoundHasOrder))
                        .filter(b -> StringUtils.equalsIgnoreCase(b.getOrderId(), reqObject.getOrderId())).findFirst().orElse(null);
                if (hasBound == null) {
                    return rspResult.fail(String.format("OrderId:{%s} not match Trf No:{%s}", reqObject.getOrderId(), reqObject.getTrfNo()));
                }
            } else {
                //没给orderId 需要check 当前trf是否绑定了其他订单
                //和 SL 的check 同步，下面的逻辑会处理
            }

        }

        List<String> errMsgs = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(reqObject.getTrfNo())) {
            Set<String> trfRels = boundTrfRelDTOS.stream().map(BoundTrfRelDTO::getTrfNo).collect(Collectors.toSet());
            Set<String> noImportTrfNos = reqObject.getTrfNo().stream().filter(item -> !trfRels.contains(item)).collect(Collectors.toSet());

            Set<String> hasBoundTrfNo = boundTrfRelDTOS.stream().filter(item -> BoundStatus.check(item.getBoundStatus(), BoundStatus.BoundHasOrder)).map(BoundTrfRelDTO::getTrfNo).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(noImportTrfNos)) {
                errMsgs.add(String.format("TRF[%s] does not exist, please import first", StringUtils.join(noImportTrfNos, ",")));
            }
            if (!CollectionUtils.isEmpty(hasBoundTrfNo)) {
                // 非1vN模式才校验
                if (reqObject.getTrfNo().size() == 1) {
                    TrfInfoExample example = new TrfInfoExample();
                    example.createCriteria().andTrfNoEqualTo(reqObject.getTrfNo().get(0)).
                            andRefSystemIdEqualTo(refSystemId).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
                    List<TrfInfoPO> trfInfoPOList = trfInfoMapper.selectByExample(example);
                    if (CollUtil.isNotEmpty(trfInfoPOList)) {
                        TrfInfoPO trfInfoPO = CollUtil.get(trfInfoPOList, 0);
                        if (trfInfoPO.getIntegrationLevel() == null ||
                                !Objects.equals(Integer.valueOf(trfInfoPO.getIntegrationLevel()), TrfOrderRelationshipRuleEnum.ONE_VS_MORE.getRule())) {
                            errMsgs.add(String.format("TRF[%s] has been bound to the order and cannot be added;", StringUtils.join(hasBoundTrfNo, ",")));
                        }
                    }
                }
            }
        }
        if (!CollectionUtils.isEmpty(reqObject.getPackageBarcode())) {
            Set<String> packageBarcodes = boundTrfRelDTOS.stream().map(BoundTrfRelDTO::getObjectNo).collect(Collectors.toSet());
            Set<String> noImportPackageBarcodes = reqObject.getPackageBarcode().stream().filter(item -> !packageBarcodes.contains(item)).collect(Collectors.toSet());

            Set<String> hasBoundTrfNo = boundTrfRelDTOS.stream().filter(item -> BoundStatus.check(item.getBoundStatus(), BoundStatus.BoundHasOrder)).map(BoundTrfRelDTO::getTrfNo).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(noImportPackageBarcodes)) {
                errMsgs.add(String.format("PackageBarcodes[%s] does not exist, please import first", StringUtils.join(noImportPackageBarcodes, ",")));
            }
            if (!CollectionUtils.isEmpty(hasBoundTrfNo)) {
                errMsgs.add(String.format("PackageBarcodes[%s] has been bound to the order and cannot be added;", StringUtils.join(hasBoundTrfNo, ",")));
            }
        }

        List<String> trfNos = boundTrfRelDTOS.stream().map(BoundTrfRelDTO::getTrfNo).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(errMsgs)) {
            checkTrfInfoRsp.setCheckStatus(CheckRspStatus.ErrorStatus.getType());
            checkTrfInfoRsp.setCheckMsg(StringUtils.join(errMsgs, ";"));
            rspResult.setData(checkTrfInfoRsp);
            return rspResult;
        }
        switch (refSystem) {
            case Shein:
            case SheinSupplier:
                List<TrfContentDTO> trfs = trfInfoExtMapper.getTrfContentInfo(trfNos, BUSINESS_LICENSE_NAME);
                if (CollectionUtils.isEmpty(trfs)) {
                    checkTrfInfoRsp.setCheckStatus(CheckRspStatus.ErrorStatus.getType());
                    checkTrfInfoRsp.setCheckMsg("SHEIN订单:获取Trf信息(CreateType/urgency)失败，请检查数据!");
                    rspResult.setData(checkTrfInfoRsp);
                    return rspResult;
                }
                Set<Integer> createTypeSets = trfs.stream().map(TrfContentDTO::getCreateType).collect(Collectors.toSet());
                if (createTypeSets.contains(CreateType.CreateTypeSelf.getType()) && createTypeSets.contains(CreateType.CreateTypeOther.getType())) {
                    checkTrfInfoRsp.setCheckStatus(CheckRspStatus.ErrorStatus.getType());
                    checkTrfInfoRsp.setCheckMsg("SHEIN订单:不能同时选择自建和非自建TrfNo，请重新选择！");
                    rspResult.setData(checkTrfInfoRsp);
                    return rspResult;
                }

                Set<Integer> urgency = trfs.stream().map(TrfContentDTO::getUrgency).collect(Collectors.toSet());
                // 选中的trfNo 存在多个urgency，弹出提示
                if (urgency.size() > 1) {
                    checkTrfInfoRsp.setCheckStatus(CheckRspStatus.PromptStatus.getType());
                    checkTrfInfoRsp.setCheckMsg("SHEIN订单:勾选的TRF对应Service Type不一致，是否继续创建订单？");
                    rspResult.setData(checkTrfInfoRsp);
                    return rspResult;
                }

                if (RefSystemIdEnum.check(refSystem.getRefSystemId(), RefSystemIdEnum.SheinSupplier)) {
                    TrfContentDTO trf = trfs.stream().findFirst().get();
                    CustomResult customResult = this.checkCustomer(trf != null ? trf.getKeyVal() : null);
                    checkTrfInfoRsp.setCheckStatus(CheckRspStatus.PromptStatus.getType());
                    checkTrfInfoRsp.setCheckMsg((customResult.isSuccess() ? "Notification：Applicant is in Boss" : "Notification：Applicant is not in Boss，Please add first"));
                    rspResult.setData(checkTrfInfoRsp);
                    return rspResult;
                }
                break;
        }
        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * 校验HL 希音供应商相关
     *
     * @param boundTrfRelDTOS
     * @param reqObject
     * @return
     */
    private CustomResult<CheckTrfInfoRsp> checkHLShinSupplier(List<BoundTrfRelDTO> boundTrfRelDTOS, CheckTrfInfoReq reqObject) {
        CustomResult<CheckTrfInfoRsp> rspResult = new CustomResult<>(true);
        CheckTrfInfoRsp checkTrfInfoRsp = new CheckTrfInfoRsp();
        //1 针对页面 是否可以再次点击create enquiry
        Integer checkCreateEnquiry = reqObject.getCheckCreateEnquiry();
        if (NumberUtil.equals(checkCreateEnquiry, 1)) {
            //只需要check当前是否有数据就行
            List<String> hasBoundOrderNo = boundTrfRelDTOS.stream().filter(b -> StringUtils.isNotBlank(b.getOrderNo())).map(b -> b.getOrderNo()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(hasBoundOrderNo)) {
                checkTrfInfoRsp.setCheckStatus(CheckRspStatus.ErrorStatus.getType());
                String msg = String.format("This trf already bound other orderNo: [ %s ]", StringUtils.join(hasBoundOrderNo, " , "));
                checkTrfInfoRsp.setCheckStatus(CheckRspStatus.ErrorStatus.getType());
                checkTrfInfoRsp.setCheckMsg(msg);
                rspResult.setData(checkTrfInfoRsp);
                return rspResult;
            }
        }
        //2 正常check 是否可以建单的check
        String orderId = reqObject.getOrderId();
        List<BoundTrfRelDTO> boundOrderNoList = boundTrfRelDTOS.stream().filter(b -> StringUtils.isNotBlank(b.getOrderNo()) && BoundStatus.check(b.getBoundStatus(), BoundStatus.BoundHasOrder)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(boundOrderNoList)) {
            return rspResult;
        }
        BoundTrfRelDTO relDTO = boundOrderNoList.stream().filter(b -> StringUtils.equalsIgnoreCase(b.getOrderId(), reqObject.getOrderId())).findFirst().orElse(null);
        String msg = "";
        if (relDTO != null) {
            String.format("This TrfNo already bound orderNo:[%s] 。", relDTO.getOrderNo());
        }
        List<String> boundOrderNos = boundOrderNoList.stream()
                .filter(b -> !StringUtils.equalsIgnoreCase(b.getOrderId(), reqObject.getOrderId()))
                .map(b -> b.getOrderNo()).collect(Collectors.toList());
        checkTrfInfoRsp.setCheckStatus(CheckRspStatus.ErrorStatus.getType());
        msg += String.format("This TrfNo already bound other orderNo:[%s]", StringUtils.join(boundOrderNos, ","));
        checkTrfInfoRsp.setCheckStatus(CheckRspStatus.ErrorStatus.getType());
        checkTrfInfoRsp.setCheckMsg(msg);
        rspResult.setData(checkTrfInfoRsp);
        rspResult.setSuccess(false);
        return rspResult;
    }


    /**
     * @param customerName
     * @return
     */
    private CustomResult checkCustomer(String customerName) {
        CustomResult rspResult = new CustomResult();
        if (StringUtils.isBlank(customerName)) {
            return rspResult;
        }
        CustomerInfoReq reqObject = new CustomerInfoReq();
        reqObject.setCustomerName(customerName);
        reqObject.setPage(1);
        reqObject.setRows(1);

        CustomerInfo customer = customerClient.getCustomerInfo(reqObject);
        rspResult.setSuccess(customer != null);
        return rspResult;
    }

    /**
     * 清洗数据(SGSMart)
     *
     * @param reqObject
     * @return
     */
    public CustomResult dataCleaning(TrfCleanReq reqObject) {
        CustomResult rspResult = new CustomResult();
        Integer refSystemId = reqObject.getRefSystemId();
        Integer cleanType = reqObject.getCleanType();
        if (reqObject.getRefSystemId() != 2) {
            return rspResult.fail("暂时只能清洗SGSMart数据");
        }
        Integer lastId = 1000000;
        List<TRFToDoListDTO> trfTodoList = Lists.newArrayList();

        do {

            trfTodoList = preOrderClient.getTrfTodoList(refSystemId, cleanType, lastId, reqObject.getTrfNo());

            if (trfTodoList == null || trfTodoList.isEmpty()) {
                break;
            }
            lastId = trfTodoList.get(trfTodoList.size() - 1).getID().intValue();

            logger.info("清洗SGSMart历史数据：cleanType：{}，lastId：{}", reqObject.getCleanType(), lastId);

            if (cleanType == 1) {
                //直接插入模糊搜索表
                List<TrfTodoInfoPO> trfs = Lists.newArrayList();
                for (TRFToDoListDTO trfTodoDTO : trfTodoList) {
                    if (StringUtils.isBlank(trfTodoDTO.getTrfNo())) {
                        continue;
                    }
                    TrfTodoInfoPO trfTodoInfoPO = new TrfTodoInfoPO();
                    trfTodoInfoPO.setTrfNo(trfTodoDTO.getTrfNo());
                    trfTodoInfoPO.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                    trfTodoInfoPO.setRefSystemId(refSystemId);
                    trfTodoInfoPO.setLabCode(trfTodoDTO.getLabCode());
                    trfTodoInfoPO.setContent(null);
                    trfTodoInfoPO.setTrfStatus(TrfStatusEnum.ToBeBound.getStatus());
                    trfTodoInfoPO.setCreatedBy(USER_DEFAULT);
                    trfTodoInfoPO.setCreatedDate(DateUtils.getNow());
                    trfTodoInfoPO.setModifiedBy(USER_DEFAULT);
                    trfTodoInfoPO.setModifiedDate(DateUtils.getNow());
                    trfs.add(trfTodoInfoPO);
                }
                if (!CollectionUtils.isEmpty(trfs)) {
                    trfTodoInfoExtMapper.batchInsert(trfs);
                }
                continue;
            }

            AsyncUtils.awaitResult(trfTodoList, trfItem -> {
                this.insertTrf(refSystemId, Lists.newArrayList(trfItem));
                return Lists.newArrayList();
            }, 10);

        } while (true);
//        if (reqObject.getCleanType() == 1) {  // 清洗 tb_trf_todo_list 中的数据
////            data = preOrderClient.getTrfBaseInfo(refSystemId, cleanType, 0, "");
//            //1. 获取所有未开单的trf数据（只需要10个lab的）
//            trfTodoList = preOrderClient.getTrfTodoList(refSystemId, cleanType, 0, "");
//
////            this.insertTrf(refSystemId, data);
//            logger.info("清洗tb_trf_base_info完成");
//        }
//        else {
//
//            do {
//                List<TRFToDoListDTO> trfBaseInfo = preOrderClient.getTrfTodoList(refSystemId, cleanType, lastId, reqObject.getTrfNo());
//                if (trfBaseInfo == null || trfBaseInfo.isEmpty()) {
//                    break;
//                }
//                lastId = trfBaseInfo.get(trfBaseInfo.size() - 1).getID().intValue();
//
//                AsyncUtils.awaitResult(trfBaseInfo, trfItem -> {
//                    this.insertTrf(refSystemId, Lists.newArrayList(trfItem));
//                    return Lists.newArrayList();
//                }, 10);
//
//            } while (true);
//            logger.info("清洗tb_order_trf_relationship完成");
//        }

//        List<TrfBaseInfoDTO> data = preOrderClient.getTrfBaseInfo();
//        if (CollectionUtils.isEmpty(data)) {
//            rspResult.setSuccess(false);
//            rspResult.setMsg("get Trf Base Info Fail!");
//            return rspResult;
//        }

        rspResult.setSuccess(true);
        return rspResult;
    }

    /**
     * 清洗数据
     *
     * @param refSystemId
     * @param trfBaseInfo
     * @return
     */
    private void insertTrf(Integer refSystemId, List<TRFToDoListDTO> trfBaseInfo) {
//        CustomerConfigRsp customerConfig = customerConfigClient.getCustomerConfig(refSystemId, "SL");
//        if (customerConfig == null) {
//            logger.error("Get Customer() Config Fail!");
//        }
//        String dffFormGroupId = customerConfig.getDffFormGroupId();
//        String gridFormGroupId = customerConfig.getGridFormGroupId();

        List<CustomerTrfInfoPO> trfs = Lists.newArrayList();
        List<TrfOrderPO> boundTrfRels = Lists.newArrayList();
        for (TRFToDoListDTO trfBaseInfoDTO : trfBaseInfo) {
            String trfNo = trfBaseInfoDTO.getTrfNo();
            if (StringUtils.isBlank(trfNo)) {
                logger.info("trfNo:{}为空，id:{}", trfNo, trfBaseInfoDTO.getID());
                continue;
            }
            SyncTrfInfoReq reqObject = new SyncTrfInfoReq();
            reqObject.setRefSystemId(refSystemId);
            reqObject.setTrfNo(String.format("%s?isAll=true", trfNo));
            reqObject.setAction("GetSGSMartTrfInfo");
            //reqObject.setCustomerGroupCode(RefSystemIdEnum.getRefSystemId(refSystemId).getCustomerGroupCode());
            reqObject.setProductLineCode(ProductLineContextHolder.getProductLineCode());
//            reqObject.setDffFormGroupId(dffFormGroupId);
//            reqObject.setGridFormGroupId(gridFormGroupId);

            BaseResponse response = todoListClient.syncGetInfo(reqObject);
            if (response != null && response.isSuccess() && !ObjectUtils.isEmpty(response.getData())) {
                String toJSONString = JSONObject.toJSONString(response.getData());
                JSONObject jsonObject = JSONObject.parseObject(toJSONString);
                JSONObject detail = (JSONObject) jsonObject.get(DETAIL);
                if (detail == null) {
                    logger.info("trfNo:{}获取详细信息时，detail节点获取失败", trfNo);
                    continue;
                }
//                String dffFormId = (String) jsonObject.get(DFF_FORM_ID);
//                String gridFormId = (String) jsonObject.get(GRID_FORM_ID);

                // 判断是否已清洗
                SearchTrfInfoDTO searchTrfInfoDTO = new SearchTrfInfoDTO();
                searchTrfInfoDTO.setHasOrder(false);
                searchTrfInfoDTO.setTrfNos(Lists.newArrayList(trfNo));
                searchTrfInfoDTO.setRefSystemId(refSystemId);
                List<BoundTrfRelDTO> boundTrfRelDTOS = boundTrfRelExtMapper.queryBoundTrfInfo(searchTrfInfoDTO);
                if (CollectionUtils.isEmpty(boundTrfRelDTOS)) {
                    // 需要添加tb_customer_trf_info 数据
                    CustomerTrfInfoPO trf = new CustomerTrfInfoPO();
                    trf.setRefSystemId(trfBaseInfoDTO.getRefSystemId());
                    trf.setTrfNo(trfNo);
                    trf.setProductLineCode(ProductLineContextHolder.getProductLineCode());
                    trf.setLabCode(trfBaseInfoDTO.getLabCode());
                    trf.setObjectNo(trfNo);
                    trf.setBatchNo(null);
                    trf.setContent(detail != null ? detail.toJSONString() : null);
//                    trf.setDffFormId(dffFormId);
//                    trf.setGridFormId(gridFormId);
                    // TODO
//                    Integer orderStatus = trfBaseInfoDTO.getOrderStatus();

                    OrderStatusEnum orderStatus = OrderStatusEnum.getOrderStatus(trfBaseInfoDTO.getOrderStatus());
                    switch (orderStatus) {
                        case New:
                            trf.setTrfStatus(TrfStatusEnum.Invoiced.getStatus());
                            break;
                        case Confirmed:
                            trf.setTrfStatus(TrfStatusEnum.ToBeTested.getStatus());
                            break;
                        case Testing:
                        case Reporting:
                            trf.setTrfStatus(TrfStatusEnum.Testing.getStatus());
                            break;
                        case Completed:
                        case Closed:
                            trf.setTrfStatus(TrfStatusEnum.Completed.getStatus());
                            break;
                        case Cancelled:
                            trf.setTrfStatus(TrfStatusEnum.Canceled.getStatus());
                            break;
                    }

                    trf.setActiveIndicator(1);
                    trf.setCreatedBy(trfBaseInfoDTO.getCreatedBy());
                    trf.setCreatedDate(trfBaseInfoDTO.getCreatedDate());
                    trf.setModifiedBy(trfBaseInfoDTO.getModifiedBy());
                    trf.setModifiedDate(trfBaseInfoDTO.getModifiedDate());
//                trf.setLastModifiedTimestamp(trfBaseInfoDTO.getLastModifiedTimestamp());
                    // 由于业务需要，需要将 希音返回的数据 做处理后再存库
                    todoListService.appendContentField(trf);

                    trfs.add(trf);
                }

                // tb_customer_trf_info 表已存在 判断 是否已绑定

                // 判断是否需要添加 tb_bound_trf_relationship 数据
                if (StringUtils.isNotBlank(trfBaseInfoDTO.getOrderNo())) {
                    List<String> boundOrderNo = Lists.newArrayList();
                    if (!CollectionUtils.isEmpty(boundTrfRelDTOS)) {
                        boundOrderNo = boundTrfRelDTOS.stream()
                                .filter(item -> !StringUtils.isBlank(item.getOrderNo()))
                                .map(BoundTrfRelDTO::getOrderNo).collect(Collectors.toList());
                    }
                    if (boundOrderNo.contains(trfBaseInfoDTO.getOrderNo())) {
                        continue;
                    }

                    TrfOrderPO boundTrfInfo = new TrfOrderPO();
                    boundTrfInfo.setTrfNo(trfNo);
                    boundTrfInfo.setOrderNo(trfBaseInfoDTO.getOrderNo());
                    boundTrfInfo.setOrderId(trfBaseInfoDTO.getOrderId());
                    boundTrfInfo.setRefSystemId(refSystemId);
                    boundTrfInfo.setBoundStatus(BoundStatus.BoundHasOrder.getType());
                    boundTrfInfo.setCreatedDate(trfBaseInfoDTO.getCreatedDate());
                    boundTrfInfo.setCreatedBy(trfBaseInfoDTO.getCreatedBy());
                    boundTrfInfo.setModifiedDate(trfBaseInfoDTO.getModifiedDate());
                    boundTrfInfo.setModifiedBy(trfBaseInfoDTO.getModifiedBy());

                    boundTrfRels.add(boundTrfInfo);
                }

            } else {
                logger.info("清洗数据时，获取trfNo[{}]详细信息失败", trfNo);
            }
        }
        if (!CollectionUtils.isEmpty(trfs)) {
            trfInfoExtMapper.batchInsert(trfs);
        }
        if (!CollectionUtils.isEmpty(boundTrfRels)) {
            boundTrfRelExtMapper.batchInsert(boundTrfRels);
        }

    }

    public CustomResult getTrfStatus(Integer type) {
        CustomResult rspResult = new CustomResult();
        List<CodeValueRsp> list = Lists.newArrayList();
        int index = 0;
        for (TrfStatusEnum statusEnum : TrfStatusEnum.values()) {
            index++;
            if (NumberUtil.toInt(type) == 1 && index < 3) {
                continue;
            }
            CodeValueRsp rsp = new CodeValueRsp();
            rsp.setValue(String.valueOf(statusEnum.getStatus()));
            rsp.setText(statusEnum.getText());
            list.add(rsp);
        }
        rspResult.setData(list);
        rspResult.setSuccess(true);
        return rspResult;
    }


    public JSONObject getTrfContent(String trfNo, Integer refSystemId) {
        CustomerTrfInfoRsp trfInfo = trfInfoExtMapper.getTrfInfo(refSystemId, trfNo);
        if (Func.isNotEmpty(trfInfo)) {
            String content = trfInfo.getContent();
            if (Func.isNotBlank(content)) {
                return JSONObject.parseObject(content);
            }
        }
        return null;
    }
}
