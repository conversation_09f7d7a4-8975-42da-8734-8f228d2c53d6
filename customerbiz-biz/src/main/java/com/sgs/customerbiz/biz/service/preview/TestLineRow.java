package com.sgs.customerbiz.biz.service.preview;

import com.sgs.customerbiz.model.trf.dto.TrfTestLineDTO;
import com.sgs.customerbiz.model.trf.dto.resp.TestLineMappingInfoV2DTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TestLineRow {

    private String 

    private CustomerTestLineJSONObject customerTestLine;

    private TestLineMappingInfoV2DTO mappingInfo;

    private TrfTestLineDTO sgsTestLine;

    public static TestLineRow match(CustomerTestLine customerTestLine, SgsTestLine sgsTestLine) {
        return new TestLineRow(customerTestLine.getTestLine(), customerTestLine.getMapping(), sgsTestLine.getTestLine());
    }

    public static TestLineRow onlyCustomer(CustomerTestLine customerTestLine) {
        return new TestLineRow(customerTestLine.getTestLine(), customerTestLine.getMapping(), null);
    }

    public static TestLineRow onlySgs(SgsTestLine sgsTestLine) {
        return new TestLineRow(null, sgsTestLine.getMapping(), sgsTestLine.getTestLine());
    }
}
