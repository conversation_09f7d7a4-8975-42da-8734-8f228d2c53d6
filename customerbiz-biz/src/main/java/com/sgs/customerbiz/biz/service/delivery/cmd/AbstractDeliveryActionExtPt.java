package com.sgs.customerbiz.biz.service.delivery.cmd;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.biz.convertor.TrfSyncConvertor;
import com.sgs.customerbiz.biz.service.delivery.DeliveryActionExtPt;
import com.sgs.customerbiz.biz.service.ruleengine.RuleExecutor;
import com.sgs.customerbiz.biz.service.synctrf.SyncActionExtPt;
import com.sgs.customerbiz.biz.service.synctrf.SyncTrfContextHolder;
import com.sgs.customerbiz.core.config.ProductLineContextHolder;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.TrfOrderMapper;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfInfoPO;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderPO;
import com.sgs.customerbiz.dfv.enums.ActiveIndicatorEnum;
import com.sgs.customerbiz.domain.domainevent.*;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.TrfTestResultDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.*;
import com.sgs.customerbiz.domain.domainservice.*;
import com.sgs.customerbiz.domain.dto.TrfActionStatusRule;
import com.sgs.customerbiz.domain.dto.TrfStatusControlConfig;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfLabDTO;
import com.sgs.customerbiz.model.trf.dto.TrfOrderDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.dto.resp.TrfSyncResult;
import com.sgs.customerbiz.model.trf.enums.PendingFlagEnum;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.Assert;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractDeliveryActionExtPt implements DeliveryActionExtPt {

    @Autowired
    private TrfDomainService trfDomainService;

    @Autowired
    private TrfOrderMapper trfOrderMapper;

    public abstract Object doDeliveryControl(TrfFullDTO trfFullDTO);

    @Override
    public Object delivery(TrfFullDTO trfFullDTO) {
        TrfInfoPO trfInfoPO = trfDomainService.getTrfInfoByTrfNo(trfFullDTO.getTrfList().get(0).getTrfNo(), trfFullDTO.getTrfList().get(0).getRefSystemId());
        if (Func.isEmpty(trfInfoPO)) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.DELIVERYACTION, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.REQUESTNULL);
            throw new CustomerBizException(errorCode,ResponseCode.FAIL.getCode(), StrUtil.format("Trf no {} is not exist, unable to use SyncQuotationToTrf", trfFullDTO.getTrfList().get(0).getTrfNo()));
            //throw new BizException(StrUtil.format("Trf no {} is not exist, unable to use SyncQuotationToTrf", trfFullDTO.getTrfList().get(0).getTrfNo()));
        }

        if (TrfStatusEnum.check(trfInfoPO.getStatus(), TrfStatusEnum.New, TrfStatusEnum.Cancelled, TrfStatusEnum.Closed)) {
            ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.DELIVERYACTION, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.STATUSERROR);
            //throw new BizException(StrUtil.format("Trf {} status is {} status can not be new/cancelled/closed，unable to use SyncQuotationToTrf", trfInfoPO.getTrfNo(), TrfStatusEnum.getText(trfInfoPO.getStatus())));
            throw new CustomerBizException(errorCode,ResponseCode.FAIL.getCode(), StrUtil.format("Trf {} status is {} status can not be new/cancelled/closed，unable to use SyncQuotationToTrf", trfInfoPO.getTrfNo(), TrfStatusEnum.getText(trfInfoPO.getStatus())));
        }

        List<TrfOrderDTO> orderList = trfFullDTO.getOrderList();
        if (Func.isNotEmpty(orderList)) {
            List<String> collect = orderList.stream().map(TrfOrderDTO::getOrderNo).collect(Collectors.toList());
            TrfOrderExample trfOrderExample = new TrfOrderExample();
            trfOrderExample.createCriteria().andOrderNoIn(collect).andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
            List<TrfOrderPO> trfOrderPOS = trfOrderMapper.selectByExample(trfOrderExample);
            if (Func.isEmpty(trfOrderPOS) || trfOrderPOS.size() != orderList.size()) {
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.DELIVERYACTION, ErrorFunctionTypeEnum.VALIDATION, ErrorTypeEnum.DATANOTMATCH);
                throw new CustomerBizException(errorCode,ResponseCode.FAIL.getCode(), StrUtil.format("Trf no {} and order no {} has no relationship，unable to use SyncQuotationToTrf", trfInfoPO.getTrfNo(), orderList.get(0).getOrderNo()));
                //throw new BizException(StrUtil.format("Trf no {} and order no {} has no relationship，unable to use SyncQuotationToTrf", trfInfoPO.getTrfNo(), orderList.get(0).getOrderNo()));
            }
        }

        TrfStatusControlConfig controlConfig = trfDomainService.getTrfStatusControlConfig(trfInfoPO.getRefSystemId(), trfInfoPO.getBuCode());
        Map<String, TrfActionStatusRule> actionStatusMapping = controlConfig.getActionStatusMapping();
        TrfActionStatusRule actionStatusRule = actionStatusMapping.get(trfFullDTO.getAction());
        Assert.notNull(actionStatusRule, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), StrUtil.format("The {} action not allowed",trfFullDTO.getAction()));
        Map<Integer, Integer> ctrlMapping = actionStatusRule.getCtrlMapping();
        Integer whiteItem = ctrlMapping.get(trfInfoPO.getStatus());
        Assert.notNull(whiteItem, ResponseCode.INTERNAL_SERVER_ERROR.getCode(), StrUtil.format("The {} action not allowed",trfFullDTO.getAction()));
        trfFullDTO.getTrfList().get(0).setTrfId(trfInfoPO.getId());
        return doDeliveryControl(trfFullDTO);
    }
}
