package com.sgs.customerbiz.biz.convert.impl.fn;

import com.sgs.customerbiz.biz.convert.impl.fn.base.DataConvertFn;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Desc
 * <AUTHOR>
 * @date 2024/2/27 16:47
 */
@Slf4j
@Component
public class DefaultValueFn implements DataConvertFn {


    @Override
    public Object invoke(Object[] args) {
        if (Func.isEmpty(args) || args.length != 3) {
            return null;
        }
        return Func.isNotEmpty(args[0]) ? args[1] : args[2];
    }

    @Override
    public String getName() {
        return "defaultValue";
    }

    @Override
    public String desc() {
        return "defaultValue";
    }
}
