package com.sgs.customerbiz.biz.utils;

import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.script.AviatorBindings;
import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.customerbiz.biz.service.datacollector.CollectedData;
import com.sgs.customerbiz.core.util.ConditionUtils;
import com.sgs.framework.tool.utils.Func;

import javax.script.Bindings;
import java.util.Map;

public class SubscriberConditionUtils {

    public static boolean subscriberConditionEval(EventSubscribeDTO subscriber, CollectedData collectedData) {
        String condition = subscriber.getCondition();
        String conditionParams = subscriber.getConditionParams();
        if (Func.isBlank(condition) || Func.isBlank(conditionParams)) {
            return true;
        }
        Map map = ConditionUtils.extractParamValue(JSONObject.parseObject(conditionParams, Map.class), collectedData);
        Bindings bindings = new AviatorBindings();
        bindings.putAll(map);
        return ConditionUtils.conditionEval(condition, bindings);
    }

}
