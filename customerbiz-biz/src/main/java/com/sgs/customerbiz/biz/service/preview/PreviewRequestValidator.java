package com.sgs.customerbiz.biz.service.preview;

import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TrfInfoExtMapper;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.TrfSyncHeaderDTO;
import com.sgs.customerbiz.model.trf.dto.req.TrfSyncReq;
import com.sgs.customerbiz.model.trf.dto.resp.CustomerGeneralConfig;
import io.vavr.API;
import io.vavr.collection.Seq;
import io.vavr.control.Option;
import io.vavr.control.Validation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PreviewRequestValidator {

    private final ConfigClient configClient;

    private final TrfInfoExtMapper trfInfoExtMapper;

    public PreviewRequestValidator(ConfigClient configClient, TrfInfoExtMapper trfInfoExtMapper) {
        this.configClient = configClient;
        this.trfInfoExtMapper = trfInfoExtMapper;
    }

//    public PreviewCtx validate(TrfSyncReq request) {
//
//        Validation.combine(validateRefSystemId(request),validateProductLineCode(request), validateTrfNoList(request))
//                .ap((refSystemId, productLineCode, trfNoList) -> {
//                    Map<String, CustomerTrfInfoRsp> customerTrfInfoMap = trfNoList.stream()
//                            .map(trf -> trfInfoExtMapper.getTrfInfo(refSystemId, trf))
//                            .collect(Collectors.toMap(CustomerTrfInfoRsp::getTrfNo, Function.identity(), (v1, v2) -> v1));
//
//                    return null;
//                })
//
//    }

    private static Validation<String, TrfSyncReq> validateRequest(TrfSyncReq request) {

        return Validation.combine(validateRefSystemId(request), validateProductLineCode(request), validateTrfNoList(request))
                .ap((refSystemId, productLineCode, trfNoList) -> request)
                .mapError(error -> error.mkString(";"));
    }


    private static Validation<String, String> validateProductLineCode(TrfSyncReq request) {
        return Option.of(request.getBuCode()).filter(StringUtils::isNotBlank).toValidation("productLineCode of request is required!");
    }

    private Validation<String, String> validateCustomerGroupCode(TrfSyncReq request) {
        return validateRefSystemId(request)
                .flatMap(id -> Option.ofOptional(configClient.getCustomerConfigBy(id))
                        .map(CustomerGeneralConfig::getCustomerGroupCode)
                        .toValidation("customerGroupCode Not Found From SCI by " + id));
    }

    private static Validation<String, List<String>> validateTrfNoList(TrfSyncReq request) {
        return Option.of(request).map(TrfSyncReq::getHeader)
                .filter(header -> CollectionUtils.isNotEmpty(header.getTrfList()))
                .map(TrfSyncHeaderDTO::getTrfList)
                .map(trfList -> trfList.stream().map(TrfHeaderDTO::getTrfNo).collect(Collectors.toList()))
                .filter(trfNoList -> trfNoList.stream().allMatch(StringUtils::isNotBlank)).toValidation("trfNo of request is required!");
    }

    private static Validation<String, Integer> validateRefSystemId(TrfSyncReq request) {
        return Option.of(request).map(TrfSyncReq::getHeader).map(TrfSyncHeaderDTO::getRefSystemId).toValidation("refSystemId of request is required!");
    }


}
