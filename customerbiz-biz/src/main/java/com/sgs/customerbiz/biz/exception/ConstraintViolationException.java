package com.sgs.customerbiz.biz.exception;

import com.sgs.customerbiz.validation.service.ValidationResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import lombok.Getter;

import java.util.List;

@Getter
public class ConstraintViolationException extends BizException {

    private final List<ValidationResult> validationResultList;

    public ConstraintViolationException(List<ValidationResult> validationResultList) {
        super(ResponseCode.ILLEGAL_ARGUMENT);
        this.validationResultList = validationResultList;
    }
}
