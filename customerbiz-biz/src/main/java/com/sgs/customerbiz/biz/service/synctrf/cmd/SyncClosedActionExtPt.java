package com.sgs.customerbiz.biz.service.synctrf.cmd;

import com.alibaba.cola.extension.Extension;
import com.sgs.customerbiz.biz.convertor.TrfConvertor;
import com.sgs.customerbiz.core.errorcode.ErrorCode;
import com.sgs.customerbiz.core.errorcode.ErrorCodeFactory;
import com.sgs.customerbiz.core.errorcode.enums.ErrorBizModelEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorCategoryEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorFunctionTypeEnum;
import com.sgs.customerbiz.core.errorcode.enums.ErrorTypeEnum;
import com.sgs.customerbiz.core.exception.CustomerBizException;
import com.sgs.customerbiz.dbstorages.mybatis.model.TrfOrderPO;
import com.sgs.customerbiz.domain.domainobject.TrfStatusResult;
import com.sgs.customerbiz.domain.domainobject.v2.TrfDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfHeaderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfOrderDOV2;
import com.sgs.customerbiz.domain.domainobject.v2.TrfStatusControlDO;
import com.sgs.customerbiz.domain.domainservice.TrfDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfInvoiceDomainService;
import com.sgs.customerbiz.domain.domainservice.TrfQuotationDomainService;
import com.sgs.customerbiz.domain.service.ReportDataService;
import com.sgs.customerbiz.model.trf.dto.TrfFullDTO;
import com.sgs.customerbiz.model.trf.dto.TrfHeaderDTO;
import com.sgs.customerbiz.model.trf.enums.PendingFlagEnum;
import com.sgs.customerbiz.model.trf.enums.TrfStatusEnum;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.tool.utils.Func;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Extension(scenario = "SyncClosed")
public class SyncClosedActionExtPt extends AbstractSyncActionExtPt {
    @Resource
    private ReportDataService reportDataService;

    @Resource
    private TrfInvoiceDomainService trfInvoiceDomainService;

    @Resource
    private TrfQuotationDomainService trfQuotationDomainService;

    @Resource
    private TrfDomainService trfDomainService;


    @Override
    public TrfStatusResult doStatusControl(TrfStatusControlDO trfStatusControlDO) {
        TrfOrderDOV2 order = trfStatusControlDO.getOrder();
        order.setOrderStatus(TrfStatusEnum.Closed.getStatus());
        String trfNo = trfStatusControlDO.getTrfNo();
        TrfOrderPO trfOrderPO = trfOrderDomainService.selectOrderInfoByParams(trfNo, order.getOrderNo(),order.getRealOrderNo(), trfStatusControlDO.getRefSystemId());
        if (Func.isNotEmpty(trfOrderPO)) {
            Integer pendingFlag = trfOrderPO.getPendingFlag();
            if (Objects.equals(pendingFlag, PendingFlagEnum.Pending.getType())) {
                ErrorCode errorCode = ErrorCodeFactory.createNewErrorCode(ErrorCategoryEnum.BUSINESS_ERROR, ErrorBizModelEnum.SYNCACTION, ErrorFunctionTypeEnum.STATUSCONTROL, ErrorTypeEnum.STATUSERROR);
                throw new CustomerBizException(errorCode, ResponseCode.FAIL.getCode(), "The Order：" + order.getOrderNo() + "，is Pending！Cannot SyncClosed！");
                //throw new BizException("The Order：" + order.getOrderNo() + "，is Pending！Cannot SyncClosed！");
            }
        }
        return trfDomainService.close(trfStatusControlDO);
    }

    @Override
    public void doStatusControlCustom(TrfFullDTO trfDOParam) {
        TrfDOV2 trfDOV2 = TrfConvertor.toTrfDOV2(trfDOParam);
        TrfHeaderDTO header = trfDOParam.getHeader();
        TrfHeaderDOV2 trfHeaderDOV2 = trfDomainService.selectSimple(header.getRefSystemId(), header.getTrfNo());
        if (Func.isNotEmpty(trfDOV2.getInvoiceList())) {
            trfInvoiceDomainService.batchSave(trfHeaderDOV2.getTrfId(), trfDOV2.getInvoiceList());
        }
        if (Func.isNotEmpty(trfDOV2.getQuotationList())) {
            trfQuotationDomainService.batchSave(header.getTrfNo(),header.getRefSystemId(),trfHeaderDOV2.getTrfId(), trfDOV2.getQuotationList());
        }
    }

    @Override
    public void syncSingleTrfPostprocess(TrfStatusResult trfStatusResult, TrfFullDTO trfDOParam) {
        reportDataService.saveQuotation(trfDOParam);
        reportDataService.saveInvoice(trfDOParam);
    }


}
