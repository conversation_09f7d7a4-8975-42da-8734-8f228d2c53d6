package com.sgs.customerbiz.biz.service.task.impl.message;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.google.common.collect.ImmutableSet;
import com.sgs.customerbiz.biz.utils.SortedUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
public class WalmartSorter implements ConvertedDataSorter {
    @Override
    public JSON sort(JSON convertedData) {
        Object objectNo = JSONPath.eval(convertedData, "$.objectNumber");
        Object customerNoObj = JSONPath.eval(convertedData, "$.customerNo");
        Object usNoObj = JSONPath.eval(convertedData, "$.usNo");
        Set<String> usNoSet = new HashSet<>();
        Optional.ofNullable(usNoObj)
                .map(Objects::toString)
                .map(nos -> nos.split(","))
                .map(arr -> Stream.of(arr).filter(StringUtils::isNotBlank).collect(Collectors.toSet()))
                .ifPresent(usNoSet::addAll);
        Boolean needContacts = Optional.ofNullable(customerNoObj).map(Object::toString).map(usNoSet::contains).orElse(false);
        log.info("objectNumber: {}, needContacts {}, buyer bossNo: {}, usNo: {}", objectNo,needContacts, customerNoObj, usNoObj);
        Object sample = JSONPath.eval(convertedData, "$.body.sample");
        JSONObject sampleNode = (JSONObject) sample;
        Map<String, Object> sortedSample = new LinkedHashMap<>();
        sortedSample.put("lab_number", Optional.ofNullable(sampleNode.getString("lab_number")).orElse(""));
        if(needContacts) {
            sortedSample.put("prior_testings", Optional.ofNullable(sampleNode.getJSONArray("prior_testings")).orElse(new JSONArray()));
        }
        sortedSample.put("program_type", Optional.ofNullable(sampleNode.getString("program_type")).orElse(""));
        sortedSample.put("style_number", Optional.ofNullable(sampleNode.getJSONArray("style_number")).orElse(new JSONArray()));
        sortedSample.put("item_number", Optional.ofNullable(sampleNode.getJSONArray("item_number")).orElse(new JSONArray()));
        sortedSample.put("upc_number", Optional.ofNullable(sampleNode.getJSONArray("upc_number")).orElse(new JSONArray()));
        sortedSample.put("item_description", Optional.ofNullable(sampleNode.getString("item_description")).orElse(""));
        SortedUtils.ifObjectPresent(sortedSample, sampleNode, "supplier", supplierNode -> {
            LinkedHashMap<String, Object> sortedResult = new LinkedHashMap<>();
            sortedResult.put("supplier_name", Optional.ofNullable(supplierNode.getString("supplier_name")).orElse(""));
            sortedResult.put("supplier_number", Optional.ofNullable(supplierNode.getString("supplier_number")).orElse(""));
            sortedResult.put("host_vendor_number", Optional.ofNullable(supplierNode.getString("host_vendor_number")).orElse(""));
            sortedResult.put("supplier_type", Optional.ofNullable(supplierNode.getString("supplier_type")).orElse(""));
            return sortedResult;
        });
        SortedUtils.ifObjectPresent(sortedSample, sampleNode, "factory", factoryNode -> {
            LinkedHashMap<String, Object> sortedResult = new LinkedHashMap<>();
            sortedResult.put("factory_name", Optional.ofNullable(factoryNode.getString("factory_name")).orElse(""));
            sortedResult.put("factory_number", Optional.ofNullable(factoryNode.getString("factory_number")).orElse(""));
            return sortedResult;
        });
        if(needContacts) {
            sortedSample.put("contacts", Optional.ofNullable(sampleNode.getJSONArray("contacts")).orElse(new JSONArray()));
        }
        sortedSample.put("rating", Optional.ofNullable(sampleNode.getString("rating")).orElse(""));
        sortedSample.put("logout_date", Optional.ofNullable(sampleNode.getString("logout_date")).orElse(""));
        sortedSample.put("retail_market", Optional.ofNullable(sampleNode.getString("retail_market")).orElse(""));
        sortedSample.put("department", Optional.ofNullable(sampleNode.getString("department")).orElse(""));
        sortedSample.put("brand", Optional.ofNullable(sampleNode.getString("brand")).orElse(""));
        sortedSample.put("purchase_order", Optional.ofNullable(sampleNode.getJSONArray("purchase_order")).orElse(new JSONArray()));
        sortedSample.put("country_of_origin", Optional.ofNullable(sampleNode.getJSONArray("country_of_origin")).orElse(new JSONArray()));
        sortedSample.put("sourcing_office", Optional.ofNullable(sampleNode.getString("sourcing_office")).orElse(""));
        sortedSample.put("children_product", Optional.ofNullable(sampleNode.getString("children_product")).orElse(""));
        sortedSample.put("material_change", Optional.ofNullable(sampleNode.getString("material_change")).orElse(""));
        sortedSample.put("gcc_cpc", Optional.ofNullable(sampleNode.getString("gcc_cpc")).orElse(""));
        sortedSample.put("fail_code", Optional.ofNullable(sampleNode.getJSONArray("fail_code")).orElse(new JSONArray()));
        sortedSample.put("retest_indicator", Optional.ofNullable(sampleNode.getString("retest_indicator")).orElse(""));
        JSONPath.set(convertedData, "$.body.sample", sortedSample);
        return convertedData;
    }

    @Override
    public SorterKey key() {
        return SorterKey.valueOf(47L);
    }
}
