package com.sgs.customerbiz.biz.service.notify.impl;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.sgs.config.api.dto.EventSubscribeDTO;
import com.sgs.config.api.dto.SystemApiDTO;
import com.sgs.config.api.service.EventSubscribeService;
import com.sgs.config.api.dto.SystemApiDTO;
import com.sgs.customerbiz.biz.dto.TaskInfoDTO;
import com.sgs.customerbiz.biz.enums.EventNotifyRuleEnum;
import com.sgs.customerbiz.biz.convert.DataConvertor;
import com.sgs.customerbiz.biz.service.datacollector.CollectedData;
import com.sgs.customerbiz.biz.service.datacollector.DataCollectService;
import com.sgs.customerbiz.biz.service.notify.DataNotifyService;
import com.sgs.customerbiz.biz.service.task.TaskService;
import com.sgs.customerbiz.biz.service.task.impl.handler.TaskFactory;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.TaskCondition;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.message.ILayerMessageTaskParameters;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.message.IlayerTransparentReq;
import com.sgs.customerbiz.biz.service.task.impl.handler.model.message.MessageTaskParameters;
import com.sgs.customerbiz.biz.service.task.impl.message.IgnoreAttrNameMap;
import com.sgs.customerbiz.dbstorages.mybatis.model.SystemApiPO;
import com.sgs.customerbiz.domain.domainevent.TrfEvent;
import com.sgs.customerbiz.domain.enums.TaskStatusEnum;
import com.sgs.customerbiz.domain.enums.TrfEventTriggerBy;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.config.impl.manager.SystemApiManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

import static com.sgs.customerbiz.biz.utils.TrfEventCodeMapping.*;

/**
 * Deprecated at 2023/7/14 shawn.yang
 *
 * @See com.com.sgs.customerbiz.biz.event.handler.MessageNotifyHandler
 * @author: shawn.yang
 * @create: 2023-05-10 22:34
 */
@Slf4j
@Service
@Deprecated
public class DataNotifyServiceImpl implements DataNotifyService {

    private final DataConvertor<String, String, JSON> jsonDataConvertor;
    private final TaskService taskService;
    private final SystemApiManager systemApiManager;
    private final EventSubscribeService eventSubscribeService;
    private final DataCollectService dataCollectService;

    //todo 临时在turkey--> shein项目适配ilayer的convert
    @Resource
    private DataConvertor<TrfEvent, String, String> ilayerDataConvertorAdaptor;

    @Override
    public void doNotify(TrfEvent trfEvent, EventSubscribeDTO eventSubscribe, Object extraData) {
        // 1.条件校验

        // 2.获取api配置
        //todo 待修正 PO问题
        SystemApiPO systemApiPO = systemApiManager.findByApiId(eventSubscribe.getApiId());
        if (systemApiPO == null) {
            log.error("systemId:{} ,api:{} not existed.", eventSubscribe.getSubscriber(), eventSubscribe.getApiId());
            throw new NullPointerException("api not existed.");
        }
        SystemApiDTO systemApi = new SystemApiDTO();
        BeanUtils.copyProperties(systemApiPO, systemApi);

        // 3.根据customer配置收集数据
        Integer notifyDataConf = eventSubscribe.getNotifyData();
        CollectedData collectedData = dataCollectService.collect(trfEvent.getRefSystemId(), trfEvent.getTrfNo(), notifyDataConf, extraData);

        // 4.data convert&mapping
        Object convertedData;
        //todo 临时方案：适配ILayer使用了ILayerDataConvertorAdaptor，长期方案JsonDataConvertor
        if (eventSubscribe.getSubscriber() == RefSystemIdEnum.Shein.getRefSystemId()) {
            convertedData = ilayerDataConvertorAdaptor.convert(trfEvent, String.valueOf(eventSubscribe.getEventCode()));
        } else {
            convertedData = jsonDataConvertor.convert(collectedData.toJSONString(), systemApi.getRequestBodyTemplate());
        }

        //todo 5.rule check for convertedData

        TaskInfoDTO messageTask;
        //todo 适配SHEIN:改成通用的message
        if (eventSubscribe.getSubscriber() == RefSystemIdEnum.Shein.getRefSystemId()) {
            // 6.build message
            ILayerMessageTaskParameters iLayerMessage = ILayerMessageTaskParameters.buildMessage(convertedData.toString(), systemApi, trfEvent);
            // 7.build Task
            messageTask = TaskFactory.createDefaultILayerMessageTask(trfEvent.getEventId(), iLayerMessage);
        } else {
            // 6.build message
            //todo 目前通知customer使用ilayer的透传接口，所以需要对请求参数包装成ilayer的格式,后续不使用ilayer了删除
            if (trfEvent.getTriggerBy() == TrfEventTriggerBy.PREORDER) {
                convertedData = new IlayerTransparentReq(trfEvent, convertedData);
            }

            MessageTaskParameters notifyMessage = MessageTaskParameters.build(convertedData, systemApi, trfEvent);
            // 7.build Task
            messageTask = TaskFactory.createDefaultMessageTask(trfEvent.getEventId(), notifyMessage);
        }

        messageTask.setIdentityId(eventSubscribe.getSubscriber());
        messageTask.setGroupKey(String.valueOf(eventSubscribe.getEventCode()));
        messageTask.setExtId(trfEvent.getTrfNo());
        messageTask.setRefSystemId(eventSubscribe.getRefSystemId());

        // 如果需要顺序发送,查找前置taskId
        TaskCondition taskCondition = this.getTaskConditionIfMatched(eventSubscribe, trfEvent.getTrfNo());
        if (taskCondition != null) {
            messageTask.setDependCondition(JSON.toJSONString(taskCondition));
        }
        // 8.submit Task
        if (taskService.checkSubmitTask(messageTask, IgnoreAttrNameMap.getIgnoreAttrNames(systemApi.getId()))) {
            taskService.submitTask(messageTask);
        }
    }

    private static final Integer[] SERIAL_EVENT_CODES = {TRF_TO_ORDER_EVENT, TRF_TO_QUOTATION_EVENT, TRF_CONFIRMED_EVENT, TRF_TESTING_EVENT
            , TRF_REPORTING_EVENT, TRF_COMPLETED_EVENT, TRF_CLOSED_EVENT};

    private TaskCondition getTaskConditionIfMatched(EventSubscribeDTO eventSubscribe, String trfNo) {
        // 不要求顺序发送
        if (!EventNotifyRuleEnum.SERIAL_ON_NOTIFY.match(eventSubscribe.getNotifyRule())) {
            return null;
        }
        // 当前code如果不在需要顺序发送的事件列表中，返回空
        int matchIndex = ArrayUtil.matchIndex(v -> v.equals(eventSubscribe.getEventCode()), SERIAL_EVENT_CODES);
        boolean isSerialEvent = matchIndex > 0;
        if (!isSerialEvent) {
            return null;
        }

        // 判断客户订阅的上一个事件是不是在顺序发送列表中
        List<EventSubscribeDTO> subscribeList = eventSubscribeService.findBySubsciberAndRefSystemId(eventSubscribe.getSubscriber(), eventSubscribe.getRefSystemId());
        Integer groupKey = subscribeList.stream().map(EventSubscribeDTO::getEventCode)
                .filter(code -> (code < eventSubscribe.getEventCode())).sorted()
                .filter(code -> ArrayUtil.matchIndex(v -> v.equals(code), SERIAL_EVENT_CODES) > 0)
                .findFirst().orElse(null);

        if (groupKey == null) {
            return null;
        }

        return TaskCondition.newBuilder()
                .identityIdEquals(eventSubscribe.getSubscriber())
                .extIdEquals(trfNo)
                .groupKeyEquals(groupKey.toString())
                .statusIn(Collections.singletonList(TaskStatusEnum.SUCCESS.getCode())).build();
    }

    public DataNotifyServiceImpl(DataConvertor<String, String, JSON> jsonDataConvertor, TaskService taskService, SystemApiManager systemApiManager, EventSubscribeService eventSubscribeService, DataCollectService dataCollectService) {
        this.jsonDataConvertor = jsonDataConvertor;
        this.taskService = taskService;
        this.systemApiManager = systemApiManager;
        this.eventSubscribeService = eventSubscribeService;
        this.dataCollectService = dataCollectService;
    }

}
