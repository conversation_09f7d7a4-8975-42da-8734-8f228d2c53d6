package com.sgs.customerbiz.biz.service.message.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.sgs.customerbiz.biz.constants.TaskConstants;
import com.sgs.customerbiz.biz.dto.TaskInfoDTO;
import com.sgs.customerbiz.biz.enums.TaskResultEnum;
import com.sgs.customerbiz.biz.event.RefSystemIdAdapter;
import com.sgs.customerbiz.biz.service.condition.base.CustomerReNotifyRuleChecker;
import com.sgs.customerbiz.biz.service.message.MessageService;
import com.sgs.customerbiz.biz.service.message.HitOrderFetcher;
import com.sgs.customerbiz.biz.service.task.TaskService;
import com.sgs.customerbiz.dbstorages.mybatis.extmapper.todolist.TaskInfoExtMapper;
import com.sgs.customerbiz.dbstorages.mybatis.extmodel.dto.TaskMessageDto;
import com.sgs.customerbiz.dbstorages.mybatis.extmodel.dto.TrfOrderLogDto;
import com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.*;
import com.sgs.customerbiz.dbstorages.mybatis.model.*;
import com.sgs.customerbiz.dfv.enums.ActiveIndicatorEnum;
import com.sgs.customerbiz.domain.enums.TaskStatusEnum;
import com.sgs.customerbiz.facade.model.enums.ResendMsgModeEnum;
import com.sgs.customerbiz.facade.model.req.MessageInquiryReq;
import com.sgs.customerbiz.facade.model.req.MessageSendReq;
import com.sgs.customerbiz.facade.model.rsp.MessageInquiryRsp;
import com.sgs.customerbiz.facade.model.rsp.MessageSendRsp;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.core.util.IdUtil;
import com.sgs.framework.tool.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static com.sgs.customerbiz.core.constants.Constants.USER_DEFAULT;

/**
 * <AUTHOR> walley.wang
 * @date         : 2023-07-27 12:23
 * @version      : V1.0.0
 * @desc         :
 * <pre>
 *     通知消息管理服务
 * </pre>
 */
@Slf4j
@Service
public class MessageServiceImpl implements MessageService {

    // TODO 迁移至公用地方或使用通用配置
    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int DEFAULT_PAGE_SIZE = 20;

    @Resource
    private TaskInfoExtMapper taskInfoExtMapper;

    @Resource
    private TaskInfoMapper taskInfoMapper;

    @Resource
    private TaskExecuteLogMapper taskExecuteLogMapper;

    @Resource
    private TaskService taskService;

    @Resource
    private TrfOrderMapper trfOrderMapper;

    @Resource
    private Map<String, CustomerReNotifyRuleChecker> customerReNotifyRuleCheckerMap;

    @Resource
    private Map<String, HitOrderFetcher> orderFetcherMap;

    @Override
    public PageInfo<MessageInquiryRsp> inquiryMessageList(MessageInquiryReq messageInquiryReq) {
        if (StringUtils.isNotEmpty(messageInquiryReq.getOrderNo())) {
            TrfOrderExample trfOrderExample = new TrfOrderExample();
            trfOrderExample.createCriteria().
                andOrderNoEqualTo(messageInquiryReq.getOrderNo()).
                andActiveIndicatorEqualTo(ActiveIndicatorEnum.Active.getStatus());
            List<TrfOrderPO> trfOrderPOList = trfOrderMapper.selectByExample(trfOrderExample);
            if (CollectionUtils.isNotEmpty(trfOrderPOList)) {
                messageInquiryReq.setTrfNos(new ArrayList<>());
                messageInquiryReq.getTrfNos().add(CollUtil.get(trfOrderPOList, 0).getTrfNo());
            } else {
                return new PageInfo<>();
            }
        }

//        FIXME 此处不应该区分执行系统和客户系统，但是SODA和SGSMart系统id一样，且目前能明确传过来的是RefSystemId，强制将SGSMart的systemId转成标准的定义
        if (messageInquiryReq.getSystemId() != null) {
            messageInquiryReq.setSystemId(RefSystemIdAdapter.route(messageInquiryReq.getSystemId()));
        }

        if (messageInquiryReq.getPageNum() == null) {
            messageInquiryReq.setPageNum(DEFAULT_PAGE_NUM);
        }
        if (messageInquiryReq.getPageSize() == null) {
            messageInquiryReq.setPageSize(DEFAULT_PAGE_SIZE);
        }

        if (messageInquiryReq.getSentAtFrom() == null) {}
        if (messageInquiryReq.getSentAtTo() == null) {}

        TaskInfoExample example = new TaskInfoExample();
        TaskInfoExample.Criteria criteria = example.createCriteria();
        if (CollectionUtils.isNotEmpty(messageInquiryReq.getTrfNos())) {
            criteria.andExtIdIn(messageInquiryReq.getTrfNos());
        }

        if (messageInquiryReq.getSystemId() != null) {
            criteria.andIdentityIdEqualTo(messageInquiryReq.getSystemId());
        }

        if (messageInquiryReq.getEventCode() != null) {
            criteria.andGroupKeyEqualTo(messageInquiryReq.getEventCode());
        }

        if (CollectionUtils.isNotEmpty(messageInquiryReq.getExecStatusList())) {
            criteria.andTaskStatusIn(messageInquiryReq.getExecStatusList());
        }

//        默认过滤cancelled状态的task
        criteria.andTaskStatusNotIn(Arrays.asList(TaskStatusEnum.CANCELED.getCode()));

        if (messageInquiryReq.getSentAtFrom() != null) {
            criteria.andLastExecTimeGreaterThanOrEqualTo(DateUtil.toMilliseconds(
                LocalDateTime.ofInstant(messageInquiryReq.getSentAtFrom().toInstant(), ZoneId.systemDefault())));
        }

        if (messageInquiryReq.getSentAtTo() != null) {
            criteria.andLastExecTimeLessThanOrEqualTo(DateUtil.toMilliseconds(
                LocalDateTime.ofInstant(messageInquiryReq.getSentAtTo().toInstant(), ZoneId.systemDefault())));
        }

        example.setOrderByClause("last_exec_time desc");

        PageHelper.startPage(messageInquiryReq.getPageNum(), messageInquiryReq.getPageSize());

        List<TaskMessageDto> taskMessageDtoList = taskInfoExtMapper.selectByExampleWithBLOBs(example, messageInquiryReq.getLabCode());
        if (CollectionUtils.isNotEmpty(taskMessageDtoList)) {
            List<MessageInquiryRsp> messageInquiryRspList = new ArrayList<>();
            taskMessageDtoList.forEach(messageDto -> {
                MessageInquiryRsp messageInquiryRsp = new MessageInquiryRsp();

                if (CollectionUtils.isNotEmpty(messageDto.getLogDtoList())) {
                    // ToOrder、Confirmed、Testing、Pending、OrderCancel、PreorderCancel、CustomerCancel 取第一笔 操作的订单
                    // Completed、Closed、UnBind取最后变成该状态的订单
                    String fetcherKey = Arrays.asList("10","30","40","80","81","82","100").contains(messageDto.getGroupKey())?"orHitOrderFetcher":"andHitOrderFetcher";
                    TrfOrderLogDto trfOrderLogDto = orderFetcherMap.get(fetcherKey).fetch(messageDto.getLogDtoList());
                    if (StringUtils.isNotBlank(messageInquiryReq.getOrderNo()) &&
                        !Objects.equals(messageInquiryReq.getOrderNo(), trfOrderLogDto.getOrderNo())) {
                        return;
                    }
                    messageInquiryRsp.setOrderNo(trfOrderLogDto.getOrderNo());
                }
                messageInquiryRsp.setMsgId(String.valueOf(messageDto.getTaskId()));
                messageInquiryRsp.setTrfNo(messageDto.getExtId());
                messageInquiryRsp.setRefSystem(new MessageInquiryRsp.RefSystemVO(RefSystemIdAdapter.recover(messageDto.getIdentityId())));
                messageInquiryRsp.setSendKeyPoint(messageDto.getGroupKey());
                messageInquiryRsp.setExecStatus(messageDto.getTaskStatus());
                messageInquiryRsp.setCreatedAt(messageDto.getCreatedDate());
                messageInquiryRsp.setSentAt(new Date(messageDto.getLastExecTime()));

                if (Objects.equals(TaskStatusEnum.FAILED.getCode(), messageDto.getTaskStatus())) {
                    if(StringUtils.isNotEmpty(messageDto.getExecResult())) {
                        JSONObject execResult = JSON.parseObject(messageDto.getExecResult());
                        messageInquiryRsp.setFailedReason(execResult.getString("message"));
                    }
                } else if (Objects.equals(TaskStatusEnum.EXPIRED.getCode(), messageDto.getTaskStatus())) {
                    messageInquiryRsp.setFailedReason(TaskStatusEnum.EXPIRED.getDesc());
                }

                messageInquiryRspList.add(messageInquiryRsp);
            });

            PageInfo<MessageInquiryRsp> page = new PageInfo<>(messageInquiryRspList);
            page.setPageNum(messageInquiryReq.getPageNum());
            page.setPageSize(messageInquiryReq.getPageSize());

            return page;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MessageSendRsp send(MessageSendReq messageSendReq) {
        if (StringUtils.isBlank(messageSendReq.getMsgId())) {
            throw new BizException("MsgId should not be null");
        }

        TaskInfoExample taskInfoExample = new TaskInfoExample();
        taskInfoExample.createCriteria().andTaskIdEqualTo(Long.parseLong(messageSendReq.getMsgId()));
        List<TaskInfoPO> taskInfoPOList = taskInfoMapper.selectByExampleWithBLOBs(taskInfoExample);
        if (CollectionUtils.isEmpty(taskInfoPOList)) {
            log.error("TaskInfo was not found. TaskId={}", messageSendReq.getMsgId());
            throw new BizException("Message was not found");
        }
        TaskInfoPO taskInfoPO = CollUtil.get(taskInfoPOList, 0);

//        针对不同类型的消息的私有规则校验
        CustomerReNotifyRuleChecker reNotifyRuleChecker = customerReNotifyRuleCheckerMap.get(taskInfoPO.getGroupKey());
        if (reNotifyRuleChecker == null) {
            reNotifyRuleChecker = customerReNotifyRuleCheckerMap.get("baseCustomerReNotifyRuleChecker");
        }
        reNotifyRuleChecker.doCheck(taskInfoPO, messageSendReq);

        if (ResendMsgModeEnum.New.getMode().equalsIgnoreCase(messageSendReq.getSendMode())) {
            TaskInfoDTO newTaskInfo = new TaskInfoDTO();
            BeanUtils.copyProperties(taskInfoPO, newTaskInfo);
            newTaskInfo.setTaskId(IdUtil.snowflakeId());
            newTaskInfo.setExpiredTime(DateTime.now().plusDays(2).getMillis());
            newTaskInfo.setMaxFail(TaskConstants.DEFAULT_MAX_FAIL);
            newTaskInfo.setMaxRetry(TaskConstants.DEFAULT_MAX_RETRY);
            newTaskInfo.setRetryTimeMillis(TaskConstants.DEFAULT_RETRY_TIME_MILLIS);
            newTaskInfo.setLastExecTime(0L);
            taskService.submitTask(newTaskInfo);
        } else {
            taskInfoPO.setTaskStatus(TaskStatusEnum.INIT.getCode());
            taskInfoPO.setMaxFail(taskInfoPO.getMaxFail()*2);
            taskInfoPO.setMaxRetry(taskInfoPO.getMaxRetry()*2);
            taskInfoPO.setModifiedDate(DateUtil.now());
            taskInfoPO.setModifiedBy(USER_DEFAULT);
            taskInfoPO.setExpiredTime(DateTime.now().plusDays(2).getMillis());
            taskInfoPO.setLastExecTime(0L);
            taskInfoPO.setCreatedBy(USER_DEFAULT);
            taskInfoPO.setCreatedDate(DateUtil.now());
            boolean bool = taskService.recover(taskInfoPO);
            log.info("The result of the task recovery is {}. TaskId={}", bool, messageSendReq.getMsgId());
        }

        return MessageSendRsp.succeed();
    }

}
