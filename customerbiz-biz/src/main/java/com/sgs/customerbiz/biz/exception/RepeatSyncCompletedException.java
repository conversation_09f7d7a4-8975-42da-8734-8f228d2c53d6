package com.sgs.customerbiz.biz.exception;

import com.sgs.customerbiz.model.trf.dto.resp.TrfSyncResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class RepeatSyncCompletedException extends BizException {

    private TrfSyncResult result;


    public RepeatSyncCompletedException(String msg) {
        super(ResponseCode.ErrDuplicateReq, msg);
    }
}
