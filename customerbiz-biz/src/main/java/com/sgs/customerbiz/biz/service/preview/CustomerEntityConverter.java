package com.sgs.customerbiz.biz.service.preview;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.sgs.config.api.dto.req.ConfigGetReq;
import com.sgs.customerbiz.biz.convert.impl.JsonDataConvertor;
import com.sgs.customerbiz.facade.model.trf.rsp.CustomerTrfInfoRsp;
import com.sgs.customerbiz.integration.ConfigClient;
import com.sgs.framework.tool.utils.Func;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class CustomerEntityConverter {



    private final JsonDataConvertor jsonDataConvertor;

    private final ConfigClient configClient;

    public CustomerEntityConverter(JsonDataConvertor jsonDataConvertor, ConfigClient configClient) {
        this.jsonDataConvertor = jsonDataConvertor;
        this.configClient = configClient;
    }

    public List<CustomerTestLineJSONObject> customerTestLine(String template, CustomerTrfInfoRsp customerTrfInfo) {

        JSON convert = jsonDataConvertor.convert(customerTrfInfo.getContent(), template);
        JSONArray customerTestLineJSON = Optional.ofNullable(convert)
                .map(c -> (JSONObject)c)
                .map(c ->c.getJSONArray("testLines"))
                .orElseThrow(() -> new IllegalStateException("convert error"));

        List<CustomerTestLineJSONObject> result = new ArrayList<>();
        for (int i = 0; i < customerTestLineJSON.size(); i++) {
            JSONObject customerTestLine = customerTestLineJSON.getJSONObject(i);
            CustomerTestLineJSONObject customerTestLineJSONObject = new CustomerTestLineJSONObject();
            customerTestLineJSONObject.putAll(customerTestLine);
            result.add(customerTestLineJSONObject);
        }
        return result;
    }
}
