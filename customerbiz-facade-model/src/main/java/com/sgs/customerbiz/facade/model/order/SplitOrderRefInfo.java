package com.sgs.customerbiz.facade.model.order;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.sgs.framework.core.common.PrintFriendliness;
import lombok.Data;

@Data
@JsonIgnoreProperties(value = {"handler"})
public class SplitOrderRefInfo extends PrintFriendliness {
    /**
     *
     */
    private String orderId;
    /**
     *
     */
    private String orderNo;
    /**
     *
     */
    private String reportId;
    /**
     *
     */
    private String reportNo;

}
