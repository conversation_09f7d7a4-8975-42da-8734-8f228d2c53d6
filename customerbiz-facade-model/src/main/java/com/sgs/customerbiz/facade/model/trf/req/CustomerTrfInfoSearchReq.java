package com.sgs.customerbiz.facade.model.trf.req;

import com.sgs.customerbiz.facade.model.enums.SearchType;
import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public final class CustomerTrfInfoSearchReq extends BaseRequest {

    private List<String> trfNos;
    private List<String> packageBarcodes;
}
