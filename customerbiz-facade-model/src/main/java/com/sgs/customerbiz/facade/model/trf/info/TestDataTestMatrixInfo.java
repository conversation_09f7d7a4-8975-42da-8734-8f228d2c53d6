package com.sgs.customerbiz.facade.model.trf.info;

import io.swagger.annotations.ApiModelProperty;

public class TestDataTestMatrixInfo {
    /**
     *
     */
    @ApiModelProperty(value = "测试组ID")
    private String testMatrixId;

    /**
     *
     */
    @ApiModelProperty(value = "测试样ID")
    private String testSampleInstanceId;

    /**
     *
     */
    @ApiModelProperty(value = "测试项ID")
    private String testLineInstanceId;

    /**
     *
     */
    @ApiModelProperty(value = "测试条件分组")
    private String testConditionGroup;

    /**
     *
     */
    @ApiModelProperty(value = "测试结论")
    private String conclusion;

    /**
     *
     */
    @ApiModelProperty(value = "测试结论备注")
    private String conclusionRemark;

    /**
     *
     */
    @ApiModelProperty(value = "客户测试结论")
    private String customerConclusion;

    public String getTestMatrixId() {
        return testMatrixId;
    }

    public void setTestMatrixId(String testMatrixId) {
        this.testMatrixId = testMatrixId;
    }

    public String getTestSampleInstanceId() {
        return testSampleInstanceId;
    }

    public void setTestSampleInstanceId(String testSampleInstanceId) {
        this.testSampleInstanceId = testSampleInstanceId;
    }

    public String getTestLineInstanceId() {
        return testLineInstanceId;
    }

    public void setTestLineInstanceId(String testLineInstanceId) {
        this.testLineInstanceId = testLineInstanceId;
    }

    public String getTestConditionGroup() {
        return testConditionGroup;
    }

    public void setTestConditionGroup(String testConditionGroup) {
        this.testConditionGroup = testConditionGroup;
    }

    public String getConclusion() {
        return conclusion;
    }

    public void setConclusion(String conclusion) {
        this.conclusion = conclusion;
    }

    public String getConclusionRemark() {
        return conclusionRemark;
    }

    public void setConclusionRemark(String conclusionRemark) {
        this.conclusionRemark = conclusionRemark;
    }

    public String getCustomerConclusion() {
        return customerConclusion;
    }

    public void setCustomerConclusion(String customerConclusion) {
        this.customerConclusion = customerConclusion;
    }
}
