package com.sgs.customerbiz.facade.model.trf.info;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class ServiceRequirementStandardInfo {
    /**
     *
     */
    @ApiModelProperty(value = "report")
    private ReportStandardInfo report;

    /**
     *
     */
    @ApiModelProperty(value = "contactList")
    private List<ContactStandardInfo> contactList;

    public ReportStandardInfo getReport() {
        return report;
    }

    public void setReport(ReportStandardInfo report) {
        this.report = report;
    }

    public List<ContactStandardInfo> getContactList() {
        return contactList;
    }

    public void setContactList(List<ContactStandardInfo> contactList) {
        this.contactList = contactList;
    }
}
