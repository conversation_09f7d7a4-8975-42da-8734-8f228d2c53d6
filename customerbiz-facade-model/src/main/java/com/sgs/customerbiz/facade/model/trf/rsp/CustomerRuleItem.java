package com.sgs.customerbiz.facade.model.trf.rsp;

import com.sgs.framework.core.common.PrintFriendliness;

/**
 * @ClassName CustomerRuleItem
 * @Description 具体Rule规则
 * <AUTHOR>
 * @Date 2023/5/29
 */
public class CustomerRuleItem extends PrintFriendliness {

    /**
     * "creatrType自建单、非自建单
     */
    private Integer createType;

    /**
     *  1：至少选一个  0：可以不选择
     */
    private Integer rule;

    public Integer getCreateType() {
        return createType;
    }

    public void setCreateType(Integer createType) {
        this.createType = createType;
    }

    public Integer getRule() {
        return rule;
    }

    public void setRule(Integer rule) {
        this.rule = rule;
    }
}
