package com.sgs.customerbiz.facade.model.enums;

import java.util.HashMap;
import java.util.Map;

public enum BoundStatus {
    Connection(0, "建立关系"),
    BoundHasOrder(1, "已绑定"),
    UnBound(2, "已解绑");

    private int type;
    private String msg;

    BoundStatus(int type, String msg) {
        this.type = type;
        this.msg = msg;
    }

    public int getType() {
        return type;
    }

    public String getMsg() {
        return msg;
    }

    static Map<Integer, BoundStatus> maps = new HashMap<>();

    static {
        for (BoundStatus type : BoundStatus.values()) {
            maps.put(type.getType(), type);
        }
    }


    public static boolean check(Integer type) {
        if (type == null) {
            return false;
        }
        return maps.containsKey(type.intValue());
    }

    /**
     * @param status
     * @param types
     * @return
     */
    public static boolean check(Integer status, BoundStatus ...types) {
        if (status == null || !maps.containsKey(status.intValue()) || (types==null || types.length==0)) {
            return false;
        }
        for (BoundStatus type : types) {
            if(maps.get(status.intValue()) == type){
                return true;
            }

        }
        return false;
    }
}
