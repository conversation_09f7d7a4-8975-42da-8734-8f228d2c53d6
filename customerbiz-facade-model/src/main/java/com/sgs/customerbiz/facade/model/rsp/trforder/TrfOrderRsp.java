package com.sgs.customerbiz.facade.model.rsp.trforder;

import com.sgs.customerbiz.facade.model.order.CareLabelInfo;
import com.sgs.customerbiz.facade.model.order.UserLabBuInfo;
import com.sgs.framework.core.common.PrintFriendliness;

import java.util.List;

public class TrfOrderRsp extends PrintFriendliness {
    /**
     *
     */
    private OrderHeaderRsp headers;
    /**
     *
     */
    private CustomerRsp applicant;
    /**
     *
     */
    private CustomerRsp payer;
    /**
     *
     */
    private CustomerRsp buyer;
    /**
     *
     */
    private CustomerRsp agent;

    private CustomerRsp supplier;

    private CustomerRsp manufacture;
    /**
     *
     */
    private List<TrfTestLineRsp> testLines;
    /**
     *
     */
    private TestRequestRsp testRequest;
    /**
     *
     */
    private TrfProductRsp product;
    /**
     *
     */
    private List<TrfProductSampleRsp> productSamples;
    /**
     *
     */
    private List<ProductListRsp> productSampleRspList;

    /**
     *
     */
    private List<CareLabelInfo> careLabels;
    /**
     *
     */
    private UserLabBuInfo lab;

    private List<OrderAttachmentRsp>  orderAttachmentDTOS;

    public OrderHeaderRsp getHeaders() {
        return headers;
    }

    public CustomerRsp getManufacture() {
        return manufacture;
    }

    public void setManufacture(CustomerRsp manufacture) {
        this.manufacture = manufacture;
    }

    public void setHeaders(OrderHeaderRsp headers) {
        this.headers = headers;
    }

    public CustomerRsp getApplicant() {
        return applicant;
    }

    public void setApplicant(CustomerRsp applicant) {
        this.applicant = applicant;
    }

    public CustomerRsp getPayer() {
        return payer;
    }

    public void setPayer(CustomerRsp payer) {
        this.payer = payer;
    }

    public CustomerRsp getSupplier() {
        return supplier;
    }

    public void setSupplier(CustomerRsp supplier) {
        this.supplier = supplier;
    }

    public CustomerRsp getBuyer() {
        return buyer;
    }

    public void setBuyer(CustomerRsp buyer) {
        this.buyer = buyer;
    }

    public CustomerRsp getAgent() {
        return agent;
    }

    public void setAgent(CustomerRsp agent) {
        this.agent = agent;
    }

    public List<TrfTestLineRsp> getTestLines() {
        return testLines;
    }

    public void setTestLines(List<TrfTestLineRsp> testLines) {
        this.testLines = testLines;
    }

    public TestRequestRsp getTestRequest() {
        return testRequest;
    }

    public void setTestRequest(TestRequestRsp testRequest) {
        this.testRequest = testRequest;
    }

    public TrfProductRsp getProduct() {
        return product;
    }

    public void setProduct(TrfProductRsp product) {
        this.product = product;
    }

    public List<TrfProductSampleRsp> getProductSamples() {
        return productSamples;
    }

    public void setProductSamples(List<TrfProductSampleRsp> productSamples) {
        this.productSamples = productSamples;
    }

    public List<ProductListRsp> getProductSampleRspList() {
        return productSampleRspList;
    }

    public void setProductSampleRspList(List<ProductListRsp> productSampleRspList) {
        this.productSampleRspList = productSampleRspList;
    }

    public List<CareLabelInfo> getCareLabels() {
        return careLabels;
    }

    public void setCareLabels(List<CareLabelInfo> careLabels) {
        this.careLabels = careLabels;
    }

    public UserLabBuInfo getLab() {
        return lab;
    }

    public void setLab(UserLabBuInfo lab) {
        this.lab = lab;
    }

    public List<OrderAttachmentRsp> getOrderAttachmentDTOS() {
        return orderAttachmentDTOS;
    }

    public void setOrderAttachmentDTOS(List<OrderAttachmentRsp> orderAttachmentDTOS) {
        this.orderAttachmentDTOS = orderAttachmentDTOS;
    }
}
