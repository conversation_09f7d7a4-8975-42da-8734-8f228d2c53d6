package com.sgs.customerbiz.facade.model.todolist.req;

import com.sgs.framework.core.base.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class GetTrfDetailPrintReq extends BaseRequest {

    @NotNull(message = "GetTrfDetailPrintInfo parameter should not be null")
    @NotEmpty(message = "GetTrfDetailPrintInfo parameter should not be empty")
    private List<GetTrfDetailPrintItemPOJO> printItemList;

    private FileSetting setting;

    private FileTemplate template;

    private FileStyle style;

    @Data
    public static class FileTemplate {
        // 模板文件资源URL，https或http格式
        private String filePath;

        // 模板文件远程存储key
        private String cloudId;

        // 模板内容
        private String content;
    }

    @Data
    public static class FileSetting {

        private final static String DEFAULT_FILE_TYPE = "PDF";
        private final static Integer DEFAULT_LAYOUT_PADDING = 20;
        private final static Integer DEFAULT_LAYOUT_MARGIN = 20;
        private final static Integer DEFAULT_LAYOUT_SCALING = 100;

        // 文件类型，支持PDF/DOC/HTML，暂时只支持一种
        private List<String> type = Collections.singletonList(DEFAULT_FILE_TYPE);

        // 内边距，单位px
        private Integer padding = DEFAULT_LAYOUT_PADDING;

        // 外边距，单位px
        private Integer margin = DEFAULT_LAYOUT_MARGIN;

        // 缩放比例，%前的数字
        private Integer scaling = DEFAULT_LAYOUT_SCALING;
    }

    @Data
    public static class FileStyle {

        private final static String DEFAULT_FONT_FAMILY = "宋体";
        private final static String DEFAULT_FONT_WEIGHT = "Regular";
        private final static Integer DEFAULT_FONT_SIZE = 14;
        private final static String DEFAULT_FONT_COLOR = "#000000";
        private final static Integer DEFAULT_LINE_HEIGHT = 1;


        // 字体
        private String fontFamily = DEFAULT_FONT_FAMILY;

        // 字体粗细
        private String fontWeight = DEFAULT_FONT_WEIGHT;

        // 字体大小，单位px
        private Integer fontSize = DEFAULT_FONT_SIZE;

        // 字体颜色
        private String fontColor = DEFAULT_FONT_COLOR;

        // 行高
        private Integer lineHeight = DEFAULT_LINE_HEIGHT;

    }

    @Data
    public static class GetTrfDetailPrintItemPOJO {
        private Integer refSystemId;

        private String customerGroupCode;

        private String productLineCode;

        private List<String> trfNoList;

        private List<String> sceneTypeList;
    }

}
