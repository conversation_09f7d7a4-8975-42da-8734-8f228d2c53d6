package com.sgs.customerbiz.facade.model.todolist.req;

import com.sgs.framework.core.base.BaseRequest;
import io.swagger.annotations.ApiModel;

import javax.validation.constraints.NotNull;

@ApiModel(description = "Remove Trf")
public class RemoveTrfReq extends BaseRequest {
    /**
     * Trf Id 不能为空
     */
    @NotNull(message = "Trf Id 不能为空")
    private Long trfId;

    /**
     *
     */
    @NotNull(message = "refSystemId 不能为空")
    private Integer refSystemId;

    private String reason;

    @NotNull(message = "reasonType 不能为空")
    private Integer reasonType;

    private String reasonContent;

    public Long getTrfId() {
        return trfId;
    }

    public void setTrfId(Long trfId) {
        this.trfId = trfId;
    }

    public Integer getRefSystemId() {
        return refSystemId;
    }

    public void setRefSystemId(Integer refSystemId) {
        this.refSystemId = refSystemId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getReasonType() {
        return reasonType;
    }

    public void setReasonType(Integer reasonType) {
        this.reasonType = reasonType;
    }

    public String getReasonContent() {
        return reasonContent;
    }

    public void setReasonContent(String reasonContent) {
        this.reasonContent = reasonContent;
    }
}
