package com.sgs.customerbiz.facade.model.trf.rsp;

import com.sgs.framework.core.common.PrintFriendliness;

import java.util.List;

/**
 * @ClassName CreateOrderSelectSampleRuleItem
 * @Description "客户选择Sample时带入订单的规则  1：至少选一个  0：可以不选择"
 * <AUTHOR>
 * @Date 2023/5/29
 */
public class CreateOrderSampleRuleItem extends PrintFriendliness {
    /**
     *  1：至少选一个  0：可以不选择
     */
    private Integer rule;

    private List<CustomerRuleItem> ruleList;

    public Integer getRule() {
        return rule;
    }

    public void setRule(Integer rule) {
        this.rule = rule;
    }

    public List<CustomerRuleItem> getRuleList() {
        return ruleList;
    }

    public void setRuleList(List<CustomerRuleItem> ruleList) {
        this.ruleList = ruleList;
    }
}
