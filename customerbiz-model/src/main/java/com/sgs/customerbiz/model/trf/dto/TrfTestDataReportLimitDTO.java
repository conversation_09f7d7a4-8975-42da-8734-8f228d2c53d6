package com.sgs.customerbiz.model.trf.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TrfTestDataReportLimitDTO implements Serializable {

    private String limitGroup;

    /**
     * Limit Value 1 + OperatorName + Limit Value2
     */
    @NotBlank(message = "limitValue不能为空！",groups = TrfTestDataReportLimitDTO.class)
    private String limitValue;

    /**
     * 单位
     */
    @NotBlank(message = "limitUnit不能为空！",groups = TrfTestDataReportLimitDTO.class)
    private String limitUnit;
}
