
package com.sgs.customerbiz.model.trf.dto;

import com.sgs.testdatabiz.facade.model.dto.rd.report.ReportCertificateDTO;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class TrfReportDTO implements Serializable {

    private String rootOrderNo;
    // TODO 结构统一后移除
    private String orderNo;

    private String realOrderNo;

    private String reportId;

    private String reportInstanceId;

    private Long systemId;

    private String reportNo;

    private String originalReportNo;

    private String rootReportNo;
    private String oldReportNo;
    private String reportVersion;
    private String rslstatus;
    private String failCode;

    private Integer reportStatus;

    private Date reportDueDate;

    private Date softCopyDeliveryDate;

    private String approveBy;

    private Date approveDate;

    private String certificateName;

    private String createBy;
    private String testMatrixMergeMode;
    private Date createDate;

    private String reportRemark;

    //SCI-1465 reportSourceType，1 执行系统 2：CustomerReport
    private Integer reportSourceType;

    private TrfLabDTO lab;

    private TrfConclusionDTO conclusion;

    private TrfReportRelationshipDTO relationship;

    private List<TrfReportMatrixDTO> reportMatrixList;

    private List<TrfReportConclusionDTO> reportConclusionList;

    private List<TrfFileDTO> reportFileList;

    private List<TrfSubReportDTO> subReportList;

    private List<TrfConditionGroupDTO> conditionGroupList;

    private List<TrfReferenceDTO> trfList;

    private List<TrfReportCertificateDTO> reportCertificateList;

    private TrfEfilingDTO eFiling;


}
