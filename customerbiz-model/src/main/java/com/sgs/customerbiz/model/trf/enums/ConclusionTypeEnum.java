package com.sgs.customerbiz.model.trf.enums;


import java.util.HashMap;
import java.util.Map;



public enum ConclusionTypeEnum {
    Matrix(601, "Individual Conclusion","Individual Conclusion"),
    TestLine(602, ConclusionDimType.MatrixDim, ConclusionCalcType.TestLine, "TestItem", "TestItem Conclusion for All Original Sample"),
    Report(603, ConclusionDimType.MatrixDim, ConclusionCalcType.Report,true,"Report Conclusion","Report Conclusion"),
    TestLine_OriginalSample(604, ConclusionDimType.MatrixDim, ConclusionCalcType.OriginalSample,"TL*OriginalSample","TestItem Conclusion for each Original Sample"),
    OriginalSample(605, ConclusionDimType.MatrixDim, ConclusionCalcType.OriginalSample, true,"OriginalSample","Original Sample Conclusion"),
    Section(606, ConclusionDimType.SectionDim, ConclusionCalcType.Section, ConclusionDimType.SectionDim, true,"Section","Section Conclusion"),
    Section_TL(607, ConclusionDimType.MatrixDim, ConclusionCalcType.TestLine, ConclusionDimType.SectionDim, "Section*TL","Section TL"),
    Section_TL_OriginalSample(608, ConclusionDimType.MatrixDim, ConclusionCalcType.OriginalSample, ConclusionDimType.SectionDim, "Section*TL*OriginalSample","Section TL OriginalSample"),
    PP_TL(609, ConclusionDimType.MatrixDim, ConclusionCalcType.TestLine, ConclusionDimType.PpDim, "PP*TL","PP TL"),
    PP_TL_OriginalSample(610, ConclusionDimType.MatrixDim, ConclusionCalcType.OriginalSample, ConclusionDimType.PpDim, "PP*TL*OriginalSample","PP TL OriginalSample"),
    PP(611, ConclusionDimType.PpDim, ConclusionCalcType.PP, ConclusionDimType.PpDim, true,"PP","PP Conclusion");

    private int code;
    private ConclusionDimType dimType;
    private ConclusionCalcType calcType;
    private ConclusionDimType bizId; // 是否需要BizId（SectionId、PPNo）
    private boolean isEmptyTestLineId; // 是否允许为空的TestLineId
    private String describe;
    private String message;

    ConclusionTypeEnum(int code, String message) {
        this.dimType = ConclusionDimType.None;
        this.bizId = ConclusionDimType.None;
        this.code = code;
        this.message = message;
    }

    ConclusionTypeEnum(int code, String describe, String message) {
        this.dimType = ConclusionDimType.None;
        this.bizId = ConclusionDimType.None;
        this.code = code;
        this.describe = describe;
        this.message = message;
    }

    ConclusionTypeEnum(int code, ConclusionDimType dimType, String describe, String message) {
        this(code, describe, message);
        this.dimType = dimType;
    }

    ConclusionTypeEnum(int code, ConclusionDimType dimType, ConclusionCalcType calcType, String describe, String message) {
        this(code, dimType, describe, message);
        this.calcType = calcType;
    }

    ConclusionTypeEnum(int code, ConclusionDimType dimType, ConclusionCalcType calcType, boolean isEmptyTestLineId, String describe, String message) {
        this(code, dimType, describe, message);
        this.calcType = calcType;
        this.isEmptyTestLineId = isEmptyTestLineId;
    }

    ConclusionTypeEnum(int code, ConclusionDimType dimType, ConclusionCalcType calcType, ConclusionDimType bizId, String describe, String message) {
        this(code, dimType, calcType, describe, message);
        this.bizId = bizId;
    }

    /**
     *
     * @param code
     * @param dimType
     * @param calcType ConclusionCalcType 类型:TestLine、OriginalSample
     * @param dimBizId 是否需要BizId（SectionId、PPNo）
     * @param isEmptyTestLineId
     * @param message
     */
    ConclusionTypeEnum(int code, ConclusionDimType dimType, ConclusionCalcType calcType, ConclusionDimType dimBizId, boolean isEmptyTestLineId, String describe, String message) {
        this(code, dimType, calcType, dimBizId, describe, message);
        this.isEmptyTestLineId = isEmptyTestLineId;
    }

    public int getCode() {
        return this.code;
    }

    public ConclusionDimType getDimType() {
        return dimType;
    }

    public ConclusionCalcType getCalcType() {
        return calcType;
    }

    public ConclusionDimType getBizId() {
        return bizId;
    }

    /**
     * 是否允许为空，默认不能为空
     * @return
     */
    public boolean isEmptyTestLineId() {
        return isEmptyTestLineId;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public String getMessage() {
        return message;
    }

    static Map<Integer, ConclusionTypeEnum> maps = new HashMap<>();

    static {
        for (ConclusionTypeEnum type : ConclusionTypeEnum.values()) {
            maps.put(type.getCode(), type);
        }
    }

    public static ConclusionTypeEnum findCode(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return null;
        }
        ConclusionTypeEnum type = maps.get(code.intValue());
        if (type == null) {
            /*throw new IllegalArgumentException("ConclusionType not found" + code);*/
            return null;
        }
        return type;
    }

    public static boolean check(Integer code) {
        if (code == null || !maps.containsKey(code.intValue())){
            return false;
        }
        return maps.get(code.intValue()) != null;
    }

    public static boolean check(Integer code,ConclusionTypeEnum ...conclusionTypes ) {
        if (code == null || !maps.containsKey(code.intValue())){
            return false;
        }
        for (ConclusionTypeEnum conclusionType : conclusionTypes) {
            if(code.compareTo(conclusionType.code)==0){
                return true;
            }
        }
        return false;
    }

    public static boolean check(Integer code, ConclusionTypeEnum conclusionType) {
        if (code == null || !maps.containsKey(code.intValue())){
            return false;
        }
        return maps.get(code.intValue()) == conclusionType;
    }

    public boolean check(ConclusionTypeEnum... conclusionTypes) {
        if (conclusionTypes == null || conclusionTypes.length <= 0){
            return false;
        }
        for (ConclusionTypeEnum conclusionType: conclusionTypes) {
            if (conclusionType == null){
                continue;
            }
            if (this.getCode() == conclusionType.getCode()){
                return true;
            }
        }
        return false;
    }

    public boolean checkType(Integer conclusionType) {
        if (conclusionType == null){
            return false;
        }
        return this.getCode() == conclusionType;
    }

    public boolean checkDimType(ConclusionDimType currBizId) {
        return this.bizId.getCode() > 0 && this.bizId == currBizId;
    }

}
