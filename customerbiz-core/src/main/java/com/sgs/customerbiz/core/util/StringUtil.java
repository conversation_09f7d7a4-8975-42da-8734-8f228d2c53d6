package com.sgs.customerbiz.core.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ReflectionUtils;

import java.util.*;

/**
 * 继承自Spring util的工具类，减少jar依赖
 *
 * <AUTHOR>
 */
public class StringUtil extends StringUtils {

	public static final int INDEX_NOT_FOUND = -1;

	public static String isNullOrEmpty(String strVal){
		if (StringUtils.isBlank(strVal)){
			return null;
		}
		return strVal;
	}
	/**
	 *
	 * @param strValue
	 * @return
	 */
	public static int hashCode(final String strValue) {
		if (strValue == null || strValue.length() <= 0){
			return 0;
		}
		return strValue.hashCode();
	}

	/**
	 *
	 * @param intValue
	 * @return
	 */
	public static int hashCode(final Integer intValue) {
		if (intValue == null){
			return 0;
		}
		return intValue.intValue();
	}

	/**
	 *
	 * @param dateValue
	 * @return
	 */
	public static int hashCode(final Date dateValue) {
		if (dateValue == null){
			return 0;
		}
		return dateValue.hashCode();
	}

	/**
	 *
	 * @param strVals
	 * @return
	 */
	public static String getFirstVal(String... strVals){
		if (strVals == null || strVals.length <= 0){
			return null;
		}
		for (String strVal: strVals) {
			if (StringUtils.isNoneBlank(strVal)){
				return strVal;
			}
		}
		return null;
	}

	/**
	 *
	 * @param value
	 * @param maxLen
	 * @return
	 */
	public static boolean checkLen(String value, int maxLen){
		return charLen(value) > maxLen;
	}

	/**
	 *
	 * @param value
	 * @param minLen
	 * @param maxLen
	 * @return
	 */
	public static boolean checkLen(String value, int minLen, int maxLen){
		int currLen = length(value);
		return currLen >= minLen && currLen <= maxLen;
	}

	/**
	 *
	 * @param value
	 * @return
	 */
	public static int charLen(String value) {
		if (value == null || value.length() <= 0){
			return 0;
		}
		char[] charArr = value.toCharArray();
		int len = 0;
		for (int index = 0; index < charArr.length; index++) {
			len++;
			if (!isLetter(charArr[index])) {
				len++;
			}
		}
		return len;
	}

	public static boolean isLetter(char c) {
		int k = 0x80;
		return c / k == 0 ? true : false;
	}

	public static Map<String, Object> parseObj2Map(Object object) {
		if (object == null) {
			return null;
		}
		return Arrays.stream(BeanUtils.getPropertyDescriptors(object.getClass()))
				.filter(pd -> !"class".equals(pd.getName()))
				.collect(HashMap::new,
						(map, pd) -> map.put(pd.getName(), ReflectionUtils.invokeMethod(pd.getReadMethod(), object)),
						HashMap::putAll);
	}


}

