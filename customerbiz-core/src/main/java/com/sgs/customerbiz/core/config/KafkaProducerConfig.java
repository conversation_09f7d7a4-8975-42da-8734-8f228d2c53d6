package com.sgs.customerbiz.core.config;

import com.sgs.grus.kafka.client.KafkaClient;
import com.sgs.grus.kafka.client.KafkaConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.config.StreamsBuilderFactoryBean;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableKafka
public class KafkaProducerConfig {

    @Value("${kafka.bootstrap-servers}")
    private String brokers = "";

    @Value("${kafka.max-request-size}")
    private Integer maxRequestSize = 1048576;

    @Bean
    public ProducerFactory<String, String> producerFactory() {
        return new DefaultKafkaProducerFactory(producerProperties());
    }

    @Bean
    public Map<String, Object> producerProperties() {
        Map<String, Object> props = new HashMap<String, Object>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, brokers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, org.apache.kafka.common.serialization.StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, org.apache.kafka.common.serialization.StringSerializer.class);
        props.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG, maxRequestSize);
        return props;
    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<String, String>(producerFactory());
    }

    /**
     *
     * @return
     */
    public KafkaConsumerConfig kafkaConsumerConfig(){
        KafkaConsumerConfig kafkaConsumer = new KafkaConsumerConfig();
        kafkaConsumer.setBrokers(this.brokers);
        kafkaConsumer.setConcurrency(2);

        return kafkaConsumer;
    }

    /**
     *
     * @return
     */
    public KafkaClient kafkaClient(){
        KafkaClient kafkaClient = new KafkaClient();
        kafkaClient.setAppId("cnapp.sgs.net.customerbiz");
        kafkaClient.setAsyncSendMaxThreadNum(2);
        kafkaClient.setAsyncSendMaxQueueNum(10000);
        kafkaClient.setKafkaConsumerConfig(this.kafkaConsumerConfig());

        return kafkaClient;
    }
}
