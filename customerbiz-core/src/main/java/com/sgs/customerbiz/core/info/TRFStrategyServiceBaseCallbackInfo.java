package com.sgs.customerbiz.core.info;


import com.sgs.customerbiz.core.annotation.ExecutorCallback;

public class TRFStrategyServiceBaseCallbackInfo {

    private Class clazz;

    private ExecutorCallback callback;

    private Integer eventTypeCode;

    public Integer getEventTypeCode() {
        return eventTypeCode;
    }

    public void setEventTypeCode(Integer eventTypeCode) {
        this.eventTypeCode = eventTypeCode;
    }

    public Class getClazz() {
        return clazz;
    }

    public void setClazz(Class clazz) {
        this.clazz = clazz;
    }

    public ExecutorCallback getCallback() {
        return callback;
    }

    public void setCallback(ExecutorCallback callback) {
        this.callback = callback;
    }
}
