UPDATE  `tb_cfg_info` SET `config_value` = '{\"trfOrderRelationshipRule\":4,\"statusRule\":4,\"statusFlow\":[1,3,4,5,6,7,100],\"statusMapping\":{\"1\":1,\"3\":3,\"4\":4,\"5\":5,\"6\":6,\"7\":7,\"100\":100},\"actionStatusMapping\":{\"Import\":{\"orderRefRule\":0,\"orderOpType\":1,\"ctrlMapping\":{}},\"SyncToOrder\":{\"orderRefRule\":1,\"orderOpType\":1,\"ctrlMapping\":{\"3\":0}},\"SyncConfirmed\":{\"orderRefRule\":1,\"orderOpType\":7,\"ctrlMapping\":{\"4\":0}},\"SyncTesting\":{\"orderRefRule\":1,\"orderOpType\":1,\"ctrlMapping\":{\"5\":0}},\"SyncCompleted\":{\"orderRefRule\":1,\"orderOpType\":7,\"ctrlMapping\":{\"6\":0}},\"SyncClosed\":{\"orderRefRule\":1,\"orderOpType\":7,\"ctrlMapping\":{\"7\":0}},\"SyncPending\":{\"orderRefRule\":1,\"orderOpType\":7,\"ctrlMapping\":{\"1\":1,\"3\":3,\"4\":4,\"5\":5}},\"SyncUnPending\":{\"orderRefRule\":1,\"orderOpType\":7,\"ctrlMapping\":{\"1\":1,\"3\":3,\"4\":4,\"5\":5}},\"SyncUnBind\":{\"orderRefRule\":1,\"orderOpType\":2,\"ctrlMapping\":{\"3\":3,\"4\":4,\"5\":5}},\"SyncReviseReport\":{\"orderOpType\":7,\"ctrlMapping\":{\"6\":0,\"100\":0}},\"PreOrderReturnTrf\":{\"orderRefRule\":1,\"orderOpType\":3,\"ctrlMapping\":{\"1\":0}},\"CustomerReturnTrf\":{\"orderRefRule\":1,\"orderOpType\":3,\"ctrlMapping\":{\"1\":0}}}}' WHERE `id` = 3;
