DROP TABLE `tb_trf_attr_mapping`;
DROP TABLE `tb_trf_care_label_sample`;
DROP TABLE `tb_trf_file`;
DROP TABLE `tb_trf_lab`;
DROP TABLE `tb_trf_lab_contact`;
DROP TABLE `tb_trf_lab_lang`;
DROP TABLE `tb_trf_order_contact`;
DROP TABLE `tb_trf_order_relationship`;
DROP TABLE `tb_trf_product_sample`;
DROP TABLE `tb_trf_sample`;
DROP TABLE `tb_trf_sample_attr`;

ALTER TABLE `tb_api_request` MODIFY COLUMN `lab_code` varchar (20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'lab代码' AFTER `system_id`;

ALTER TABLE `tb_api_request` MODIFY COLUMN `request_id` varchar (100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '请求id' AFTER `lab_code`;

ALTER TABLE `tb_api_request` MODIFY COLUMN `response_status` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '响应状态码' AFTER `response_body`;

ALTER TABLE `tb_api_request` MODIFY COLUMN `created_by` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `response_status`;

ALTER TABLE `tb_api_request` MODIFY COLUMN `modified_by` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;


ALTER TABLE `tb_trf` DROP COLUMN `del_flag`;

ALTER TABLE `tb_trf` DROP COLUMN `trf_template_id`;

ALTER TABLE `tb_trf` DROP COLUMN `product_line_code`;

ALTER TABLE `tb_trf` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf` DROP COLUMN `modified_time`;

ALTER TABLE `tb_trf` MODIFY COLUMN `trf_no` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Trf 编号' AFTER `id`;

ALTER TABLE `tb_trf` MODIFY COLUMN `product_category` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '1：TX、2：FW' AFTER `service_type`;

ALTER TABLE `tb_trf` MODIFY COLUMN `source` int (11) NULL DEFAULT NULL COMMENT 'TRF来源：1 Online TRF，2 Order To TRF' AFTER `self_test_flag`;

ALTER TABLE `tb_trf` MODIFY COLUMN `channel` varchar (100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `source`;

ALTER TABLE `tb_trf` MODIFY COLUMN `integration_level` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '样品Level，1 客户提供Component  2、SGS 拆样' AFTER `system_id`;

ALTER TABLE `tb_trf`
    ADD COLUMN `sample_level` int(11) NULL DEFAULT NULL AFTER `integration_level`;

ALTER TABLE `tb_trf`
    ADD COLUMN `bu_id` int(11) NULL DEFAULT NULL AFTER `lab_code`;

ALTER TABLE `tb_trf`
    ADD COLUMN `bu_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `bu_id`;

ALTER TABLE `tb_trf` MODIFY COLUMN `trf_remark` varchar (1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `bu_code`;

ALTER TABLE `tb_trf` MODIFY COLUMN `pending_type` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `trf_remark`;

ALTER TABLE `tb_trf` MODIFY COLUMN `pending_flag` tinyint(4) NULL DEFAULT 0 COMMENT '0: activate, 1: pending' AFTER `pending_type`;

ALTER TABLE `tb_trf`
    ADD COLUMN `pending_remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Pending备注' AFTER `pending_flag`;

ALTER TABLE `tb_trf` MODIFY COLUMN `ext_fields` json NULL COMMENT '扩展信息json结构' AFTER `pending_remark`;

ALTER TABLE `tb_trf`
    ADD COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `ext_fields`;

ALTER TABLE `tb_trf`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf`
    ADD INDEX `idx_created_time`(`created_date`) USING BTREE;


ALTER TABLE `tb_trf_care_label` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf_care_label` DROP COLUMN `modified_time`;


ALTER TABLE `tb_trf_care_label` MODIFY COLUMN `test_sample_ids` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL AFTER `trf_id`;

ALTER TABLE `tb_trf_care_label` MODIFY COLUMN `care_label_seq` int (4) NULL DEFAULT NULL AFTER `select_country`;

ALTER TABLE `tb_trf_care_label` MODIFY COLUMN `cloud_id` varchar (500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `select_img_ids`;

ALTER TABLE `tb_trf_care_label` MODIFY COLUMN `care_label_file_path` varchar (500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `cloud_id`;

ALTER TABLE `tb_trf_care_label` MODIFY COLUMN `product_item_no` varchar (500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `care_label_file_path`;

ALTER TABLE `tb_trf_care_label`
    ADD COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `product_item_no`;

ALTER TABLE `tb_trf_care_label`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_care_label`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf_care_label`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_care_label`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_care_label`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;



ALTER TABLE `tb_trf_customer` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf_customer` DROP COLUMN `modified_time`;

ALTER TABLE `tb_trf_customer`
    ADD COLUMN `customer_instance_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `trf_id`;

ALTER TABLE `tb_trf_customer` MODIFY COLUMN `customer_address` varchar (500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `customer_name`;

ALTER TABLE `tb_trf_customer` MODIFY COLUMN `payment_term` varchar (100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `customer_address`;

ALTER TABLE `tb_trf_customer` MODIFY COLUMN `black_flag` tinyint(4) NULL DEFAULT NULL AFTER `payment_term`;

ALTER TABLE `tb_trf_customer`
    ADD COLUMN `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `black_flag`;

ALTER TABLE `tb_trf_customer`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_customer`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf_customer`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_customer`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_customer`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_customer`
    ADD INDEX `idx_created_time`(`created_date`) USING BTREE;

ALTER TABLE `tb_trf_customer_contact` DROP COLUMN `contact_id`;

ALTER TABLE `tb_trf_customer_contact` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf_customer_contact` DROP COLUMN `modified_time`;


ALTER TABLE `tb_trf_customer_contact`
    ADD COLUMN `trf_id` bigint(20) NULL DEFAULT NULL AFTER `id`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `boss_contact_id` bigint(20) NULL DEFAULT NULL AFTER `contact_address_id`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `boss_site_use_id` bigint(20) NULL DEFAULT NULL AFTER `boss_contact_id`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `contact_usage` tinyint(4) NULL DEFAULT NULL AFTER `boss_site_use_id`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `contact_region_account` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `contact_usage`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `contact_name` varchar (200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `contact_region_account`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `contact_email` varchar (300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `contact_name`;

ALTER TABLE `tb_trf_customer_contact` MODIFY COLUMN `contact_fax` varchar (128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `contact_telephone`;

ALTER TABLE `tb_trf_customer_contact`
    ADD COLUMN `responsible_team_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `contact_fax`;

ALTER TABLE `tb_trf_customer_contact`
    ADD COLUMN `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `responsible_team_code`;

ALTER TABLE `tb_trf_customer_contact`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_customer_contact`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf_customer_contact`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_customer_contact`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_customer_contact`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_customer_contact`
    ADD INDEX `idx_created_time`(`created_date`) USING BTREE;

ALTER TABLE `tb_trf_customer_contact`
    ADD INDEX `idx_trf_id`(`trf_id`) USING BTREE;


ALTER TABLE `tb_trf_customer_lang` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf_customer_lang` DROP COLUMN `modified_time`;


ALTER TABLE `tb_trf_customer_lang`
    ADD COLUMN `trf_id` bigint(20) NULL DEFAULT NULL AFTER `id`;

ALTER TABLE `tb_trf_customer_lang`
    ADD COLUMN `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `customer_address`;

ALTER TABLE `tb_trf_customer_lang`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_customer_lang`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf_customer_lang`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_customer_lang`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_customer_lang`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_customer_lang`
    ADD INDEX `idx_created_time`(`created_date`) USING BTREE;

ALTER TABLE `tb_trf_customer_lang`
    ADD INDEX `idx_trf_id`(`trf_id`) USING BTREE;


ALTER TABLE `tb_trf_invoice` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf_invoice` DROP COLUMN `modified_time`;


ALTER TABLE `tb_trf_invoice`
    ADD COLUMN `system_id` int(11) NULL DEFAULT NULL COMMENT '调用系统id' AFTER `trf_id`;

ALTER TABLE `tb_trf_invoice`
    ADD COLUMN `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `order_no`;

ALTER TABLE `tb_trf_invoice`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_invoice`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf_invoice`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_invoice`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_invoice`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_invoice`
    ADD INDEX `idx_created_time`(`created_date`) USING BTREE;



ALTER TABLE `tb_trf_product` DROP COLUMN `language_id`;

ALTER TABLE `tb_trf_product` DROP COLUMN `product_sample_id`;

ALTER TABLE `tb_trf_product` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf_product` DROP COLUMN `modified_time`;


ALTER TABLE `tb_trf_product` DROP INDEX `idx_template_id`;

ALTER TABLE `tb_trf_product` MODIFY COLUMN `template_id` varchar (50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'dff form id' AFTER `trf_id`;

ALTER TABLE `tb_trf_product`
    ADD COLUMN `object_type` tinyint(4) NOT NULL COMMENT '对象类型：1 Product,2 Sample' AFTER `template_id`;

ALTER TABLE `tb_trf_product`
    ADD COLUMN `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `object_type`;

ALTER TABLE `tb_trf_product`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `active_indicator`;

ALTER TABLE `tb_trf_product`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `created_date`;

ALTER TABLE `tb_trf_product`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `created_by`;

ALTER TABLE `tb_trf_product`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `modified_date`;

ALTER TABLE `tb_trf_product`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_product`
    ADD INDEX `idx_created_time`(`created_date`) USING BTREE;


ALTER TABLE `tb_trf_product_attr` DROP COLUMN `seq`;

ALTER TABLE `tb_trf_product_attr` DROP COLUMN `value`;

ALTER TABLE `tb_trf_product_attr` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf_product_attr` DROP COLUMN `modified_time`;


ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `trf_id` bigint(20) NULL DEFAULT NULL AFTER `id`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `trf_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `trf_id`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `product_instance_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `trf_product_id`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `template_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `product_instance_id`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `object_type` tinyint(4) NOT NULL COMMENT '对象类型：1 Product,2 Sample' AFTER `template_id`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `language_id` int(11) NULL DEFAULT NULL AFTER `object_type`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `attr_seq` int(11) NULL DEFAULT NULL AFTER `language_id`;

ALTER TABLE `tb_trf_product_attr` MODIFY COLUMN `label_code` varchar (200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `attr_seq`;

ALTER TABLE `tb_trf_product_attr` MODIFY COLUMN `label_name` varchar (200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `label_code`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `label_value` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `label_name`;

ALTER TABLE `tb_trf_product_attr` MODIFY COLUMN `customer_label` varchar (200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `field_code`;

ALTER TABLE `tb_trf_product_attr` MODIFY COLUMN `data_type` varchar (200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `customer_label`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `active_indicator` int(1) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `data_type`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_product_attr`
    ADD COLUMN `last_modified_timestamp` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_product_attr`
    ADD INDEX `idx_created_time`(`created_date`) USING BTREE;

ALTER TABLE `tb_trf_product_attr`
    ADD INDEX `idx_trf_no`(`trf_no`) USING BTREE;



ALTER TABLE `tb_trf_quotation` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf_quotation` DROP COLUMN `modified_time`;


ALTER TABLE `tb_trf_quotation`
    ADD COLUMN `system_id` int(11) NULL DEFAULT NULL COMMENT '调用系统id' AFTER `trf_id`;

ALTER TABLE `tb_trf_quotation`
    ADD COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `order_no`;

ALTER TABLE `tb_trf_quotation`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_quotation`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf_quotation`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_quotation`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_quotation`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_quotation`
    ADD INDEX `idx_created_time`(`created_date`) USING BTREE;

ALTER TABLE `tb_trf_report` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf_report` DROP COLUMN `modified_time`;


ALTER TABLE `tb_trf_report`
    ADD COLUMN `system_id` int(11) NULL DEFAULT NULL COMMENT '调用系统id' AFTER `trf_id`;

ALTER TABLE `tb_trf_report`
    ADD COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `report_no`;

ALTER TABLE `tb_trf_report`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_report`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf_report`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_report`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_report`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_report`
    ADD INDEX `idx_created_time`(`created_date`) USING BTREE;



ALTER TABLE `tb_trf_service_requirement` DROP COLUMN `invoice_vat_type`;

ALTER TABLE `tb_trf_service_requirement` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf_service_requirement` DROP COLUMN `modified_time`;


ALTER TABLE `tb_trf_service_requirement`
    ADD COLUMN `invoice_type` tinyint(4) NULL DEFAULT NULL AFTER `need_photo`;

ALTER TABLE `tb_trf_service_requirement` MODIFY COLUMN `sample_save_duration` int (11) NULL DEFAULT NULL AFTER `invoice_type`;

ALTER TABLE `tb_trf_service_requirement`
    ADD COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `other_request_remark`;

ALTER TABLE `tb_trf_service_requirement`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_service_requirement`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf_service_requirement`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_service_requirement`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_service_requirement`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_service_requirement`
    ADD INDEX `idx_created_time`(`created_date`) USING BTREE;


ALTER TABLE `tb_trf_service_requirement_delivery` DROP COLUMN `to`;

ALTER TABLE `tb_trf_service_requirement_delivery` DROP COLUMN `others`;

ALTER TABLE `tb_trf_service_requirement_delivery` DROP COLUMN `cc`;

ALTER TABLE `tb_trf_service_requirement_delivery` DROP COLUMN `way`;

ALTER TABLE `tb_trf_service_requirement_delivery` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf_service_requirement_delivery` DROP COLUMN `modified_time`;


ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD COLUMN `trf_service_requirement_id` bigint(20) NULL DEFAULT NULL AFTER `trf_id`;

ALTER TABLE `tb_trf_service_requirement_delivery` MODIFY COLUMN `required` tinyint(4) NULL DEFAULT NULL AFTER `delivery_type`;

ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD COLUMN `delivery_to` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `required`;

ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD COLUMN `delivery_others` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `delivery_to`;

ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD COLUMN `delivery_cc` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `delivery_others`;

ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD COLUMN `delivery_way` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `delivery_cc`;

ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD COLUMN `active_indicator` int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `delivery_way`;

ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD INDEX `idx_created_time`(`created_date`) USING BTREE;

ALTER TABLE `tb_trf_service_requirement_delivery`
    ADD INDEX `idx_service_requirement_id`(`trf_service_requirement_id`) USING BTREE;


ALTER TABLE `tb_trf_service_requirement_lang` DROP COLUMN `report_language`;

ALTER TABLE `tb_trf_service_requirement_lang` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf_service_requirement_lang` DROP COLUMN `modified_time`;


ALTER TABLE `tb_trf_service_requirement_lang`
    ADD COLUMN `trf_id` bigint(20) NULL DEFAULT NULL AFTER `id`;

ALTER TABLE `tb_trf_service_requirement_lang`
    ADD COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `report_address`;

ALTER TABLE `tb_trf_service_requirement_lang`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_service_requirement_lang`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf_service_requirement_lang`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_service_requirement_lang`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_service_requirement_lang`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_service_requirement_lang`
    ADD INDEX `idx_created_time`(`created_date`) USING BTREE;

ALTER TABLE `tb_trf_service_requirement_lang`
    ADD INDEX `idx_trf_id`(`trf_id`) USING BTREE;


ALTER TABLE `tb_trf_test_item` DROP COLUMN `test_line_name`;

ALTER TABLE `tb_trf_test_item` DROP COLUMN `citation_name`;

ALTER TABLE `tb_trf_test_item` DROP COLUMN `external_test_standard_id`;

ALTER TABLE `tb_trf_test_item` DROP COLUMN `external_test_standard_name`;

ALTER TABLE `tb_trf_test_item` DROP COLUMN `created_time`;

ALTER TABLE `tb_trf_test_item` DROP COLUMN `modified_time`;


ALTER TABLE `tb_trf_test_item` MODIFY COLUMN `test_line_id` int (11) NULL DEFAULT NULL AFTER `trf_id`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `evaluation_alias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `test_line_id`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `evaluation_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `evaluation_alias`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `test_line_seq` int(11) NULL DEFAULT NULL AFTER `evaluation_name`;

ALTER TABLE `tb_trf_test_item` MODIFY COLUMN `pp_no` int (11) NULL DEFAULT NULL AFTER `test_line_seq`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `pp_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `pp_no`;

ALTER TABLE `tb_trf_test_item` MODIFY COLUMN `citation_id` int (11) NULL DEFAULT NULL AFTER `pp_name`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `citation_full_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `citation_type`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `external_test_citation_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `external_test_item_name`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `external_test_citation_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL AFTER `external_test_citation_id`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `external_check_type` int(11) NULL DEFAULT NULL AFTER `external_test_citation_name`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `active_indicator` tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active' AFTER `external_check_type`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `created_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `active_indicator`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `created_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `created_by`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `modified_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人' AFTER `created_date`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `modified_date` datetime(0) NULL DEFAULT NULL COMMENT '修改时间' AFTER `modified_by`;

ALTER TABLE `tb_trf_test_item`
    ADD COLUMN `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '最近修改时间' AFTER `modified_date`;

ALTER TABLE `tb_trf_test_item`
    ADD INDEX `idx_created_time`(`created_date`) USING BTREE;

CREATE TABLE `tb_trf_attachment`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT,
    `trf_id`                  bigint(20) NULL DEFAULT NULL,
    `object_type`             tinyint(4) NULL DEFAULT NULL,
    `object_id`               bigint(20) NULL DEFAULT NULL,
    `file_name`               varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `file_path`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `file_type`               int(4) NULL DEFAULT NULL,
    `file_size`               bigint(20) NULL DEFAULT NULL,
    `language_id`             int(11) NULL DEFAULT NULL,
    `active_indicator`        int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                     `idx_created_time`(`created_date`) USING BTREE,
    INDEX                     `idx_object_id_type`(`object_id`, `object_type`) USING BTREE,
    INDEX                     `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE `tb_trf_order`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT,
    `trf_id`                  bigint(20) NULL DEFAULT NULL,
    `system_id`               int(11) NULL DEFAULT NULL COMMENT '归属系统id',
    `order_id`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `order_no`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `order_status`            int(11) NULL DEFAULT NULL,
    `bound_status`            int(11) NOT NULL DEFAULT 0 COMMENT 'Trf绑定状态（1：已绑定、2：已解绑）',
    `active_indicator`        int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                     `idx_created_time`(`created_date`) USING BTREE,
    INDEX                     `idx_trf_id`(`trf_id`) USING BTREE,
    INDEX                     `idx_order_no`(`system_id`, `order_no`, `bound_status`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `tb_trf_test_item_lang`
(
    `id`                          bigint(20) NOT NULL AUTO_INCREMENT,
    `trf_id`                      bigint(20) NULL DEFAULT NULL,
    `trf_test_item_id`            bigint(20) NULL DEFAULT NULL,
    `language_id`                 int(11) NULL DEFAULT NULL,
    `evaluation_alias`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `evaluation_name`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `pp_name`                     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `citation_full_name`          varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `external_test_item_name`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `external_test_citation_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `active_indicator`            tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`                datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`               datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
    `last_modified_timestamp`     datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                         `idx_created_time`(`created_date`) USING BTREE,
    INDEX                         `idx_trf_id`(`trf_id`) USING BTREE,
    INDEX                         `idx_trf_test_item_id`(`trf_test_item_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `tb_trf_test_sample`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT,
    `trf_id`                  bigint(20) NULL DEFAULT NULL,
    `test_sample_instance_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `parent_test_sample_id`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `test_sample_no`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `external_sample_no`      varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `test_sample_type`        int(11) NULL DEFAULT NULL,
    `test_sample_seq`         int(11) NULL DEFAULT NULL,
    `active_indicator`        tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                     `idx_created_time`(`created_date`) USING BTREE,
    INDEX                     `idx_trf_id`(`trf_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `tb_trf_test_sample_file`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT,
    `trf_id`                  bigint(20) NULL DEFAULT NULL,
    `trf_test_sample_id`      bigint(20) NULL DEFAULT NULL,
    `file_name`               varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `file_path`               varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `file_type`               tinyint(4) NULL DEFAULT NULL,
    `file_size`               bigint(20) NULL DEFAULT NULL,
    `language_id`             int(11) NULL DEFAULT NULL,
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                     `idx_created_time`(`created_date`) USING BTREE,
    INDEX                     `idx_trf_id`(`trf_id`) USING BTREE,
    INDEX                     `idx_trf_test_sample_id`(`trf_test_sample_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;



CREATE TABLE `tb_trf_test_sample_group`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT,
    `trf_id`                  bigint(20) NULL DEFAULT NULL,
    `trf_test_sample_id`      bigint(20) NULL DEFAULT NULL,
    `test_sample_instance_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '测试样实例id',
    `main_sample_flag`        tinyint(4) NULL DEFAULT 0,
    `active_indicator`        tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                     `idx_created_time`(`created_date`) USING BTREE,
    INDEX                     `idx_trf_id`(`trf_id`) USING BTREE,
    INDEX                     `idx_trf_test_sample_id`(`trf_test_sample_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;



CREATE TABLE `tb_trf_test_sample_material`
(
    `id`                         bigint(20) NOT NULL AUTO_INCREMENT,
    `trf_id`                     bigint(20) NULL DEFAULT NULL,
    `trf_test_sample_id`         bigint(20) NULL DEFAULT NULL,
    `material_composition`       varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `material_description`       varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `material_end_use`           varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `material_name`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `material_color`             varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `material_texture`           varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `material_other_sample_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `material_applicable_flag`   tinyint(4) NULL DEFAULT NULL,
    `material_sample_remark`     varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `material_category`          varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `material_sku`               varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `material_item`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `active_indicator`           tinyint(4) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`               datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `modified_by`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`              datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
    `last_modified_timestamp`    datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                        `idx_created_time`(`created_date`) USING BTREE,
    INDEX                        `idx_trf_id`(`trf_id`) USING BTREE,
    INDEX                        `idx_trf_test_sample_id`(`trf_test_sample_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;



CREATE TABLE `tb_dict_value`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `system_id`               int(11) NOT NULL COMMENT '归属系统',
    `dict_type`               varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典类型',
    `dict_value`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典值',
    `dict_label`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '字典显示名称',
    `active_indicator`        int(1) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                     `idx_dict_type`(`system_id`, `dict_type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `tb_dfv_field_constraint`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `system_id`               int(11) NOT NULL COMMENT '归属系统id',
    `function_id`             bigint(20) NOT NULL COMMENT '功能id',
    `field_id`                bigint(20) NOT NULL COMMENT '属性id',
    `field_path`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `type`                    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '约束类型',
    `constraint_value1`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '约束条件值1',
    `constraint_value2`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '约束条件值2',
    `sort_num`                int(11) NOT NULL COMMENT '排序',
    `field_id_ref`            bigint(20) NOT NULL COMMENT '关联属性：例如A属性的值要大于B属性值的约束时，需要指定B属性的属性id',
    `active_indicator`        int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uidx_func_field_constraint_type`(`function_id`, `field_id`, `type`) USING BTREE,
    INDEX                     `idx_created_date`(`created_date`) USING BTREE,
    INDEX                     `idx_func_id`(`system_id`, `function_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DFV-功能-入参属性规则' ROW_FORMAT = Dynamic;

CREATE TABLE `tb_dfv_func_field`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `system_id`               int(11) NOT NULL COMMENT '归属系统id',
    `function_id`             bigint(20) NOT NULL COMMENT '所属功能id',
    `full_field_path`         varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性全路径，与field_path不同，当对象tree某个节点出现集合类型时，full_field_path会描述整个路径，而field path只从集合的item开始，例如\r\ntrf.orderList.orderNo，fullFieldPath为trf.orderList[].orderNo，而fieldPath为orderNo',
    `field_path`              varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性路径，由英文逗号链接的对象树父子关系，例如trf.order.lab.labCode',
    `field_code`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性code',
    `field_type`              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '属性类型：\r\ncollection，string，integer，long，digits，object, boolean,  date',
    `label_name`              varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性名称',
    `sort_num`                int(11) NOT NULL COMMENT '排序',
    `parent_id`               bigint(20) NOT NULL COMMENT '父属性id，0为无父属性，其他表示父属性id',
    `active_indicator`        int(1) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                     `uidx_func_field`(`system_id`, `function_id`, `field_code`) USING BTREE,
    INDEX                     `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DFV-功能-入参属性' ROW_FORMAT = Dynamic;

CREATE TABLE `tb_dfv_function`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `system_id`               int(11) NOT NULL COMMENT '归属系统id',
    `function_code`           varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '功能代码',
    `description`             varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '功能描述',
    `active_indicator`        int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uidx_sys_func`(`system_id`, `function_code`) USING BTREE,
    INDEX                     `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DFV-功能' ROW_FORMAT = Dynamic;


CREATE TABLE `tb_dfv_template`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `system_id`               int(11) NOT NULL COMMENT '归属系统id',
    `template_code`           varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板代码',
    `function_id`             bigint(20) NOT NULL COMMENT '归属功能id',
    `description`             varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板功能描述',
    `active_indicator`        int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `idx_sys_tpl`(`system_id`, `template_code`) USING BTREE,
    INDEX                     `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DFV-验证实例-模板' ROW_FORMAT = Dynamic;


CREATE TABLE `tb_dfv_template_field`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `template_id`             bigint(20) NOT NULL COMMENT '模板id',
    `field_id`                bigint(20) NOT NULL COMMENT '属性id',
    `field_path`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `sort_num`                int(11) NOT NULL COMMENT '排序',
    `parent_id`               bigint(20) NOT NULL COMMENT '父属性id，0为无父属性，其他表示父属性id',
    `active_indicator`        int(1) NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                     `idx_template_id`(`template_id`) USING BTREE,
    INDEX                     `idx_created_date`(`created_date`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DFV-验证实例-模板属性' ROW_FORMAT = Dynamic;


CREATE TABLE `tb_dfv_template_field_constraint`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `template_id`             bigint(20) NOT NULL COMMENT '模板id',
    `template_field_id`       bigint(20) NOT NULL COMMENT '模板属性id',
    `field_id`                bigint(20) NOT NULL COMMENT '属性id',
    `field_path`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
    `type`                    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '约束类型',
    `constraint_value1`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '约束条件值1',
    `constraint_value2`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '约束条件值2',
    `field_id_ref`            bigint(20) NOT NULL COMMENT '关联属性：例如A属性的值要大于B属性值的约束时，需要指定B属性的属性id',
    `active_indicator`        int(1) NOT NULL DEFAULT 1 COMMENT '有效无效标记：0: inactive, 1: active',
    `sort_num`                int(11) NOT NULL COMMENT '排序',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NOT NULL COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                     `idx_created_date`(`created_date`) USING BTREE,
    INDEX                     `idx_template_id`(`template_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'DFV-验证实例-模板属性规则' ROW_FORMAT = Dynamic;



CREATE TABLE `tb_task_info`
(
    `id`                bigint(20) NOT NULL COMMENT '主键',
    `task_id`           bigint(20) NOT NULL COMMENT '任务id',
    `ext_id`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '外部业务id,可重复',
    `group_key`         varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '同一个extId下的分组key,可重复',
    `handler_name`      varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务处理器名称',
    `task_parameters`   text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '任务参数',
    `depend_task`       bigint(20) NULL DEFAULT NULL COMMENT '前置taskId',
    `expired_time`      bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '过期时间，时间戳 0表示不过期',
    `task_status`       tinyint(3) UNSIGNED NULL DEFAULT 0 COMMENT '请求状态 0:初始化 1:执行中  2:成功 3:失败 4:取消 9:过期',
    `fail_count`        int(10) UNSIGNED NULL DEFAULT NULL COMMENT '请求失败次数',
    `max_fail`          int(10) UNSIGNED NULL DEFAULT 0 COMMENT '最大允许失败次数',
    `retry_count`       int(10) UNSIGNED NULL DEFAULT 0 COMMENT '重试次数',
    `max_retry`         int(10) UNSIGNED NULL DEFAULT 0 COMMENT '最大允许重试次数',
    `retry_time_millis` bigint(20) UNSIGNED NULL DEFAULT 180000 COMMENT '重试间隔时间,毫秒',
    `last_exec_time`    bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '最后执行时间',
    `created_by`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '创建人',
    `created_date`      datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`     datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_taskId`(`task_id`) USING BTREE,
    INDEX               `idx_createdDate`(`created_date`) USING BTREE,
    INDEX               `idx_extId`(`ext_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;


CREATE TABLE `tb_task_execute_log`
(
    `id`              bigint(20) NOT NULL,
    `task_id`         bigint(20) NOT NULL COMMENT '任务id',
    `group_key`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '分组key',
    `ext_id`          varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '外部id',
    `handler_name`    varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '任务处理器名称',
    `task_parameters` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '任务参数',
    `execute_result`  varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行结果',
    `execute_ip`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行节点ip',
    `created_by`      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci   NOT NULL COMMENT '创建人',
    `created_date`    datetime(0) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;



-- ----------------------------
-- Table structure for tb_cfg_event_subscribe
-- ----------------------------
CREATE TABLE `tb_cfg_event_subscribe`
(
    `id`                      bigint(20) NOT NULL COMMENT '主键',
    `system_id`               int(11) NOT NULL COMMENT '系统标识',
    `event_code`              int(11) NOT NULL COMMENT '事件code',
    `api_id`                  bigint(20) NOT NULL COMMENT '对应触发api的id',
    `notify_rule`             int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '事件通知规则 低位开始，第一位：多次发送标识，第二位：顺序发送标识，第三位：trf允许合并发送标识',
    `notify_data`             int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '事件触发时需要发生给客户的数据 0:不需要 ，低位开始，第一位：Invoice，第二位：quotation，第三位：。。。具体参见标准对象定义',
    `created_by`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_refSysId_eventCode_apiId`(`system_id`, `event_code`, `api_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_cfg_info
-- ----------------------------
CREATE TABLE `tb_cfg_info`
(
    `id`                      bigint(20) NOT NULL COMMENT '主键',
    `csutomer_group`          varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置方分组',
    `customer_no`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置方编号',
    `identity_id`             varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '配置方身份标识',
    `product_line`            varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'BU',
    `config_type`             int(10) UNSIGNED NOT NULL COMMENT '配置参数类型 1.技术参数配置 2.业务参数配置',
    `config_key`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置key,如：ORDER_TRF_REL、TRF_CHANGE_MODE、TRF_UPDATE_LIMIT',
    `config_value`            varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置值',
    `created_by`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_cfg_support_system
-- ----------------------------
CREATE TABLE `tb_cfg_support_system`
(
    `id`                      bigint(20) NOT NULL COMMENT '主键',
    `system_id`               int(11) NOT NULL COMMENT '系统标识',
    `system_name`             varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户名称',
    `system_type`             tinyint(4) NULL DEFAULT NULL COMMENT '系统类别  1.客户系统 2.执行系统',
    `system_no`               varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户编号',
    `system_group_code`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户分组编码',
    `system_desc`             varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '对客户的描述',
    `created_by`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE INDEX `uk_refSystemId`(`system_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_cfg_system_api
-- ----------------------------
CREATE TABLE `tb_cfg_system_api`
(
    `id`                      bigint(20) NOT NULL COMMENT '主键',
    `system_id`               int(11) NOT NULL COMMENT '系统标识',
    `protocol_type`           tinyint(4) UNSIGNED NOT NULL DEFAULT 1 COMMENT '协议类型 1.http 2.queue',
    `request_url`             varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求地址',
    `request_method`          tinyint(4) UNSIGNED NULL DEFAULT NULL COMMENT '请求方法 1.get 2.post ',
    `request_body_template`   text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求数据的模板',
    `response_body_template`  varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '返回数据的模板',
    `created_by`              varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
    `created_date`            datetime(0) NOT NULL COMMENT '创建时间',
    `modified_by`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
    `modified_date`           datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
    `last_modified_timestamp` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP (0) COMMENT '最近修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

