UPDATE `tb_cfg_system_api` SET `request_body_template` = '{\r\n    \"action\":\"SyncOrderStatus\",\r\n    \"dataStatus\":\"ToOrder\",\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":10016,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"WTDH\":\"$.trfList[0].trfNo\",\r\n        \"STATUS\":\"接样\",\r\n        \"Time\":\"#toDate($.extra.operationTime)\",\r\n        \"REMARK\":\"-\"\r\n    }\r\n}' WHERE `id` = 16;


UPDATE `tb_cfg_system_api` SET `request_body_template` = '{\r\n    \"action\":\"SyncOrderStatus\",\r\n    \"dataStatus\":\"Confirmed\",\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":10016,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"WTDH\":\"$.trfList[0].trfNo\",\r\n        \"STATUS\":\"受理\",\r\n        \"Time\":\"#toDate($.extra.operationTime)\",\r\n        \"REMARK\":\"-\"\r\n    }\r\n}' WHERE `id` = 17;


UPDATE `tb_cfg_system_api` SET `request_body_template` = '{\r\n    \"action\":\"SyncOrderStatus\",\r\n    \"dataStatus\":\"Testing\",\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":10016,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"WTDH\":\"$.trfList[0].trfNo\",\r\n        \"STATUS\":\"检测中\",\r\n        \"Time\":\"#toDate($.extra.operationTime)\",\r\n        \"REMARK\":\"-\"\r\n    }\r\n}', `response_body_template` = '{}', `remark` = 'AFL 状态同步接口（Testing）', `created_by` = 'YANG', `created_date` = '2023-09-08 13:42:12', `modified_by` = 'YANG', `modified_date` = '2023-09-08 13:44:20', `last_modified_timestamp` = '2023-12-04 14:55:00' WHERE `id` = 18;

UPDATE `tb_cfg_system_api` SET `request_body_template` = '{\r\n    \"action\":\"SyncOrderStatus\",\r\n    \"dataStatus\":\"Completed\",\r\n    \"bu\":\"$.extra.productLineCode\",\r\n    \"objectNumber\":\"$.trfList[0].trfNo\",\r\n    \"refSystemId\":10016,\r\n    \"msgId\":\"$.extra.randomSequence\",\r\n    \"body\":{\r\n        \"WTDH\":\"$.trfList[0].trfNo\",\r\n        \"STATUS\":\"报告出具\",\r\n        \"Time\":\"#toDate($.extra.operationTime)\",\r\n        \"REMARK\":\"-\"\r\n    }\r\n}' WHERE `id` = 19;


UPDATE `tb_cfg_system_api` SET `request_body_template` = '{\r\n  \"_reports\":[\r\n    {\r\n		\"_reportNo\": \"$.reportList[*].reportNo\",\r\n      \"data\": [\r\n        {\r\n				\"_reportNo\": \"@_reportNo\",\r\n					\"_testSampleInstanceId\":\"$.reportList[reportNo=@._reportNo].reportMatrixList[*].testSampleInstanceId\",\r\n					\r\n          \"G_ID\": \"$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].external.testSampleId\",\r\n          \"WTDH\": \"$.trfList[0].trfNo\",\r\n          \"SFSC\": \"$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].testSampleNo\",\r\n          \"SC\": \"$.testSampleList[testSampleInstanceId=@._testSampleInstanceId][0].external.testSampleNo\",\r\n          \"PDFTIME\": \"#toDate($.reportList[reportNo=@._reportNo].softCopyDeliveryDate[0],\'yyyy-MM-dd\')\",\r\n          \"VER\": \"#yiliReportDeliveryCounter($.extra.refSystemId,$.extra.trfNo)\",\r\n          \"YPBMWD\": \"-\",\r\n          \"PDFFILES\": [\r\n            {\r\n              \"PDFNAME\": \"$.reportList[reportNo = @_reportNo].reportFileList[*].fileName\",\r\n              \"PDFSTRING\": null,\r\n              \"PDFURI\": \"$.reportList[reportNo = @_reportNo].reportFileList[*].cloudId\"\r\n            }\r\n          ],\r\n          \"PADATA\": [\r\n            {\r\n              \"G_ID\": \"$.reportList[reportNo = @_reportNo].reportMatrixList[*].testMatrixId\",\r\n              \"SC\": null,\r\n							\"_testLineInstanceId\":\"$.reportList[reportNo=@_reportNo].reportMatrixList[*].testLineInstanceId\",\r\n              \"PA\": \"$.orderList[0].testItemMappingList[testLineInstanceId =@._testLineInstanceId].external.testItemId[0]\",\r\n              \"PANAME\": \"$.orderList[0].testItemMappingList[testLineInstanceId =@._testLineInstanceId].external.testItemName[0]\",\r\n              \"UNIT\": \"$.testResultList[testMatrixId = @.G_ID].testResult.resultUnit[0]\",\r\n              \"VALUE_S\": \"$.testResultList[testMatrixId = @.G_ID].testResult.resultValue[0]\",\r\n              \"CONCLUSION\": \"$.reportList[reportNo =@_reportNo][0].reportMatrixList[testMatrixId = @.G_ID][0].conclusion.customerConclusion\",\r\n              \"METHOD\": \"$.testLineList[testLineInstanceId = @._testLineInstanceId][0].citation.languageList[0].citationFullName\",\r\n              \"METHODNAME\": \"$.testLineList[testLineInstanceId = @._testLineInstanceId][0].citation.languageList[0].citationFullName\",\r\n              \"LODORLOQ\": \"$.testResultList[testMatrixId = @.G_ID].methodLimit.limitType[0]\",\r\n              \"LIMITVALUE\": \"$.testResultList[testMatrixId = @.G_ID].methodLimit.limitValueFullName[0]\",\r\n              \"TESTTIME\": \"#toDate($.orderList[0].testingStartDate)\",\r\n              \"TESTENDTIME\": \"#toDate($.orderList[0].testingEndDate)\",\r\n              \"EQNAME\": null,\r\n              \"SYTJ\": null,\r\n              \"STATUS\": null,\r\n              \"BZYQ\": \"$.testResultList[testMatrixId = @.G_ID].reportLimit.limitValueFullName[0]\",\r\n              \"HighLimit\": null,\r\n              \"LowLimit\": null\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  ] ,\r\n  \"_buildReportsNode\": \"#buildVirtualNode(\'_reports\',@._reports)\",\r\n  \"action\": \"SyncReport\",\r\n  \"dataStatus\": \"Completed\",\r\n  \"bu\": \"$.extra.productLineCode\",\r\n  \"objectNumber\": \"$.extra.trfNo\",\r\n  \"refSystemId\": \"$.extra.refSystemId\",\r\n  \"msgId\": \"$.extra.randomSequence\",\r\n  \"body\": {\r\n    \"reports\": \"$._reports[0].data\"\r\n  }\r\n}' WHERE `id` = 20;
