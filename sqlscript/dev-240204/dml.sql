INSERT INTO `tb_cfg_info`(`id`, `csutomer_group`, `customer_no`, `identity_id`, `product_line`, `config_type`,
                          `config_key`, `config_value`, `created_by`, `created_date`, `modified_by`, `modified_date`,
                          `last_modified_timestamp`)
VALUES (37, NULL, NULL, '10019', 'SL', 2, 'REFSYSTEM.API.IMPORT', '33', 'system', CURRENT_TIMESTAMP,
        'system', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);


UPDATE tb_trf SET pending_type =
    CASE pending_type
        WHEN 1 THEN 'Waiting for payment'
        when 2 then 'Waiting for client information/confirmation'
        when 3 then 'Client requested to hold'
        when 4 then 'Waiting for additional sample'
        when 5 then 'Other special reasons,pls contact with CS'
        ELSE pending_type END;

UPDATE tb_trf_order SET pending_type =
                      CASE pending_type
                          WHEN 1 THEN 'Waiting for payment'
                          when 2 then 'Waiting for client information/confirmation'
                          when 3 then 'Client requested to hold'
                          when 4 then 'Waiting for additional sample'
                          when 5 then 'Other special reasons,pls contact with CS'
                          ELSE pending_type END;

UPDATE tb_trf_order_log SET pending_type =
                      CASE pending_type
                          WHEN 1 THEN 'Waiting for payment'
                          when 2 then 'Waiting for client information/confirmation'
                          when 3 then 'Client requested to hold'
                          when 4 then 'Waiting for additional sample'
                          when 5 then 'Other special reasons,pls contact with CS'
                          ELSE pending_type END;

