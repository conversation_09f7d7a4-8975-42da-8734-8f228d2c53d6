INSERT INTO sgs_todolistdb.tb_cfg_info (id, csutomer_group, customer_no, identity_id, product_line, config_type,
                                        config_key, config_value, created_by, created_date, modified_by, modified_date,
                                        last_modified_timestamp)
VALUES (168, null, null, '0', '', 1, 'RefSystemIdSetOfUsingSGSTrfNo', '[10018]', 'admin', '2024-03-07 18:55:22',
        'admin', '2024-03-07 18:55:22', '2024-04-26 10:56:00');
DELETE
FROM sgs_todolistdb.tb_cfg_event_subscribe
WHERE id = 52;

INSERT INTO sgs_todolistdb.tb_cfg_event_subscribe (id, subscriber, ref_system_id, event_code, api_id, priority, notify_rule, notify_data, `condition`, condition_params, handler_name, error_handle, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (150, 10002, 10018, 10, 14, 0, 0, 2023, null, null, 'messageNotifyHand<PERSON>', '', 'Walley', '2023-09-18 14:07:29', 'Walley', '2023-09-18 14:07:29', '2023-12-27 13:38:05');
INSERT INTO sgs_todolistdb.tb_cfg_event_subscribe (id, subscriber, ref_system_id, event_code, api_id, priority, notify_rule, notify_data, `condition`, condition_params, handler_name, error_handle, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (151, 10002, 10018, 40, 14, 0, 0, 2023, null, null, 'messageNotifyHandler', '', 'Walley', '2023-09-18 14:07:29', 'Walley', '2023-09-18 14:07:29', '2023-12-27 13:38:05');
INSERT INTO sgs_todolistdb.tb_cfg_event_subscribe (id, subscriber, ref_system_id, event_code, api_id, priority, notify_rule, notify_data, `condition`, condition_params, handler_name, error_handle, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (152, 10002, 10018, 60, 14, 0, 0, 2023, null, null, 'messageNotifyHandler', '', 'XQW', '2024-03-13 14:50:12', 'XQW', '2024-03-13 14:51:21', '2024-03-13 14:51:21');
INSERT INTO sgs_todolistdb.tb_cfg_event_subscribe (id, subscriber, ref_system_id, event_code, api_id, priority, notify_rule, notify_data, `condition`, condition_params, handler_name, error_handle, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (153, 10002, 10018, 90, 14, 0, 0, 2023, null, null, 'messageNotifyHandler', '', 'Walley', '2023-09-18 14:07:31', 'Walley', '2023-09-18 14:07:31', '2023-12-27 13:38:05');
INSERT INTO sgs_todolistdb.tb_cfg_event_subscribe (id, subscriber, ref_system_id, event_code, api_id, priority, notify_rule, notify_data, `condition`, condition_params, handler_name, error_handle, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (154, 10002, 10018, 100, 14, 0, 0, 2023, null, null, 'messageNotifyHandler', '', 'Walley', '2023-09-18 14:07:32', 'Walley', '2023-09-18 14:07:32', '2023-12-27 13:38:05');
INSERT INTO sgs_todolistdb.tb_cfg_event_subscribe (id, subscriber, ref_system_id, event_code, api_id, priority, notify_rule, notify_data, `condition`, condition_params, handler_name, error_handle, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (155, 10002, 10018, 110, 14, 0, 0, 2023, null, null, 'unPendingHandler', '', 'Walley', '2023-09-18 14:07:33', 'Walley', '2023-09-18 14:07:33', '2023-12-27 13:38:05');
INSERT INTO sgs_todolistdb.tb_cfg_event_subscribe (id, subscriber, ref_system_id, event_code, api_id, priority, notify_rule, notify_data, `condition`, condition_params, handler_name, error_handle, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (156, 10002, 10018, 3003, 14, 0, 0, 2023, null, null, 'messageNotifyHandler', '', 'Walley', '2023-09-18 14:07:30', 'XQW', '2023-09-18 14:07:30', '2023-12-27 13:38:05');
INSERT INTO sgs_todolistdb.tb_cfg_event_subscribe (id, subscriber, ref_system_id, event_code, api_id, priority, notify_rule, notify_data, `condition`, condition_params, handler_name, error_handle, created_by, created_date, modified_by, modified_date, last_modified_timestamp) VALUES (157, 10002, 10018, 3004, 14, 0, 0, 2023, null, null, 'messageNotifyHandler', '', 'XQW', '2024-06-13 13:05:47', 'XQW', '2024-06-13 13:06:12', '2024-06-13 13:06:12');
